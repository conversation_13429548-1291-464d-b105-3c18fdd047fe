package com.example.springboot.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import com.example.springboot.entity.AiChatMessage;
import com.example.springboot.entity.AiChatSession;
import com.example.springboot.service.AIChatService;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
@Controller
public class WebSocketHandler {

    @Autowired
    private AIChatService aiChatService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @MessageMapping("/chat")
    public void handleChatMessage(@RequestBody String messageJson) {
        try {
            // 解析消息
            ChatMessageRequest request = objectMapper.readValue(messageJson, ChatMessageRequest.class);
            
            // 获取会话ID
            AiChatSession session = aiChatService.getSessionByUuid(request.getSessionId());
            if (session == null) {
                throw new RuntimeException("会话不存在");
            }
            
            // 调用AI服务获取流式响应
            Flux<AiChatMessage> responseFlux = aiChatService.sendMessage(session.getId(), request.getMessage());
            
            // 订阅流并发送消息
            responseFlux.subscribe(
                message -> {
                    try {
                        String responseJson = objectMapper.writeValueAsString(message);
                        messagingTemplate.convertAndSend("/topic/chat/" + request.getSessionId(), responseJson);
                    } catch (Exception e) {
                        log.error("Error sending message: ", e);
                    }
                },
                error -> {
                    log.error("Error in chat stream: ", error);
                    try {
                        AiChatMessage errorMessage = new AiChatMessage();
                        errorMessage.setSessionId(request.getSessionId());
                        errorMessage.setRole("system");
                        errorMessage.setContent("发生错误: " + error.getMessage());
                        String errorJson = objectMapper.writeValueAsString(errorMessage);
                        messagingTemplate.convertAndSend("/topic/chat/" + request.getSessionId(), errorJson);
                    } catch (Exception e) {
                        log.error("Error sending error message: ", e);
                    }
                }
            );
        } catch (Exception e) {
            log.error("Error processing chat message: ", e);
        }
    }

    private static class ChatMessageRequest {
        private String sessionId;
        private String message;

        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}

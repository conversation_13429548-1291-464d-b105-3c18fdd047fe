package com.example.springboot.service;

import java.util.List;
import java.util.Map;

import com.example.springboot.entity.AiChatMessage;
import com.example.springboot.entity.AiChatSession;
import com.example.springboot.entity.WjxSurveyData;

import reactor.core.publisher.Flux;

public interface AIChatService {
    
    /**
     * 创建新的会话
     * @param tokenCode 代币码
     * @param surveyLink 问卷链接
     * @param excelData Excel数据
     * @return 创建的会话
     */
    AiChatSession createSession(String tokenCode, String surveyLink, List<List<String>> excelData);
    
    /**
     * 获取用户的所有会话
     * @param tokenCode 代币码
     * @return 会话列表
     */
    List<AiChatSession> getSessions(String tokenCode);
    
    /**
     * 根据ID获取会话详情
     * @param sessionId 会话ID
     * @return 会话详情
     */
    AiChatSession getSession(Long sessionId);
    
    /**
     * 获取会话详情（通过UUID）
     * @param uuid UUID
     * @return 会话详情
     */
    AiChatSession getSessionByUuid(String uuid);
    
    /**
     * 获取消息列表
     * @param sessionId 会话ID
     * @return 消息列表
     */
    List<AiChatMessage> getMessages(Long sessionId);
    
    /**
     * 发送消息到指定会话，并获取AI回复（流式）
     * @param sessionId 会话ID
     * @param message 用户消息
     * @return AI回复的消息流
     */
    Flux<AiChatMessage> sendMessage(Long sessionId, String message);
    
    /**
     * 导出Excel数据
     * @param sessionId 会话ID
     * @param data 数据
     * @return Excel文件的字节数组
     */
    byte[] exportExcel(Long sessionId, List<List<String>> data);
    
    /**
     * 导出Excel (通过UUID)
     * @param uuid UUID
     * @param data 数据
     * @return Excel文件的字节数组
     */
    byte[] exportExcelByUuid(String uuid, List<List<String>> data);
    
    /**
     * 更新Excel数据
     * @param sessionId 会话ID
     * @param data 数据
     */
    void updateExcelData(Long sessionId, List<List<String>> data);

    /**
     * 重命名会话
     * @param uuid UUID
     * @param newName 新名称
     */
    void renameSession(String uuid, String newName);

    /**
     * 删除会话（逻辑删除）
     * @param uuid UUID
     */
    void deleteSession(String uuid);

    /**
     * 获取会话历史消息
     * @param sessionId 会话ID
     * @return 会话历史消息列表
     */
    List<AiChatMessage> getSessionMessages(String sessionId);
    
    /**
     * 获取会话数据修改历史
     * @param sessionId 会话ID
     * @return 会话数据修改历史列表
     */
    List<AiChatMessage.DataModification> getSessionDataModifications(String sessionId);
    
    /**
     * 应用数据修改
     * @param sessionId 会话ID
     * @param modification 数据修改
     */
    void applyDataModification(String sessionId, AiChatMessage.DataModification modification);
    
    /**
     * 获取会话上下文（用于AI记忆）
     * @param sessionId 会话ID
     * @return 会话上下文
     */
    Map<String, Object> getSessionContext(String sessionId);
    

    
    /**
     * 获取会话的完整数据状态
     * @param sessionId 会话ID
     * @return 会话数据状态
     */
    Map<String, Object> getSessionDataState(String sessionId);

    /**
     * 保存消息
     * @param message 消息
     */
    void saveMessage(AiChatMessage message);

    /**
     * 获取最新的Excel数据
     * @param sessionId 会话ID
     * @return 最新的Excel数据
     */
    String getLatestExcelData(String sessionId);

    /**
     * 处理数据修改并更新Excel数据
     * @param sessionId 会话ID
     * @param modifications 数据修改列表
     */
    void processDataModifications(String sessionId, List<AiChatMessage.DataModification> modifications);

    /**
     * 获取会话上下文（最近的消息）
     * @param sessionId 会话ID
     * @param limit 限制数量
     * @return 会话上下文
     */
    List<AiChatMessage> getSessionContext(String sessionId, int limit);

    /**
     * 导出Excel
     * @param sessionId 会话ID
     * @return Excel文件的字节数组
     */
    byte[] exportExcel(String sessionId);


    AiChatMessage findLatestAssistantMessageWithExcel(String sessionId);

    /**
     * 获取会话的所有Excel数据版本历史
     */
    List<Map<String, Object>> getExcelVersionHistory(String sessionId);

    /**
     * 获取指定版本的Excel数据
     */
    String getExcelVersionData(String sessionId, Long messageId);

    /**
     * 恢复指定版本的数据为当前版本
     */
    void restoreExcelVersion(String sessionId, Long messageId);

    /**
     * 恢复初始版本的数据为当前版本
     */
    void restoreInitialExcelVersion(String sessionId);

    /**
     * 获取指定版本的Excel数据和修改信息
     */
    Map<String, Object> getExcelVersionDataWithModifications(String sessionId, String messageId);

    AiChatMessage getLatestAssistantMessage(String sessionId);

    /**
     * 根据会话ID获取问卷数据
     * @param sessionId 会话ID
     * @return 问卷数据
     */
    WjxSurveyData getSurveyDataBySessionId(String sessionId);
} 
package com.example.springboot.entity;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 统计分析结果数据结构
 */
public class AnalysisResult {

    @Data
    public static class RegressionResult {
        private Double rSquared;           // 决定系数
        private Double adjustedRSquared;   // 调整决定系数
        private List<Double> coefficients; // 回归系数
        private List<Double> pValues;      // p值
        private List<String> variableNames; // 变量名称
        private Double fStatistic;         // F统计量
        private Double fPValue;            // F检验p值
        private List<Double> standardErrors; // 标准误
        private List<Double> tValues;      // t值
        private Integer sampleSize;        // 样本量
        private Integer degreesOfFreedom;  // 自由度
    }

    @Data
    public static class FactorAnalysisResult {
        private Double kmoMeasure;                    // KMO测度
        private Double bartlettTestStatistic;         // Bartlett球形检验统计量
        private Double bartlettTestPValue;            // Bartlett球形检验p值
        private Map<Integer, List<Double>> factorLoadings; // 因子载荷矩阵
        private Map<Integer, Double> varianceExplained;    // 各因子解释的方差比例
        private Map<Integer, Double> cumulativeVariance;   // 累积方差解释比例
        private List<String> variableNames;           // 变量名称
        private Integer numberOfFactors;              // 提取的因子数
        private List<Double> eigenvalues;             // 特征值
        private Double totalVarianceExplained;        // 总方差解释比例
        private Integer bartlettTestDf;               // Bartlett球形检验自由度
        private List<FactorLoadingItem> factorLoadingItems; // 因子载荷和共同度列表
    }

    @Data
    public static class FactorLoadingItem {
        private String itemName;                      // 项目名称
        private Map<String, Double> loadings;         // 各因子的载荷，键为因子名称（如"因子1"）
        private Double communality;                   // 共同度

        public void setCommunalality(double communality) {
            this.communality = communality;
        }
    }

    @Data
    public static class MediationResult {
        private Double directEffect;      // 直接效应
        private Double indirectEffect;    // 间接效应
        private Double totalEffect;       // 总效应
        private Double sobelZ;            // Sobel检验Z值
        private Double sobelPValue;       // Sobel检验p值
        private Double bootstrapLowerCI;  // Bootstrap置信区间下限
        private Double bootstrapUpperCI;  // Bootstrap置信区间上限
        private String mediationType;     // 中介类型：完全中介/部分中介/无中介
        private Double proportionMediated; // 中介效应占总效应的比例
        private RegressionResult pathA;   // 路径a的回归结果（X->M）
        private RegressionResult pathB;   // 路径b的回归结果（M->Y）
        private RegressionResult pathC;   // 路径c的回归结果（X->Y）
        private RegressionResult pathCPrime; // 路径c'的回归结果（X->Y，控制M）
    }

    @Data
    public static class CorrelationResult {
        private Double correlationCoefficient; // 相关系数
        private Double pValue;                 // p值
        private Integer sampleSize;            // 样本量
        private Double confidenceIntervalLower; // 置信区间下限
        private Double confidenceIntervalUpper; // 置信区间上限
        private String significance;           // 显著性描述
    }

    @Data
    public static class ReliabilityResult {
        private Double cronbachAlpha;     // Cronbach's Alpha系数
        private Double standardizedAlpha; // 标准化Alpha系数
        private String explanation;       // 详细解读
        private List<com.example.springboot.entity.FactorAnalysisOutput.TableData> tables; // 结构化表格
        private List<String> itemNames;  // 题项名称
        private List<Double> itemMeans;  // 题项均值
        private List<Double> itemStds;   // 题项标准差
        private List<Integer> itemCounts; // 题项有效个案数
        private List<Double> itemDeletedMeans; // 删除题项后的均值
        private List<Double> itemDeletedVars;  // 删除题项后的方差
        private List<Double> itemDeletedCorrs; // 修正后的项目与总计相关性
    }
} 
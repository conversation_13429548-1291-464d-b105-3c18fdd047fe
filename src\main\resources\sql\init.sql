CREATE TABLE `activation_code` (
                                   `code_id` int NOT NULL AUTO_INCREMENT,
                                   `activation_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                   `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                   `isUsed` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                   `createtime` datetime DEFAULT NULL,
                                   `deadlinetime` datetime DEFAULT NULL,
                                   `remarks` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                                   `counts` int DEFAULT '0',
                                   PRIMARY KEY (`code_id`,`activation_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=123 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
CREATE TABLE `info` (
                        `id` int NOT NULL AUTO_INCREMENT,
                        `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                        `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
                        `city` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                        `mac` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                        `createtime` datetime DEFAULT NULL,
                        `updatetime` datetime DEFAULT NULL,
                        `lastusertime` datetime DEFAULT NULL,
                        `isnormal` int DEFAULT NULL,
                        `qq` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21500 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

CREATE TABLE `token` (
                         `id` int NOT NULL AUTO_INCREMENT,
                         `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                         `remainingUses` int NOT NULL,
                         `generatedTime` datetime NOT NULL,
                         `useTime` datetime DEFAULT NULL,
                         `out_trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                         `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                         `level` int NOT NULL,
                         `status` int NOT NULL DEFAULT '0',
                         `wenjuanLink` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '',
                         `txtcontentId` int DEFAULT NULL,
                         `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                         `remainingYFUses` int NOT NULL DEFAULT '0',
                         PRIMARY KEY (`id`,`out_trade_no`) USING BTREE,
                         UNIQUE KEY `out_trade_no` (`out_trade_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=85752 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE `tokenvault` (
                              `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                              `token_balance` decimal(10,2) NOT NULL COMMENT '代币余额',
                              `activation_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代币激活码',
                              `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                              `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                              `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `id` (`id`),
                              UNIQUE KEY `activation_code` (`activation_code`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `txtcontent` (
                              `id` bigint NOT NULL AUTO_INCREMENT,
                              `content` mediumtext COLLATE utf8mb4_general_ci NOT NULL,
                              `wenjuanlink` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                              `tokenValue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                              `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                              `downloadCount` int DEFAULT '0',
                              `fileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=897 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `wjx_order` (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                             `order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
                             `created_time` datetime NOT NULL COMMENT '创建时间',
                             `target_count` int NOT NULL COMMENT '目标份数',
                             `completed_count` int NOT NULL DEFAULT '0' COMMENT 'submitdata的完成份数',
                             `is_completed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否生成完submitdata',
                             `real_completed_count` int NOT NULL DEFAULT '0' COMMENT '真实刷完问卷的完成份数',
                             `is_real_completed` tinyint(1) NOT NULL COMMENT '是否刷完问卷',
                             `fensan_level` int DEFAULT NULL COMMENT '分散提交等级',
                             `ip_area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '换IP地区 如果是空表示不换IP',
                             `source_bili` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源比例',
                             `tianxie_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '填写用时  100,200',
                             `survey_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷链接',
                             `order_status` int DEFAULT NULL COMMENT '订单状态  -1表示不正常 正常是1,2,3,4... -1表示通用异常，-2表示比例数据有误，-3表示找不到可以用的submitdata，-4表示失败份数太高，-5表示订单付款失败，-6表示超过20次请求不到可用的token，-7表示连续5次获取代理失败,-8表示没有找到与订单号对应的脚本，-9表示任务被暂停',
                             `token_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Token令牌',
                             `js_text` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'js脚本',
                             `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                             `thread_lock` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'python生成submitdata时候使用的锁',
                             `trade_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付宝订单号',
                             `price` decimal(10,2) DEFAULT NULL COMMENT '付款金额',
                             `is_pay` tinyint DEFAULT NULL COMMENT '是否付款成功',
                             `activation_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '激活码',
                             PRIMARY KEY (`id`,`order_number`) USING BTREE,
                             UNIQUE KEY `order_number` (`order_number`)
) ENGINE=InnoDB AUTO_INCREMENT=437 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `wjx_submit_data` (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `value` text COLLATE utf8mb4_general_ci NOT NULL,
                                   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   `order_number` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
                                   `is_used` tinyint(1) NOT NULL DEFAULT '0',
                                   PRIMARY KEY (`id`),
                                   KEY `order_number_index` (`order_number`)
) ENGINE=InnoDB AUTO_INCREMENT=44865 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `wjx_token` (
                             `id` bigint NOT NULL AUTO_INCREMENT,
                             `nc_token` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
                             `nc_csessionid` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
                             `nc_sig` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
                             `submit_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                             `order_number` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
                             `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                             `expired` tinyint(1) DEFAULT '0',
                             `is_used` tinyint(1) DEFAULT '0',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=278108 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

package com.example.springboot.tool;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.example.springboot.entity.AiChatMessage;
import com.example.springboot.mapper.AIChatMessageMapper;
import com.example.springboot.service.impl.AIChatServiceImpl;

@ExtendWith(MockitoExtension.class)
class AdjustDataToolsTest {

    @Mock
    private AIChatServiceImpl aiChatService;

    @Mock
    private AIChatMessageMapper messageMapper;

    @InjectMocks
    private AdjustDataTools adjustDataTools;

    private String testSessionId = "test-session-id";

    @BeforeEach
    void setUp() {
        // 设置基本测试数据
    }

    @Test
    void testGetChatHistory_RecentMessages() {
        // 准备测试数据
        String sessionId = "test-session";
        Integer limit = 5;
        
        List<AiChatMessage> mockMessages = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            AiChatMessage msg = new AiChatMessage();
            msg.setSessionId(sessionId);
            msg.setRole(i % 2 == 0 ? "assistant" : "user");
            msg.setContent("消息内容 " + i);
            msg.setMessageOrder(i);
            msg.setCreateTime(LocalDateTime.now().minusMinutes(i));
            mockMessages.add(msg);
        }
        
        // Mock方法调用
        when(messageMapper.findRecentMessagesExcludeLast(sessionId, limit)).thenReturn(mockMessages);
        
        // 执行测试
        List<Map<String, Object>> result = adjustDataTools.getChatHistory(sessionId, limit);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.size());
        
        // 验证消息内容
        for (int i = 0; i < result.size(); i++) {
            Map<String, Object> message = result.get(i);
            assertEquals(mockMessages.get(i).getRole(), message.get("role"));
            assertEquals(mockMessages.get(i).getContent(), message.get("content"));
        }
        
        // 验证方法调用
        verify(messageMapper).findRecentMessagesExcludeLast(sessionId, limit);
    }

    @Test
    void testGetChatHistory_CompleteHistory() {
        // 准备测试数据
        String sessionId = "test-session";
        Integer limit = -1; // 获取完整历史
        
        List<AiChatMessage> mockMessages = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            AiChatMessage msg = new AiChatMessage();
            msg.setSessionId(sessionId);
            msg.setRole(i % 2 == 0 ? "assistant" : "user");
            msg.setContent("完整消息内容 " + i);
            msg.setMessageOrder(i);
            msg.setCreateTime(LocalDateTime.now().minusMinutes(i));
            mockMessages.add(msg);
        }
        
        // Mock方法调用
        when(messageMapper.findBySessionId(sessionId)).thenReturn(mockMessages);
        
        // 执行测试
        List<Map<String, Object>> result = adjustDataTools.getChatHistory(sessionId, limit);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(10, result.size());
        
        // 验证消息内容
        for (int i = 0; i < result.size(); i++) {
            Map<String, Object> message = result.get(i);
            assertEquals(mockMessages.get(i).getRole(), message.get("role"));
            assertEquals(mockMessages.get(i).getContent(), message.get("content"));
        }
        
        // 验证方法调用
        verify(messageMapper).findBySessionId(sessionId);
    }

    @Test
    void testGetChatHistory_DefaultLimit() {
        // 准备测试数据
        String sessionId = "test-session";
        Integer limit = null; // 使用默认值
        
        List<AiChatMessage> mockMessages = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            AiChatMessage msg = new AiChatMessage();
            msg.setSessionId(sessionId);
            msg.setRole(i % 2 == 0 ? "assistant" : "user");
            msg.setContent("默认消息内容 " + i);
            msg.setMessageOrder(i);
            msg.setCreateTime(LocalDateTime.now().minusMinutes(i));
            mockMessages.add(msg);
        }
        
        // Mock方法调用
        when(messageMapper.findRecentMessagesExcludeLast(sessionId, 10)).thenReturn(mockMessages);
        
        // 执行测试
        List<Map<String, Object>> result = adjustDataTools.getChatHistory(sessionId, limit);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(10, result.size());
        
        // 验证方法调用
        verify(messageMapper).findRecentMessagesExcludeLast(sessionId, 10);
    }

    @Test
    void testModerationEffectAnalysis_Center() {
        // 构造模拟数据
        String sessionId = "test-session";
        Integer dependentVar = 1;
        List<Integer> independentVars = List.of(2);
        List<Integer> moderatorVars = List.of(3);
        String centerType = "center";
        // mock getNumericColumnData
        when(aiChatService.findLatestAssistantMessageWithExcel(sessionId)).thenReturn(null); // 可根据需要mock表头
        AdjustDataTools spyTools = spy(adjustDataTools);
        doReturn(List.of(1.0, 2.0, 3.0, 4.0, 5.0)).when(spyTools).getNumericColumnData(sessionId, 0);
        doReturn(List.of(2.0, 3.0, 4.0, 5.0, 6.0)).when(spyTools).getNumericColumnData(sessionId, 1);
        doReturn(List.of(1.0, 0.0, 1.0, 0.0, 1.0)).when(spyTools).getNumericColumnData(sessionId, 2);
        doReturn(true).when(spyTools).isNumericColumn(sessionId, 1);
        doReturn(true).when(spyTools).isNumericColumn(sessionId, 2);
        doReturn("自变量").when(spyTools).getColTitle(sessionId, 2);
        doReturn("调节变量").when(spyTools).getColTitle(sessionId, 3);
        // 执行
        List result = spyTools.moderationEffectAnalysis(sessionId, dependentVar, independentVars, moderatorVars, centerType);
        assertNotNull(result);
        assertTrue(result.size() > 0);
    }
} 
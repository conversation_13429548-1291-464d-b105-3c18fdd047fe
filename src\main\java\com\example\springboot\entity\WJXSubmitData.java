package com.example.springboot.entity;

import lombok.Data;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Data
public class WJXSubmitData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String value;  // 提交的数据

    private LocalDateTime createdTime;  // 创建时间

    private String orderNumber;  // 订单号，关联订单表

    private Boolean isUsed = false;  // 是否已使用

    private LocalDateTime useTime;  // 使用时间

    @PrePersist
    protected void onCreate() {
        this.createdTime = LocalDateTime.now(); // 自动设置当前时间
    }
}

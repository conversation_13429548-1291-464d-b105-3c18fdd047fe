function searchOrder(orderNumber) {
    const orderNo = $('#orderNoInput').val().trim()?$('#orderNoInput').val().trim():orderNumber;
    if (!orderNo) {
        alert('请输入订单号');
        return;
    }

    $('#loadingOverlay').show();

    fetch(`/tokenvault/recharge-logs?orderNo=${encodeURIComponent(orderNo)}`)
        .then(response => {
            // 先统一解析响应体
            return response.json().then(data => {
                if (!response.ok) {
                    // 创建包含状态码和响应数据的错误对象
                    const error = new Error(data.message || `请求失败，状态码: ${response.status}`);
                    error.status = response.status;
                    error.data = data;
                    throw error;
                }
                return data;
            });
        })
        .then(data => {
            // 处理业务逻辑错误
            if (data.success === false) {
                throw new Error(data.message || "操作未成功");
            }
            renderResults(data);
        })
        .catch(error => {
            console.error('请求失败:', error);

            // 优先使用响应中的错误信息
            const errorMessage = error.data?.message || error.message;

            // 特殊处理限流错误（429状态码）
            if (error.status === 429) {
                alert(`⚠️ 请求过于频繁: ${errorMessage}`);
            } else {
                alert(`❌ 请求失败: ${errorMessage}`);
            }
        })
        .finally(() => {
            $('#loadingOverlay').hide();
        });
}

window.onload = function () {
    const orderNumber = getQueryParam('ordernum');
    console.log('当前订单号：', orderNumber)
    if (orderNumber) {
        document.getElementById('orderNoInput').value = orderNumber;
        searchOrder(orderNumber); // 显示加载旋转器
    }
};
function getQueryParam(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}
function renderResults(data) {
    const tbody = $('#resultBody').empty();
    const table = $('#resultTable');
    const noResult = $('#noResult');

    if (data.length > 0) {
        data.forEach(log => {
            const row = `
                <tr>
                    <td class="font-monospace">${log.activationCode || '-'}</td>
                    <td class="text-nowrap">¥${log.rechargeAmount?.toFixed(2) || '0.00'}</td>
                    <td class="text-nowrap">${formatDateTime(log.createdAt)}</td>
                    <td class="text-break">${log.remark || '-'}</td>
                     <td>
                        <button class="btn btn-sm btn-outline-secondary copy-btn"
                                onclick="copyCode('${log.activationCode}')">
                            复制代币码
                        </button>
                    </td>
                </tr>`;
            tbody.append(row);
        });
        table.show();
        noResult.hide();
    } else {
        table.hide();
        noResult.show();
    }
}

function formatDateTime(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return isNaN(date) ? '-' :
        `${date.getFullYear()}-${pad(date.getMonth()+1)}-${pad(date.getDate())} ` +
        `${pad(date.getHours())}:${pad(date.getMinutes())}`;
}

function pad(num) {
    return num.toString().padStart(2, '0');
}


function copyCode(code) {
    if (!code || code === '未知') {
        alert('无效的代币码');
        return;
    }

    navigator.clipboard.writeText(code).then(() => {
        const buttons = document.getElementsByClassName('copy-btn');
        Array.from(buttons).forEach(btn => {
            if (btn.textContent.includes(code)) {
                const originalText = btn.innerHTML;
                btn.innerHTML = '✓ 已复制';
                btn.classList.add('text-success');
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('text-success');
                }, 1500);
            }
        });
        alert('复制成功！'); // 新增alert提示
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择复制');
    });
}

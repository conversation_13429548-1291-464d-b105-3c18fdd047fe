2025-07-05 16:04:05.868 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-05 16:04:05.980 INFO  [restartedMain] c.e.s.SpringbootApplication - Starting SpringbootApplication using Java 17.0.6 with PID 23496 (D:\cursorProjects\MyWenJuanXing_Management2\target\classes started by ZhangYangyang in D:\cursorProjects\MyWenJuanXing_Management2)
2025-07-05 16:04:05.982 DEBUG [restartedMain] c.e.s.SpringbootApplication - Running with Spring Boot v3.4.3, Spring v6.2.3
2025-07-05 16:04:05.984 INFO  [restartedMain] c.e.s.SpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-05 16:04:06.422 INFO  [restartedMain] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-05 16:04:06.422 INFO  [restartedMain] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-05 16:04:07.930 INFO  [restartedMain] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-05 16:04:08.166 INFO  [restartedMain] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 220 ms. Found 1 JPA repository interface.
2025-07-05 16:04:10.148 INFO  [restartedMain] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 9090 (http)
2025-07-05 16:04:10.193 INFO  [restartedMain] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
2025-07-05 16:04:10.198 INFO  [restartedMain] o.a.c.c.StandardService - Starting service [Tomcat]
2025-07-05 16:04:10.199 INFO  [restartedMain] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-07-05 16:04:10.354 INFO  [restartedMain] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-07-05 16:04:10.354 INFO  [restartedMain] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3930 ms
2025-07-05 16:04:10.696 INFO  [restartedMain] c.z.h.HikariDataSource - HikariPool-1 - Starting...
2025-07-05 16:04:11.492 INFO  [restartedMain] c.z.h.p.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1bc831b8
2025-07-05 16:04:11.496 INFO  [restartedMain] c.z.h.HikariDataSource - HikariPool-1 - Start completed.
2025-07-05 16:04:11.836 INFO  [restartedMain] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-05 16:04:12.076 INFO  [restartedMain] o.h.Version - HHH000412: Hibernate ORM core version 6.6.8.Final
2025-07-05 16:04:12.162 INFO  [restartedMain] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-05 16:04:12.395 INFO  [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-05 16:04:12.570 INFO  [restartedMain] o.h.o.c.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.36
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-05 16:04:13.478 INFO  [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-05 16:04:13.481 INFO  [restartedMain] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-05 16:04:16.206 WARN  [restartedMain] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-05 16:04:16.421 INFO  [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-05 16:04:17.921 INFO  [restartedMain] o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-05 16:04:18.025 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-05 16:04:18.027 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7c40fdd2]]
2025-07-05 16:04:18.029 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-05 16:04:18.030 INFO  [restartedMain] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
2025-07-05 16:04:18.048 INFO  [restartedMain] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 9090 (http) with context path '/'
2025-07-05 16:04:18.065 INFO  [restartedMain] c.e.s.SpringbootApplication - Started SpringbootApplication in 12.727 seconds (process running for 14.03)
2025-07-05 16:04:51.133 INFO  [http-nio-9090-exec-1] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-05 16:04:51.134 INFO  [http-nio-9090-exec-1] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-05 16:04:51.136 INFO  [http-nio-9090-exec-1] o.s.w.s.DispatcherServlet - Completed initialization in 1 ms
2025-07-05 16:04:53.134 DEBUG [http-nio-9090-exec-7] c.e.s.m.T.findByTokenCode - ==>  Preparing: SELECT * FROM tokenvault WHERE activation_code = ?
2025-07-05 16:04:53.146 DEBUG [http-nio-9090-exec-7] c.e.s.m.T.findByTokenCode - ==> Parameters: TESTCODE12345(String)
2025-07-05 16:04:53.184 DEBUG [http-nio-9090-exec-7] c.e.s.m.T.findByTokenCode - <==      Total: 1
2025-07-05 16:04:53.186 DEBUG [http-nio-9090-exec-7] c.e.s.m.T.findByTokenCode - ==>  Preparing: SELECT * FROM tokenvault WHERE activation_code = ?
2025-07-05 16:04:53.188 DEBUG [http-nio-9090-exec-7] c.e.s.m.T.findByTokenCode - ==> Parameters: TESTCODE12345(String)
2025-07-05 16:04:53.211 DEBUG [http-nio-9090-exec-7] c.e.s.m.T.findByTokenCode - <==      Total: 1
2025-07-05 16:04:53.212 DEBUG [http-nio-9090-exec-7] c.e.s.m.A.getTotalTokenConsumedByTokenCode - ==>  Preparing: SELECT COALESCE(SUM(token_consumed), 0) FROM ai_chat_session WHERE token_code = ? AND is_deleted = 0
2025-07-05 16:04:53.213 DEBUG [http-nio-9090-exec-7] c.e.s.m.A.getTotalTokenConsumedByTokenCode - ==> Parameters: TESTCODE12345(String)
2025-07-05 16:04:53.232 DEBUG [http-nio-9090-exec-7] c.e.s.m.A.getTotalTokenConsumedByTokenCode - <==      Total: 1
2025-07-05 16:04:53.265 INFO  [http-nio-9090-exec-8] c.e.s.c.AiChatController - Getting sessions for token code: TESTCODE12345
2025-07-05 16:04:53.266 DEBUG [http-nio-9090-exec-8] c.e.s.m.A.findByTokenCode - ==>  Preparing: SELECT s.*, (SELECT MAX(create_time) FROM ai_chat_message WHERE session_id = s.uuid) as lastMessageTime FROM ai_chat_session s WHERE s.token_code = ? AND s.is_deleted = 0 ORDER BY lastMessageTime DESC, s.create_time DESC
2025-07-05 16:04:53.267 DEBUG [http-nio-9090-exec-8] c.e.s.m.A.findByTokenCode - ==> Parameters: TESTCODE12345(String)
2025-07-05 16:04:53.337 DEBUG [http-nio-9090-exec-8] c.e.s.m.A.findByTokenCode - <==      Total: 7
2025-07-05 16:04:53.338 INFO  [http-nio-9090-exec-8] c.e.s.c.AiChatController - Found 7 sessions for token code: TESTCODE12345
2025-07-05 16:04:54.212 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findByUuid - ==>  Preparing: SELECT * FROM ai_chat_session WHERE uuid = ? AND is_deleted = 0
2025-07-05 16:04:54.213 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findByUuid - ==> Parameters: ************************************(String)
2025-07-05 16:04:54.234 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findByUuid - <==      Total: 1
2025-07-05 16:04:54.235 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findLatestBySessionId - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? AND complete_excel_data IS NOT NULL AND complete_excel_data != '' ORDER BY message_order DESC LIMIT 1
2025-07-05 16:04:54.236 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findLatestBySessionId - ==> Parameters: ************************************(String)
2025-07-05 16:04:54.256 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findLatestBySessionId - <==      Total: 1
2025-07-05 16:04:54.258 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findLatestBySessionId - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? AND complete_excel_data IS NOT NULL AND complete_excel_data != '' ORDER BY message_order DESC LIMIT 1
2025-07-05 16:04:54.258 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findLatestBySessionId - ==> Parameters: ************************************(String)
2025-07-05 16:04:54.280 DEBUG [http-nio-9090-exec-9] c.e.s.m.A.findLatestBySessionId - <==      Total: 1
2025-07-05 16:04:54.362 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findByUuid - ==>  Preparing: SELECT * FROM ai_chat_session WHERE uuid = ? AND is_deleted = 0
2025-07-05 16:04:54.363 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findByUuid - ==> Parameters: ************************************(String)
2025-07-05 16:04:54.382 DEBUG [http-nio-9090-exec-1] c.e.s.m.A.findBySessionIdAndHasModifications - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? AND data_modifications IS NOT NULL AND TRIM(data_modifications) != '' AND TRIM(data_modifications) != '[]' AND complete_excel_data IS NOT NULL ORDER BY create_time DESC
2025-07-05 16:04:54.382 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findByUuid - <==      Total: 1
2025-07-05 16:04:54.382 DEBUG [http-nio-9090-exec-1] c.e.s.m.A.findBySessionIdAndHasModifications - ==> Parameters: ************************************(String)
2025-07-05 16:04:54.383 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findLatestBySessionId - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? AND complete_excel_data IS NOT NULL AND complete_excel_data != '' ORDER BY message_order DESC LIMIT 1
2025-07-05 16:04:54.384 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findLatestBySessionId - ==> Parameters: ************************************(String)
2025-07-05 16:04:54.404 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findLatestBySessionId - <==      Total: 1
2025-07-05 16:04:54.406 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findById - ==>  Preparing: SELECT * FROM ai_chat_session WHERE id = ? AND is_deleted = 0
2025-07-05 16:04:54.407 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findById - ==> Parameters: 29(Long)
2025-07-05 16:04:54.428 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findById - <==      Total: 1
2025-07-05 16:04:54.431 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findBySessionId - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? ORDER BY message_order ASC
2025-07-05 16:04:54.431 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findBySessionId - ==> Parameters: ************************************(String)
2025-07-05 16:04:54.457 DEBUG [http-nio-9090-exec-1] c.e.s.m.A.findBySessionIdAndHasModifications - <==      Total: 46
2025-07-05 16:04:54.518 DEBUG [http-nio-9090-exec-10] c.e.s.m.A.findBySessionId - <==      Total: 139
2025-07-05 16:05:06.018 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findByUuid - ==>  Preparing: SELECT * FROM ai_chat_session WHERE uuid = ? AND is_deleted = 0
2025-07-05 16:05:06.019 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findByUuid - ==> Parameters: ************************************(String)
2025-07-05 16:05:06.042 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findByUuid - <==      Total: 1
2025-07-05 16:05:06.043 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findLatestBySessionId - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? AND complete_excel_data IS NOT NULL AND complete_excel_data != '' ORDER BY message_order DESC LIMIT 1
2025-07-05 16:05:06.045 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findLatestBySessionId - ==> Parameters: ************************************(String)
2025-07-05 16:05:06.068 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findLatestBySessionId - <==      Total: 1
2025-07-05 16:05:06.070 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findLatestBySessionId - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? AND complete_excel_data IS NOT NULL AND complete_excel_data != '' ORDER BY message_order DESC LIMIT 1
2025-07-05 16:05:06.071 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findLatestBySessionId - ==> Parameters: ************************************(String)
2025-07-05 16:05:06.092 DEBUG [http-nio-9090-exec-3] c.e.s.m.A.findLatestBySessionId - <==      Total: 1
2025-07-05 16:05:06.160 DEBUG [http-nio-9090-exec-4] c.e.s.m.A.findBySessionIdAndHasModifications - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? AND data_modifications IS NOT NULL AND TRIM(data_modifications) != '' AND TRIM(data_modifications) != '[]' AND complete_excel_data IS NOT NULL ORDER BY create_time DESC
2025-07-05 16:05:06.161 DEBUG [http-nio-9090-exec-4] c.e.s.m.A.findBySessionIdAndHasModifications - ==> Parameters: ************************************(String)
2025-07-05 16:05:06.178 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findByUuid - ==>  Preparing: SELECT * FROM ai_chat_session WHERE uuid = ? AND is_deleted = 0
2025-07-05 16:05:06.180 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findByUuid - ==> Parameters: ************************************(String)
2025-07-05 16:05:06.181 DEBUG [http-nio-9090-exec-4] c.e.s.m.A.findBySessionIdAndHasModifications - <==      Total: 0
2025-07-05 16:05:06.199 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findByUuid - <==      Total: 1
2025-07-05 16:05:06.200 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findLatestBySessionId - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? AND complete_excel_data IS NOT NULL AND complete_excel_data != '' ORDER BY message_order DESC LIMIT 1
2025-07-05 16:05:06.200 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findLatestBySessionId - ==> Parameters: ************************************(String)
2025-07-05 16:05:06.243 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findLatestBySessionId - <==      Total: 1
2025-07-05 16:05:06.244 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findById - ==>  Preparing: SELECT * FROM ai_chat_session WHERE id = ? AND is_deleted = 0
2025-07-05 16:05:06.245 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findById - ==> Parameters: 34(Long)
2025-07-05 16:05:06.265 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findById - <==      Total: 1
2025-07-05 16:05:06.265 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findBySessionId - ==>  Preparing: SELECT * FROM ai_chat_message WHERE session_id = ? ORDER BY message_order ASC
2025-07-05 16:05:06.266 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findBySessionId - ==> Parameters: ************************************(String)
2025-07-05 16:05:06.333 DEBUG [http-nio-9090-exec-2] c.e.s.m.A.findBySessionId - <==      Total: 24
2025-07-05 16:05:18.024 INFO  [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 1, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 11, active threads = 1, queued tasks = 2, completed tasks = 7]
2025-07-05 16:35:18.028 INFO  [MessageBroker-5] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 2, completed tasks = 440]
2025-07-05 17:05:18.030 INFO  [MessageBroker-15] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 2, completed tasks = 872]
2025-07-05 17:35:20.353 INFO  [MessageBroker-6] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 2, completed tasks = 1305]
2025-07-05 18:05:20.363 INFO  [MessageBroker-9] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 2, completed tasks = 1738]
2025-07-05 18:35:20.387 INFO  [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 2, completed tasks = 2171]
2025-07-05 19:05:20.399 INFO  [MessageBroker-11] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 2, completed tasks = 2604]
2025-07-05 19:35:20.416 INFO  [MessageBroker-12] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 2, completed tasks = 3037]
2025-07-05 20:05:20.427 INFO  [MessageBroker-7] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[1 current WS(1)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 1], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 2, completed tasks = 3470]
2025-07-05 20:35:20.439 INFO  [MessageBroker-13] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 6], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 2], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 1, completed tasks = 3884]
2025-07-05 21:05:20.449 INFO  [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 6], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 2], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 1, completed tasks = 4245]
2025-07-05 21:35:20.483 INFO  [MessageBroker-15] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 6], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 2], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 1, completed tasks = 4606]
2025-07-05 22:05:20.497 INFO  [MessageBroker-11] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 6], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 2], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 1, completed tasks = 4967]
2025-07-05 22:35:20.502 INFO  [MessageBroker-12] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 6], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 2], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 1, completed tasks = 5328]
2025-07-05 23:05:20.513 INFO  [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 6], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 2], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 1, completed tasks = 5689]
2025-07-05 23:35:20.519 INFO  [MessageBroker-12] o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 1 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(1)-CONNECTED(1)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 6], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 2], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 1, completed tasks = 6050]

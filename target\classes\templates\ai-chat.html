<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI数据分析助手</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <!-- <link href="https://cdn.jsdelivr.net/npm/handsontable/dist/handsontable.full.min.css" rel="stylesheet"> -->
    <link href="https://cdn.jsdelivr.net/npm/handsontable@12.0.0/dist/handsontable.full.min.css" rel="stylesheet">
    <!-- <link href="css//handsontable.full.min.css" rel="stylesheet"> -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet"> -->
    <link href="css//bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/11.6.0/styles/default.min.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/11.6.0/highlight.min.js"></script>

    <link rel="stylesheet" href="css/ai-chat.css">
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
</head>

<body>
    <div class="main-container">
        <!-- 左侧会话面板 -->
        <div class="session-panel" id="sessionPanel">
            <div class="session-header">
                <div class="d-flex align-items-center">
                    <img src="imgs/logo.png" alt="Logo" style="width: 30px; margin-right: 10px;">
                    <h5 class="mb-0">易风AI数据分析</h5>
                </div>
                <button class="toggle-btn" onclick="toggleSessionPanel()">
                    <i class="bi bi-chevron-left"></i>
                </button>
                <button class="search-btn" onclick="toggleSearchInput()"
                    style="background: none; border: none; padding: 0; cursor: pointer; color: #666;">
                    <i class="bi bi-search"></i>
                </button>
            </div>
            <div class="session-search" id="sessionSearchInput">
                <input type="text" id="sessionSearch" placeholder="搜索会话名称或链接">
            </div>
            <button class="new-chat-btn" onclick="createNewChat()">
                <i class="bi bi-plus-lg"></i>
                新建对话
            </button>
            <div class="session-list" id="sessionList">
                <!-- 会话列表将通过JavaScript动态添加 -->
            </div>
        </div>

        <!-- 中间数据面板 -->
        <div class="data-panel">
            <div class="data-header">
                <div class="data-header-top-row" style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
                    <div style="flex:1 1 auto;min-width:0;display:flex;align-items:center;gap:12px;flex-wrap:wrap;">
                        <div class="version-control" style="display: none; max-width:400px; flex-shrink:0;">
                            <select id="versionSelect" class="form-select form-select-sm">
                                <option value="">暂无历史版本</option>
                            </select>
                            <button id="restoreVersionBtn" class="btn btn-sm btn-outline-primary" onclick="restoreVersion()" disabled>
                                恢复到此版本
                            </button>
                            <button id="switchToCurrentBtn" class="btn btn-sm btn-outline-secondary" onclick="exitPreviewVersion()" style="display: none;">
                                切换为目前版本
                            </button>
                        </div>
                    </div>
                    <div class="top-cards-right">
                        <div class="token-info-card">
                          <span class="token-info-icon"><i class="bi bi-qr-code"></i></span>
                          <div class="token-info-main">
                            <span class="token-info-title">代币码</span>
                            <span class="token-info-value" id="displayTokenCode">未输入</span>
                          </div>
                        </div>
                        <div class="token-info-card">
                          <span class="token-info-icon"><i class="bi bi-wallet2"></i></span>
                          <div class="token-info-main">
                            <span class="token-info-title">当前余额</span>
                            <span class="token-info-value token-info-value-green" id="tokenCount">0</span>
                            <span class="token-info-title" style="font-size:13px;">代币</span>
                          </div>
                        </div>
                        <div class="token-info-card">
                          <span class="token-info-icon"><i class="bi bi-cash-coin"></i></span>
                          <div class="token-info-main">
                            <span class="token-info-title">总消费</span>
                            <span class="token-info-value token-info-value-black" id="totalTokenConsumed">0</span>
                            <span class="token-info-title" style="font-size:13px;">代币</span>
                          </div>
                        </div>
                    </div>
                    <button id="logoutBtnHeader" class="btn btn-sm btn-outline-danger" style="margin-left:8px;vertical-align:middle;" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> 退出登录
                    </button>
                </div>
            </div>
            <div class="toolbar" id="toolbar">
                <div class="toolbar-left">
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <div class="token-info-card" id="surveyLinkContainer" style="display: none;">
                          <span class="token-info-icon"><i class="bi bi-link-45deg"></i></span>
                          <div class="token-info-main">
                            <span class="token-info-title">问卷链接</span>
                            <a href="#" id="surveyLinkShort" class="token-info-link" target="_blank" style="font-weight:bold;">问卷</a>
                          </div>
                        </div>
                        
                        <div class="token-info-card" id="sessionCostCard" style="display: none;">
                          <span class="token-info-icon"><i class="bi bi-cash-coin"></i></span>
                          <div class="token-info-main">
                            <span class="token-info-title">本会话消费</span>
                            <span class="nowrap"><span class="token-info-value token-info-value-black" id="currentSessionTokenConsumed">0</span> <span class="token-info-title" style="font-size:13px;">代币</span></span>
                          </div>
                        </div>
                    </div>
                </div>
                <div class="toolbar-right">
                    <button class="btn btn-success btn-sm" id="analysisBtn" data-bs-toggle="modal" data-bs-target="#analysisParamModal" style="margin-right:6px;">
                        <i class="bi bi-bar-chart"></i> 数据分析
                    </button>
                    <button class="btn btn-warning btn-sm" id="adjustmentBtn" data-bs-toggle="modal" data-bs-target="#adjustmentParamModal" style="margin-right:6px;">
                        <i class="bi bi-tools"></i> 数据调整
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleAnswerDisplay()" id="toggleAnswerBtn"
                        style="display: none; margin-right:6px;">
                        <i class="bi bi-arrow-repeat"></i> 切换答案显示
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="exportExcel()" id="exportBtn" style="margin-right:6px;">
                        <i class="bi bi-download"></i> 导出Excel
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="toggleFullscreen()" id="fullscreenBtn" style="margin-right:6px;">
                        <i class="bi bi-arrows-fullscreen"></i> 全屏预览
                    </button>
                    <button id="logoutBtn" class="btn btn-sm btn-outline-danger"  onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> 退出登录
                    </button>
                </div>
            </div>
            <div class="excel-container">
                <div class="fullscreen-header">
                    <div class="fullscreen-controls">
                        <button class="btn btn-outline-secondary fullscreen-btn" onclick="toggleAnswerDisplay()"
                            id="fullscreenToggleAnswerBtn" style="display: none;">
                            <i class="bi bi-arrow-repeat"></i> 切换答案显示
                        </button>
                        <button class="btn btn-outline-primary" onclick="exportExcel()">
                            <i class="bi bi-download"></i> 导出Excel
                        </button>
                        <!-- 缩放按钮 -->
                        <button class="btn btn-outline-primary fullscreen-btn" onclick="zoomOutTable()">
                            <i class="bi bi-zoom-out"></i> 缩小
                        </button>
                        <button class="btn btn-outline-primary fullscreen-btn" onclick="zoomInTable()">
                            <i class="bi bi-zoom-in"></i> 放大
                        </button>
                        <button class="btn btn-primary fullscreen-btn" onclick="toggleFullscreen()">
                            <i class="bi bi-arrows-angle-contract"></i> 退出全屏
                        </button>
                    </div>
                </div>
                <div id="previewTip" class="preview-tip" style="display:none;">当前浏览的是历史版本，仅供查看，点击"退出预览"或切换回当前版本可恢复</div>
                <button id="exitPreviewBtn" class="btn btn-warning btn-sm"
                    style="display:none;position:absolute;top:10px;right:20px;z-index:10;"
                    onclick="exitPreviewVersion()">退出预览</button>
                <div class="upload-container" id="uploadContainer">
                    <div id="tokenInputSection" class="auth-section">
                        <div class="auth-header">
                            <div class="auth-icon">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <h3 class="auth-title">身份验证</h3>
                            <p class="auth-subtitle">请输入您的代币码以开始使用</p>
                        </div>
                        <div class="auth-form">
                            <div class="input-group-modern">
                                <div class="input-icon">
                                    <i class="bi bi-key"></i>
                                </div>
                                <input type="text" class="form-control-modern" id="tokenCode" placeholder="请输入代币码">
                            </div>
                            <button class="btn-modern btn-primary-modern" onclick="validateToken()">
                                <span class="btn-text">验证代币</span>
                                <i class="bi bi-arrow-right btn-icon"></i>
                            </button>
                        </div>
                    </div>
                    <div id="uploadSection" class="upload-section" style="display: none;">
                        <div class="upload-header">
                            <div class="upload-icon">
                                <i class="bi bi-cloud-upload"></i>
                            </div>
                            <h3 class="upload-title">创建新会话</h3>
                            <p class="upload-subtitle">上传Excel文件并输入问卷链接开始分析</p>
                        </div>
                        <div class="upload-form">
                            <div class="form-group-modern">
                                <label class="form-label-modern">
                                    <i class="bi bi-link-45deg"></i>
                                    问卷链接
                                </label>
                                <input type="text" class="form-control-modern" id="surveyLink" placeholder="请输入问卷链接">
                            </div>
                            <div class="form-group-modern">
                                <label class="form-label-modern">
                                    <i class="bi bi-file-earmark-excel"></i>
                                    Excel文件
                                </label>
                                <div class="file-upload-area" id="fileUploadArea">
                                    <input type="file" class="file-input-hidden" id="excelFile" accept=".xlsx,.xls">
                                    <div class="file-upload-content">
                                        <i class="bi bi-cloud-upload file-upload-icon"></i>
                                        <p class="file-upload-text">点击选择文件或拖拽文件到此处</p>
                                        <p class="file-upload-hint">支持 .xlsx, .xls 格式</p>
                                    </div>
                                </div>
                            </div>
                            <button class="btn-modern btn-success-modern upload-btn" onclick="uploadAndParse()">
                                <span class="btn-text">开始分析</span>
                                <i class="bi bi-play-fill btn-icon"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div id="excelTable" class="hot-table"></div>
                <!-- 分析参数选择弹窗 -->
                <div class="modal fade" id="analysisParamModal" tabindex="-1">
                  <div class="modal-dialog modal-fullscreen">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title">数据分析参数设置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                      </div>
                      <div class="modal-body">
                        <form id="analysisParamForm" style="height:100%;display:flex;flex-direction:column;min-height:0;min-width:0;">
                          <div style="flex:1 1 0;display:flex;flex-direction:row;gap:24px;min-height:0;min-width:0;">
                            <div class="mb-3" style="width: 140px; min-width: 0;">
                              <label for="analysisType" class="form-label">分析类型</label>
                              <select class="form-select" id="analysisType" name="analysisType">
                                <option value="cronbach">信度分析</option>
                                <option value="factor">效度分析</option>
                                <option value="frequency">频数统计</option>
                                <option value="desc">描述性统计</option>
                                <option value="correlation">相关分析</option>
                                <option value="regression">线性回归</option>
                                <option value="anova">方差分析</option>
                                <option value="independentTTest">独立样本t检验</option>
                                <option value="oneSampleTTest">单样本t检验</option>
                                <option value="moderation">调节效应</option>
                                <option value="mediation">中介效应</option>
                                <option value="crossTab">交叉(卡方)分析</option>
                              </select>
                            </div>
                            <!-- 动态参数区域 -->
                            <div id="analysisDynamicParams" style="flex:2 1 0;min-width:0;"></div>
                          </div>
                        </form>
                      </div>
                      <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="runAnalysisBtn">开始分析</button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 数据调整参数选择弹窗 -->
                <div class="modal fade" id="adjustmentParamModal" tabindex="-1">
                  <div class="modal-dialog modal-fullscreen">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title">数据调整参数设置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                      </div>
                      <div class="modal-body">
                        <form id="adjustmentParamForm" style="height:100%;display:flex;flex-direction:column;min-height:0;min-width:0;">
                          <div style="flex:1 1 0;display:flex;flex-direction:row;gap:24px;min-height:0;min-width:0;">
                            <div class="mb-3" style="width: 200px; min-width: 0;">
                              <label for="adjustmentType" class="form-label">调整类型</label>
                              <select class="form-select" id="adjustmentType" name="adjustmentType">
                                <option value="multiDimensionalScale">分维度调整量表</option>
                              </select>
                            </div>
                            <!-- 动态参数区域 -->
                            <div id="adjustmentDynamicParams" style="flex:2 1 0;min-width:0;"></div>
                          </div>
                        </form>
                      </div>
                      <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-warning" id="runAdjustmentBtn">开始调整</button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 分析结果展示区域 -->
            </div>
        </div>

        <!-- 右侧聊天面板 -->
        <div class="chat-panel">
            <div class="chat-header">
                <h5 class="mb-0">AI助手</h5>
            </div>
            <div class="message-list" id="messageList">
                <!-- 消息列表将通过JavaScript动态添加 -->
            </div>
            <div class="chat-input">
                <div class="input-group">
                    <div class="form-control" id="messageInput" contenteditable="true"
                        style="min-height:80px;max-height:180px;overflow-y:auto;outline:none;" placeholder="请输入您的需求...">
                    </div>
                    <button class="btn btn-primary" onclick="sendMessage()">发送</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析结果模态框 -->
    <div class="modal fade analysis-modal" id="analysisModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">分析结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body analysis-result" id="analysisResult">
                    <!-- 分析结果将通过JavaScript动态添加 -->
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/handsontable@12.0.0/dist/handsontable.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.5.0/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="js/ai-chat.js"></script>
    <style>
        .selected-range-table {
            border-collapse: collapse;
            margin: 8px 0;
            background: #f8f9fa;
        }

        .selected-range-table td {
            border: 1px solid #ccc;
            padding: 2px 6px;
            font-size: 13px;
            min-width: 32px;
            text-align: center;
        }

        .message.selected-range {
            background: #e9f7ef;
            border-left: 4px solid #2ecc71;
            margin: 8px 0;
            padding: 6px 10px;
            border-radius: 4px;
        }

        .remove-selected-range {
            background: #fff;
            border: 1px solid #bbb;
            color: #888;
            border-radius: 3px;
            font-size: 12px;
            padding: 0 6px;
            margin-left: 8px;
            cursor: pointer;
        }

        .remove-selected-range:hover {
            background: #f8d7da;
            color: #c00;
            border-color: #c00;
        }

        /* 选区标签样式 */
        .range-tag {
            display: inline-block;
            background: #e9f7ef;
            border: 1px solid #2ecc71;
            border-radius: 3px;
            padding: 0 6px;
            margin: 0 2px;
            color: #2ecc71;
            font-size: 13px;
            cursor: pointer;
            user-select: none;
        }

        .range-tag .del-btn {
            color: #c00;
            margin-left: 4px;
            cursor: pointer;
            font-style: normal;
        }

        /* 悬浮添加按钮样式 */
        .add-range-btn {
            background: #fff;
            border: 1px solid #2ecc71;
            color: #2ecc71;
            border-radius: 4px;
            font-size: 13px;
            padding: 2px 10px;
            box-shadow: 0 2px 8px rgba(44, 204, 113, 0.08);
            cursor: pointer;
            transition: background 0.2s;
        }

        .add-range-btn:hover {
            background: #e9f7ef;
        }

        /* Handsontable表头行样式 */
        .htCore .table-header-row {
            background: #e3f0fa !important;
            font-weight: bold;
        }

        .version-control {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 8px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .version-control select {
            min-width: 200px;
        }

        .version-control button {
            white-space: nowrap;
        }

        .version-control button.previewing {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #000;
        }

        .version-control button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .version-info {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }

        .version-tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 5px;
        }

        .version-tag.current {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .version-tag.preview {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .toolbar-left {
            display: flex;
            gap: 8px;
            /* Space between buttons */
            align-items: center;
            flex-wrap: wrap;
            /* 允许按钮自动换行 */
        }

        .toolbar-right {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: flex-end;
            max-width: 100%;
        }

        /* 网格布局基础样式 */
        .toolbar-right.grid-layout {
            display: grid;
            grid-gap: 8px;
            justify-content: end;
            align-items: center;
        }

        /* 默认3列网格（大屏幕） */
        .toolbar-right.grid-layout:not(.grid-2col):not(.grid-1col) {
            grid-template-columns: repeat(3, 1fr);
            max-width: 600px;
        }

        /* 2列网格（中等屏幕） */
        .toolbar-right.grid-layout.grid-2col {
            grid-template-columns: repeat(2, 1fr);
            max-width: 400px;
        }

        /* 1列网格（小屏幕） */
        .toolbar-right.grid-layout.grid-1col {
            grid-template-columns: 1fr;
            max-width: 200px;
        }

        /* 按钮在网格布局中的样式 */
        .toolbar-right.grid-layout .btn {
            width: 100%;
            white-space: nowrap;
            justify-self: stretch;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        /* 确保所有工具栏按钮的图标和文字居中 */
        .toolbar-right .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: 4px; /* 图标和文字之间的间距 */
        }

        .toolbar-right .btn i {
            flex-shrink: 0; /* 防止图标被压缩 */
        }

        /* 分析参数弹窗全屏自适应宽度和内容区flex布局（彻底自适应） */
        #analysisParamModal .modal-dialog.modal-fullscreen {
            max-width: 100vw;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
        }
        #analysisParamModal .modal-content {
            height: 100vh;
            border-radius: 0;
            display: flex;
            flex-direction: column;
        }
        #analysisParamModal .modal-body {
            flex: 1 1 0;
            height: auto;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        /* 数据调整参数弹窗样式 */
        #adjustmentParamModal .modal-dialog.modal-fullscreen {
            max-width: 100vw;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
        }
        #adjustmentParamModal .modal-content {
            height: 100vh;
            border-radius: 0;
            display: flex;
            flex-direction: column;
        }
        #adjustmentParamModal .modal-body {
            flex: 1 1 0;
            height: auto;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        /* 数据调整相关样式 */
        .dimension-item {
            transition: all 0.3s ease;
        }

        .dimension-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }



        .question-checkbox:checked + label {
            background-color: #e3f2fd;
            border-radius: 4px;
            padding: 2px 6px;
        }

        .selected-questions .badge {
            font-size: 0.8em;
            padding: 6px 10px;
        }

        .question-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .question-details table {
            font-size: 0.9em;
        }

        .question-details .table th {
            background-color: #e9ecef;
            font-weight: 600;
            border-top: none;
        }

        .form-check-label {
            cursor: pointer;
            user-select: none;
        }

        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .card.border-primary {
            border-width: 2px !important;
        }

        .card-header.bg-light {
            background-color: #f8f9fa !important;
            border-bottom: 2px solid #0d6efd;
        }

        /* 加载动效样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(3px);
        }

        .loading-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            text-align: center;
            animation: fadeInScale 0.3s ease-out;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .loading-text {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }

        .loading-subtext {
            font-size: 14px;
            color: #666;
            margin: 0;
        }

        .loading-dots {
            display: inline-block;
            animation: loadingDots 1.5s infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeInScale {
            0% {
                opacity: 0;
                transform: scale(0.8);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes fadeOut {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(0.9);
            }
        }

        @keyframes loadingDots {
            0%, 20% {
                color: rgba(0, 0, 0, 0);
                text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                            0.5em 0 0 rgba(0, 0, 0, 0);
            }
            40% {
                color: #666;
                text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                            0.5em 0 0 rgba(0, 0, 0, 0);
            }
            60% {
                text-shadow: 0.25em 0 0 #666,
                            0.5em 0 0 rgba(0, 0, 0, 0);
            }
            80%, 100% {
                text-shadow: 0.25em 0 0 #666,
                            0.5em 0 0 #666;
            }
        }
        #analysisParamForm > div {
            flex: 1 1 0;
            display: flex;
            flex-direction: row;
            gap: 24px;
            min-height: 0;
        }
        .transfer-container {
            flex: 1 1 0;
            display: flex;
            flex-direction: row;
            gap: 24px;
            min-height: 0;
        }
        .transfer-list {
            flex: 1 1 0;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }
        .transfer-list ul {
            flex: 1 1 0;
            min-height: 0;
            max-height: none;
        }
    </style>
    <script>
        // 你可以直接调用 sendMessage()，它已支持流式SSE聊天。
        // 只需保证 currentSessionId 已设置，输入消息后点击发送即可。
        // sendMessage() 内部会自动用 fetch+流式解析处理后端SSE响应。
        // 如果你想自定义流式处理，可参考 ai-chat.js 的 sendMessage 和 handleStream 实现。
    </script>
</body>

</html>
package com.example.springboot.tool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.commons.math3.linear.Array2DRowRealMatrix;
import org.apache.commons.math3.linear.LUDecomposition;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.linear.RealVector;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import com.example.springboot.tool.ReverseDataAdjustmentTools.AdjustmentResult;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 逆向数据调整工具类扩展
 * 包含回归分析、方差分析、T检验等调整功能
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ReverseDataAdjustmentToolsExtension {

    private final ReverseDataAdjustmentTools baseTools;
    private final Random random = new Random();

    /**
     * 调整数据以满足特定的回归关系
     */
    @Tool(description = "调整数据以满足指定的回归系数和R²值，支持多元线性回归")
    public String adjustDataForRegression(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "因变量列索引（从1开始）") Integer dependentVar,
            @ToolParam(description = "自变量列索引列表（从1开始）") List<Integer> independentVars,
            @ToolParam(description = "目标回归系数列表（与自变量对应）") List<Double> targetCoefficients,
            @ToolParam(description = "目标R²值（0.0-1.0）") Double targetRSquared,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {
        
        log.info("[回归调整] 开始调整回归关系，sessionId={}, dependentVar={}, independentVars={}, targetR²={}", 
                sessionId, dependentVar, independentVars, targetRSquared);
        
        try {
            // 参数验证
            if (dependentVar == null || dependentVar < 1) {
                throw new IllegalArgumentException("因变量列索引必须大于0，当前值：" + dependentVar);
            }

            if (independentVars == null || independentVars.isEmpty()) {
                throw new IllegalArgumentException("自变量列索引列表不能为空");
            }

            // 验证自变量列索引
            for (Integer col : independentVars) {
                if (col == null || col < 1) {
                    throw new IllegalArgumentException("自变量列索引必须大于0，当前值：" + col);
                }
                if (col.equals(dependentVar)) {
                    throw new IllegalArgumentException("自变量不能与因变量是同一列");
                }
            }

            if (targetCoefficients == null || targetCoefficients.isEmpty()) {
                throw new IllegalArgumentException("目标回归系数列表不能为空");
            }

            if (targetCoefficients.size() != independentVars.size()) {
                throw new IllegalArgumentException("目标回归系数数量必须与自变量数量一致");
            }

            // 验证回归系数
            for (Double coeff : targetCoefficients) {
                if (coeff == null) {
                    throw new IllegalArgumentException("回归系数不能为null");
                }
            }

            if (targetRSquared == null || targetRSquared < 0.0 || targetRSquared > 1.0) {
                throw new IllegalArgumentException("目标R²值必须在0.0到1.0之间，当前值：" + targetRSquared);
            }

            if (tolerance == null) tolerance = 0.05;
            if (tolerance <= 0 || tolerance > 1) {
                throw new IllegalArgumentException("容差必须在0到1之间，当前值：" + tolerance);
            }
            
            // 获取原始数据
            List<Double> yData = baseTools.getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xData = new ArrayList<>();
            for (Integer col : independentVars) {
                List<Double> data = baseTools.getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                xData.add(new ArrayList<>(data));
            }
            
            if (yData.isEmpty()) {
                throw new RuntimeException("因变量列没有有效的数值数据");
            }
            
            int sampleSize = yData.size();
            int numPredictors = independentVars.size();
            
            // 计算当前回归统计 - 使用内部方法避免数据库保存
            Map<String, Object> currentStats = baseTools.calculateRegressionInternal(sessionId, dependentVar, independentVars);
            double currentRSquared = (Double) currentStats.get("rSquared");
            double[] currentCoefficients = (double[]) currentStats.get("coefficients");
            
            log.info("[回归调整] 当前R²: {}, 目标R²: {}", currentRSquared, targetRSquared);
            
            // 如果已经在目标范围内，无需调整
            if (Math.abs(currentRSquared - targetRSquared) <= tolerance) {
                boolean coefficientsMatch = true;
                for (int i = 0; i < targetCoefficients.size(); i++) {
                    if (Math.abs(currentCoefficients[i + 1] - targetCoefficients.get(i)) > tolerance) {
                        coefficientsMatch = false;
                        break;
                    }
                }
                
                if (coefficientsMatch) {
                    Map<String, Object> metrics = new HashMap<>();
                    metrics.put("currentRSquared", currentRSquared);
                    metrics.put("targetRSquared", targetRSquared);
                    metrics.put("currentCoefficients", Arrays.copyOfRange(currentCoefficients, 1, currentCoefficients.length));
                    metrics.put("targetCoefficients", targetCoefficients);
                    return String.format("当前回归关系已满足要求，R²=%.3f", currentRSquared);
                }
            }
            
            // 生成新的因变量数据以满足目标回归关系
            List<Double> adjustedYData = generateRegressionData(xData, targetCoefficients, targetRSquared);
            
            // 验证调整后的回归统计
            // 重新构建xMatrix用于验证
            double[][] xMatrix = new double[sampleSize][numPredictors];
            for (int i = 0; i < sampleSize; i++) {
                for (int j = 0; j < numPredictors; j++) {
                    xMatrix[i][j] = xData.get(j).get(i);
                }
            }

            OLSMultipleLinearRegression adjustedRegression = new OLSMultipleLinearRegression();
            double[] adjustedYArray = adjustedYData.stream().mapToDouble(Double::doubleValue).toArray();
            adjustedRegression.newSampleData(adjustedYArray, xMatrix);
            double achievedRSquared = adjustedRegression.calculateRSquared();
            double[] achievedCoefficients = adjustedRegression.estimateRegressionParameters();
            
            // 生成变更记录
            List<List<Object>> changedCells = new ArrayList<>();
            for (int i = 0; i < yData.size(); i++) {
                if (!yData.get(i).equals(adjustedYData.get(i))) {
                    // 转换为适当的选项值
                    double adjustedValue = adjustedYData.get(i);
                    int optionValue = Math.max(1, Math.min(5, (int) Math.round(adjustedValue)));
                    
                    changedCells.add(Arrays.asList(
                        i + 1, // 行号（跳过表头）
                        dependentVar - 1, // 列号（0基索引）
                        String.valueOf(optionValue)
                    ));
                }
            }
            
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("originalRSquared", currentRSquared);
            metrics.put("achievedRSquared", achievedRSquared);
            metrics.put("targetRSquared", targetRSquared);
            metrics.put("originalCoefficients", Arrays.copyOfRange(currentCoefficients, 1, currentCoefficients.length));
            metrics.put("achievedCoefficients", Arrays.copyOfRange(achievedCoefficients, 1, achievedCoefficients.length));
            metrics.put("targetCoefficients", targetCoefficients);
            metrics.put("cellsChanged", changedCells.size());
            
            // 重新计算实际的R²值，确保信息一致性
            Map<String, Object> actualStats = baseTools.calculateRegressionInternal(sessionId, dependentVar, independentVars);
            double actualRSquared = (Double) actualStats.getOrDefault("rSquared", achievedRSquared);
            metrics.put("achievedRSquared", actualRSquared);

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            String explanation = String.format(
                "成功调整回归模型R²值从%.3f到%.3f（目标%.3f），共调整%d个单元格。",
                currentRSquared, actualRSquared, targetRSquared, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);
            
        } catch (Exception e) {
            log.error("[回归调整] 调整失败", e);
            return "回归关系调整失败: " + e.getMessage();
        }
    }

    /**
     * 生成满足指定回归关系的因变量数据
     */
    private List<Double> generateRegressionData(List<List<Double>> xData, List<Double> coefficients, double targetRSquared) {
        int sampleSize = xData.get(0).size();
        int numPredictors = xData.size();
        
        List<Double> yData = new ArrayList<>();
        
        // 计算线性组合部分
        for (int i = 0; i < sampleSize; i++) {
            double linearCombination = 0.0;
            for (int j = 0; j < numPredictors; j++) {
                linearCombination += coefficients.get(j) * xData.get(j).get(i);
            }
            yData.add(linearCombination);
        }
        
        // 添加误差项以达到目标R²
        if (targetRSquared < 1.0) {
            DescriptiveStatistics yStats = new DescriptiveStatistics();
            yData.forEach(yStats::addValue);
            double yVariance = yStats.getVariance();
            
            // 计算需要的误差方差
            double errorVariance = yVariance * (1.0 - targetRSquared) / targetRSquared;
            double errorStd = Math.sqrt(errorVariance);
            
            NormalDistribution errorDist = new NormalDistribution(0, errorStd);
            for (int i = 0; i < sampleSize; i++) {
                yData.set(i, yData.get(i) + errorDist.sample());
            }
        }
        
        return yData;
    }

    /**
     * 调整数据以满足方差分析的要求
     */
    @Tool(description = "调整数据以满足方差分析的显著性要求，控制组间差异")
    public String adjustDataForAnova(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "分组变量列索引（从1开始）") Integer groupCol,
            @ToolParam(description = "被解释变量列索引（从1开始）") Integer valueCol,
            @ToolParam(description = "目标F统计量值") Double targetFStatistic,
            @ToolParam(description = "目标显著性水平（如0.05）") Double targetPValue,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {
        
        log.info("[方差分析调整] 开始调整数据，sessionId={}, groupCol={}, valueCol={}, targetF={}, targetP={}", 
                sessionId, groupCol, valueCol, targetFStatistic, targetPValue);
        
        try {
            // 参数验证
            if (groupCol == null || groupCol < 1) {
                throw new IllegalArgumentException("分组变量列索引必须大于0，当前值：" + groupCol);
            }

            if (valueCol == null || valueCol < 1) {
                throw new IllegalArgumentException("被解释变量列索引必须大于0，当前值：" + valueCol);
            }

            if (groupCol.equals(valueCol)) {
                throw new IllegalArgumentException("分组变量和被解释变量不能是同一列");
            }

            if (targetFStatistic == null || targetFStatistic < 0) {
                throw new IllegalArgumentException("目标F统计量必须大于等于0，当前值：" + targetFStatistic);
            }

            if (targetPValue != null && (targetPValue <= 0 || targetPValue >= 1)) {
                throw new IllegalArgumentException("目标显著性水平必须在0到1之间，当前值：" + targetPValue);
            }

            if (tolerance == null) tolerance = 0.05;
            if (tolerance <= 0 || tolerance > 1) {
                throw new IllegalArgumentException("容差必须在0到1之间，当前值：" + tolerance);
            }

            // 获取原始数据
            List<String> groupData = baseTools.getColumnData(sessionId, groupCol - 1);
            List<Double> valueData = baseTools.getNumericColumnData(sessionId, valueCol - 1);
            
            if (groupData.isEmpty() || valueData.isEmpty()) {
                throw new RuntimeException("分组数据或数值数据为空");
            }
            
            if (groupData.size() != valueData.size()) {
                throw new RuntimeException("分组数据与数值数据长度不一致");
            }
            
            // 获取当前方差分析统计 - 使用内部方法避免数据库保存
            Map<String, Object> currentStats = baseTools.calculateAnovaInternal(sessionId, groupCol, valueCol);
            double currentF = (Double) currentStats.get("fStatistic");

            // 按组分类数据
            Map<String, List<Double>> groupedData = new HashMap<>();
            for (int i = 0; i < groupData.size(); i++) {
                String group = groupData.get(i);
                groupedData.computeIfAbsent(group, k -> new ArrayList<>()).add(valueData.get(i));
            }
            
            // 调整组间均值以达到目标F统计量
            List<Double> adjustedValueData = adjustGroupMeans(groupedData, targetFStatistic);
            
            // 生成变更记录
            List<List<Object>> changedCells = new ArrayList<>();
            for (int i = 0; i < valueData.size(); i++) {
                if (!valueData.get(i).equals(adjustedValueData.get(i))) {
                    double adjustedValue = adjustedValueData.get(i);
                    int optionValue = Math.max(1, Math.min(5, (int) Math.round(adjustedValue)));
                    
                    changedCells.add(Arrays.asList(
                        i + 1,
                        valueCol - 1,
                        String.valueOf(optionValue)
                    ));
                }
            }
            
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("targetFStatistic", targetFStatistic);
            metrics.put("targetPValue", targetPValue);
            metrics.put("cellsChanged", changedCells.size());

            // 重新计算实际的F统计量，确保信息一致性
            Map<String, Object> actualStats = baseTools.calculateAnovaInternal(sessionId, groupCol, valueCol);
            double actualF = (Double) actualStats.getOrDefault("fStatistic", 0.0);
            metrics.put("achievedFStatistic", actualF);

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            String explanation = String.format(
                "成功调整方差分析F统计量到%.3f（目标%.3f），共调整%d个单元格。",
                actualF, targetFStatistic, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[方差分析调整] 调整失败", e);
            return "方差分析调整失败: " + e.getMessage();
        }
    }

    /**
     * 调整组间均值以达到目标F统计量
     */
    private List<Double> adjustGroupMeans(Map<String, List<Double>> groupedData, double targetF) {
        List<String> groups = new ArrayList<>(groupedData.keySet());
        int numGroups = groups.size();
        
        // 计算总体均值
        double grandMean = groupedData.values().stream()
                .flatMap(List::stream)
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
        
        // 调整各组均值
        Map<String, Double> adjustedMeans = new HashMap<>();
        for (int i = 0; i < numGroups; i++) {
            String group = groups.get(i);
            // 根据目标F值计算需要的组间差异
            double adjustment = (i - (numGroups - 1) / 2.0) * Math.sqrt(targetF) * 0.5;
            adjustedMeans.put(group, grandMean + adjustment);
        }
        
        // 生成调整后的数据
        List<Double> result = new ArrayList<>();
        for (String group : groups) {
            List<Double> groupValues = groupedData.get(group);
            double targetMean = adjustedMeans.get(group);
            
            DescriptiveStatistics stats = new DescriptiveStatistics();
            groupValues.forEach(stats::addValue);
            double currentMean = stats.getMean();
            double adjustment = targetMean - currentMean;
            
            for (Double value : groupValues) {
                result.add(value + adjustment);
            }
        }
        
        return result;
    }

    /**
     * 调整数据以满足T检验的显著性要求
     */
    @Tool(description = "调整数据以满足独立样本T检验或单样本T检验的显著性要求")
    public String adjustDataForTTest(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "分组变量列索引（独立样本T检验用，从1开始）") Integer groupCol,
            @ToolParam(description = "被检验变量列索引（从1开始）") Integer testCol,
            @ToolParam(description = "检验类型：independent（独立样本）或onesample（单样本）") String testType,
            @ToolParam(description = "目标t统计量值") Double targetTStatistic,
            @ToolParam(description = "单样本检验的对比值（仅单样本检验需要）") Double testValue,
            @ToolParam(description = "目标显著性水平（如0.05）") Double targetPValue,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[T检验调整] 开始调整数据，sessionId={}, testType={}, testCol={}, targetT={}",
                sessionId, testType, testCol, targetTStatistic);

        try {
            // 参数验证
            if (testCol == null || testCol < 1) {
                throw new IllegalArgumentException("被检验变量列索引必须大于0，当前值：" + testCol);
            }

            if (testType == null || (!testType.equals("independent") && !testType.equals("onesample"))) {
                throw new IllegalArgumentException("检验类型必须是'independent'或'onesample'，当前值：" + testType);
            }

            if (targetTStatistic == null) {
                throw new IllegalArgumentException("目标t统计量不能为null");
            }

            if (targetPValue != null && (targetPValue <= 0 || targetPValue >= 1)) {
                throw new IllegalArgumentException("目标显著性水平必须在0到1之间，当前值：" + targetPValue);
            }

            if (tolerance == null) tolerance = 0.05;
            if (tolerance <= 0 || tolerance > 1) {
                throw new IllegalArgumentException("容差必须在0到1之间，当前值：" + tolerance);
            }

            // 独立样本T检验的额外验证
            if ("independent".equals(testType)) {
                if (groupCol == null || groupCol < 1) {
                    throw new IllegalArgumentException("独立样本T检验的分组变量列索引必须大于0，当前值：" + groupCol);
                }
                if (groupCol.equals(testCol)) {
                    throw new IllegalArgumentException("分组变量和被检验变量不能是同一列");
                }
            }

            // 单样本T检验的额外验证
            if ("onesample".equals(testType) && testValue == null) {
                throw new IllegalArgumentException("单样本T检验需要指定对比值");
            }

            List<List<Object>> changedCells = new ArrayList<>();
            Map<String, Object> metrics = new HashMap<>();
            String explanation;

            if ("independent".equals(testType)) {
                // 独立样本T检验调整
                if (groupCol == null) {
                    throw new IllegalArgumentException("独立样本T检验需要指定分组变量列");
                }

                List<String> groupData = baseTools.getColumnData(sessionId, groupCol - 1);
                List<Double> testData = baseTools.getNumericColumnData(sessionId, testCol - 1);

                if (groupData.isEmpty() || testData.isEmpty()) {
                    throw new RuntimeException("分组数据或检验数据为空");
                }

                // 按组分类数据
                Map<String, List<Double>> groupedData = new HashMap<>();
                for (int i = 0; i < groupData.size(); i++) {
                    String group = groupData.get(i);
                    groupedData.computeIfAbsent(group, k -> new ArrayList<>()).add(testData.get(i));
                }

                if (groupedData.size() != 2) {
                    throw new RuntimeException("独立样本T检验需要恰好两个组");
                }

                List<Double> adjustedData = adjustForIndependentTTest(groupedData, targetTStatistic);

                // 生成变更记录
                for (int i = 0; i < testData.size(); i++) {
                    if (!testData.get(i).equals(adjustedData.get(i))) {
                        double adjustedValue = adjustedData.get(i);
                        int optionValue = Math.max(1, Math.min(5, (int) Math.round(adjustedValue)));

                        changedCells.add(Arrays.asList(
                            i + 1,
                            testCol - 1,
                            String.valueOf(optionValue)
                        ));
                    }
                }

                explanation = String.format("成功调整独立样本T检验数据，目标t统计量%.3f，共调整%d个单元格",
                    targetTStatistic, changedCells.size());

            } else if ("onesample".equals(testType)) {
                // 单样本T检验调整
                if (testValue == null) {
                    throw new IllegalArgumentException("单样本T检验需要指定对比值");
                }

                List<Double> testData = baseTools.getNumericColumnData(sessionId, testCol - 1);
                if (testData.isEmpty()) {
                    throw new RuntimeException("检验数据为空");
                }

                List<Double> adjustedData = adjustForOneSampleTTest(testData, testValue, targetTStatistic);

                // 生成变更记录
                for (int i = 0; i < testData.size(); i++) {
                    if (!testData.get(i).equals(adjustedData.get(i))) {
                        double adjustedValue = adjustedData.get(i);
                        int optionValue = Math.max(1, Math.min(5, (int) Math.round(adjustedValue)));

                        changedCells.add(Arrays.asList(
                            i + 1,
                            testCol - 1,
                            String.valueOf(optionValue)
                        ));
                    }
                }

                // 单样本T检验调整完成

            } else {
                throw new IllegalArgumentException("不支持的检验类型：" + testType);
            }

            metrics.put("targetTStatistic", targetTStatistic);
            metrics.put("targetPValue", targetPValue);
            metrics.put("testType", testType);
            metrics.put("cellsChanged", changedCells.size());

            // 重新计算实际的T统计量，确保信息一致性
            Map<String, Object> actualStats = baseTools.calculateTTestInternal(sessionId, groupCol, testCol, testType, testValue);
            double actualT = (Double) actualStats.getOrDefault("tStatistic", 0.0);
            metrics.put("achievedTStatistic", actualT);

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            explanation = String.format(
                "成功调整T检验统计量到%.3f（目标%.3f），共调整%d个单元格。",
                actualT, targetTStatistic, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[T检验调整] 调整失败", e);
            return "T检验调整失败: " + e.getMessage();
        }
    }

    /**
     * 调整数据以满足独立样本T检验要求
     */
    private List<Double> adjustForIndependentTTest(Map<String, List<Double>> groupedData, double targetT) {
        List<String> groups = new ArrayList<>(groupedData.keySet());
        List<Double> group1 = groupedData.get(groups.get(0));
        List<Double> group2 = groupedData.get(groups.get(1));

        // 计算当前统计量
        DescriptiveStatistics stats1 = new DescriptiveStatistics();
        group1.forEach(stats1::addValue);
        DescriptiveStatistics stats2 = new DescriptiveStatistics();
        group2.forEach(stats2::addValue);

        double mean1 = stats1.getMean();
        double mean2 = stats2.getMean();
        double pooledStd = Math.sqrt(((stats1.getVariance() * (group1.size() - 1)) +
                                     (stats2.getVariance() * (group2.size() - 1))) /
                                    (group1.size() + group2.size() - 2));

        // 计算需要的均值差异
        double requiredMeanDiff = targetT * pooledStd * Math.sqrt(1.0/group1.size() + 1.0/group2.size());
        double currentMeanDiff = mean1 - mean2;
        double adjustment = (requiredMeanDiff - currentMeanDiff) / 2.0;

        // 调整两组数据
        List<Double> result = new ArrayList<>();

        // 第一组向上调整
        for (Double value : group1) {
            result.add(value + adjustment);
        }

        // 第二组向下调整
        for (Double value : group2) {
            result.add(value - adjustment);
        }

        return result;
    }

    /**
     * 调整数据以满足单样本T检验要求
     */
    private List<Double> adjustForOneSampleTTest(List<Double> data, double testValue, double targetT) {
        DescriptiveStatistics stats = new DescriptiveStatistics();
        data.forEach(stats::addValue);

        double currentMean = stats.getMean();
        double currentStd = stats.getStandardDeviation();
        int n = data.size();

        // 计算需要的样本均值
        double requiredMean = testValue + targetT * currentStd / Math.sqrt(n);
        double adjustment = requiredMean - currentMean;

        // 调整所有数据点
        return data.stream()
                .map(value -> value + adjustment)
                .collect(Collectors.toList());
    }
}

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #    url: *******************************************************************
#    url: ***************************************************************************
#    url: ******************************************************************
    url: *********************************************************************************************
#    username: MyWenJuanXing
    username: yifeng
#    username: root
#    password: 12345njcit
    password: zYY0511@



mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # mybatis日志
    map-underscore-to-camel-case: true #驼峰命名


logging:
  file:
    path: /logs # 定义日志存储路径
  level:
    root: info # 默认日志级别







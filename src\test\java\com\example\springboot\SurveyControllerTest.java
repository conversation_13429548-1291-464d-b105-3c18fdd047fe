// package com.example.springboot;

// import com.example.springboot.Utils.ExcelGenerator;
// import com.example.springboot.entity.QuestionInfo;
// import com.example.springboot.entity.SurveyData;
// import com.example.springboot.service.impl.WJXOrderService;
// import com.fasterxml.jackson.databind.ObjectMapper;
// import com.fasterxml.jackson.databind.SerializationFeature;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.http.HttpHeaders;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.client.RestTemplate;

// import java.io.ByteArrayOutputStream;
// import java.io.IOException;
// import java.util.Arrays;
// import java.util.List;

// @SpringBootTest(properties = {
//     "spring.main.allow-bean-definition-overriding=true",
//     "spring.main.allow-circular-references=true"
// })
// @ActiveProfiles("test")
// class SurveyControllerTest {

//     private static final String TEST_URL = "https://www.wjx.cn/vm/YRtEuZS.aspx";
// //    private static final String TEST_URL = "https://www.wjx.cn/vm/rqT3qnT.aspx";
//     // 使用List存储多个回答记录
//     private static final List<String> TEST_DATA = Arrays.asList(
//             "1$}2$1}3$3}4$1}5$高杜晓^萧杜超^沈潘健}6$嗯，不错}7$1|2}8$1}9$2,1,-2}10$1}11$一直加油}12$孙谭莉^刘艳^严瑞}13$1!贾夏兰^2!段杨文}14$加把劲}15$你在进步}16$1!1,2!2}17$1!2,2!2;4,3!1}18$1!1,2!2,3!2,4!2,5!5}19$1!80^2!60}20$4}21$3}22$1}23$1}24$1!3,2!4}25$4}26$2,3,1}27$1!100.00^2!0.00}28$0",
//             "1$}2$1}3$1}4$2}5$田宁^赵凤^沈谢艳}6$希望你继续保持这个状态}7$1|2}8$1}9$2,1,-2}10$1}11$再不怕困难}12$魏蒋玲^赖超^曾霞}13$1!陆杨迪^2!雷洋}14$继续加油吧}15$加把劲}16$1!2,2!4}17$1!1;4,2!1;2,3!1;3}18$1!1,2!2,3!5,4!1,5!3}19$1!40^2!100}20$2}21$11}22$1}23$1}24$1!2,2!4}25$1}26$2,3,1}27$1!40.00^2!60.00}28$100"
//     );
// //    private static final List<String> TEST_DATA = Arrays.asList(
// //            "1$4^很有潜力,6^再不怕困难,2,3,1,5}2$6,3,5,4,1,2}3$1,4,5,3,7^你在进步,-2,-2}4$2,4^继续保持好状态,5^加倍努力,1,3,7,-2",
// //            "1$4^厉害了,5,6^保持住,2,3,1}2$6,4,1,3,5,2}3$5,7^加把劲,3,6,4,-2,-2}4$4^继续保持好状态,6^继续发展,1,3,2,5^真的很赞,-2"
// //    );
//     @Test
//     void generateExcelIntegrationTest() throws IOException {
//         // 2. 执行测试
// //        ResponseEntity<byte[]> response = generateExcel(TEST_URL, TEST_DATA);
// //
// //        // 3. 验证基础响应
// //        assertNotNull(response, "响应不应为null");
// //        assertEquals(200, response.getStatusCodeValue(), "HTTP状态码应为200");
// //        assertTrue(response.getBody().length > 1024, "生成的文件应大于1KB");
// //
// //        // 4. 验证文件头
// //        HttpHeaders headers = response.getHeaders();
// //        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
// //                headers.getContentType().toString(),
// //                "Content-Type应为Excel类型");
// //
// //        assertTrue(headers.getFirst(HttpHeaders.CONTENT_DISPOSITION)
// //                        .contains("survey_data.xlsx"),
// //                "文件名应包含survey_data.xlsx");
// //
// //        // 5. 将文件保存到本地验证
// //        byte[] excelBytes = response.getBody();
// //        String outputPath = "target/generated-excel/survey_data2.xlsx";
// //
// //        // 确保目录存在
// //        new File("target/generated-excel").mkdirs();
// //
// //        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
// //            fos.write(excelBytes);
// //        }
// //
// //        System.out.println("Excel文件已保存至: " + new File(outputPath).getAbsolutePath());
//     }

//     @Autowired
//     WJXOrderService wjxService;
//     @Test
//     void test()  {
//         String s = wjxService.fetchSurveyHtml(TEST_URL, "202505231839047664",1);
// //        System.out.println(s);
//         List<SurveyData> surveyData = ExcelGenerator.parseSurveyData(s);
//         // Create ObjectMapper and configure it
//         ObjectMapper objectMapper = new ObjectMapper();
//         objectMapper.enable(SerializationFeature.INDENT_OUTPUT); // for pretty printing

//         try {
//             // Convert list to JSON string
//             String json = objectMapper.writeValueAsString(surveyData);

//             // Print the JSON
//             System.out.println(json);
//         } catch (Exception e) {
//             e.printStackTrace();
//         }
//     }

//     @Test
//     void test2()  {
//         List<SurveyData> surveyData1 = wjxService.fetchSurveyData(TEST_URL, "123", 2);
// //        System.out.println(s);
//         System.out.println(surveyData1);

//     }
//     public ResponseEntity<byte[]> generateExcel(
//             String surveyUrl,
//             List<String> answerData) throws IOException {
//         String html = "";
//         // 1. 获取问卷HTML
// //        String html = fetchSurveyHtml(surveyUrl);
//         if (surveyUrl == null || surveyUrl.isEmpty()) {
//             System.out.println("Survey URL is required");
//             return null;
//         }
//         try {
//             RestTemplate restTemplate = new RestTemplate();
//             HttpHeaders headers = new HttpHeaders();
//             // 设置必要的请求头，例如User-Agent
//             headers.add("User-Agent", "Mozilla/5.0");

//             ResponseEntity<String> response = restTemplate.getForEntity(surveyUrl, String.class);
//             html = response.getBody();
//         } catch (Exception e) {
//             System.out.println("Error fetching survey");
//             return null;
//         }

//         // 2. 解析题目信息
//         List<QuestionInfo> questions = ExcelGenerator.parseSurveyQuestions(html);

//         // 3. 生成Excel
//         ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//         ExcelGenerator.generateExcel(questions, answerData, outputStream);

//         // 4. 返回文件
//         return ResponseEntity.ok()
//                 .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
//                 .header("Content-Disposition", "attachment; filename=survey_data.xlsx")
//                 .body(outputStream.toByteArray());
//     }

// }

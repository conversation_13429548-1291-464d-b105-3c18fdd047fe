let parsedDataJson;
let matchIndex;

function generate_jsondata() {
    let data_json = {};
    let div_list = $(".field-label");
    let lists = $('.field.ui-field-contain')
    for (let i = 0; i < div_list.length; i++) {
        let questionData = {}; // 存储题目数据
        let questionType = parseInt(lists.eq(i).attr('type'));
        // 尝试获取 div[class="topicnumber"] 和 div[class="topichtml"] 的文本
        let topicNumber = lists.eq(i).find(".topicnumber").length ? lists.eq(i).find(".topicnumber").text().trim() : "";
        // let topicHtml = lists.eq(i).find(".topichtml").text().trim();
        let topicHtml = lists.eq(i).find(".topichtml").text().trim().split(/[\s【]/)[0];

        // 拼接结果
        // let combinedText = topicNumber + topicHtml;
        let combinedText = topicHtml;
        questionData.title = combinedText

        questionData.type = questionType.toString();
        if (questionType === 1 || questionType === 2) {
            // 填空题情况
            questionData.msg = '填空题';
            questionData.num = 1;
        } else if (questionType == 3) {  // 单选题
            questionData.msg = '单选题'
            questionData.num = 1;
        } else if (questionType == 4) {  // 多选题
            let options = lists.eq(i).find('.ui-checkbox div[class="label"]').parent();
            questionData.msg = '多选题'
            questionData.num = options.length;
        } else if (questionType == 6) {  // 处理矩阵量表题型
            let rows = lists.eq(i).find('.matrixtable tbody tr[tp="d"]');

            if (checkMatrixRating(lists, i)) {
                questionData.type = '6single';
                questionData.msg = '矩阵单选题'
                questionData.num = rows.length;

                // 获取列数
                let cols = lists.eq(i).find('.matrixtable thead th').length - 1;
                questionData.cols = cols;

            } else {
                let cols = $(rows[0]).find('a');
                questionData.type = '6multiple';
                questionData.msg = '矩阵多选题'
                questionData.num = rows.length * cols.length;

                // 获取列数
                questionData.cols = cols.length;
            }
        } else if (questionType == 7) {  // 处理下拉框题型
            questionData.msg = '下拉题'
            questionData.num = 1;
        } else if (questionType == 11) {  // 处理排序题型
            let options = lists.eq(i).find('.ui-li-static');
            questionData.msg = '排序题'
            questionData.num = options.length;
        } else if (questionType == 12 || questionType == 9) {  // 处理矩阵滑条题型
            if (questionType == 9 && (lists.eq(i).find('.rangeslider').length === 0)) {
                if (lists.eq(i).find('.matrix-rating').hasClass('scaletablewrap')) {
                    questionData.msg = '多填空题2'
                } else {
                    questionData.msg = '多填空题1'
                }
                let len = document.querySelectorAll(`#div${i + 1} input`).length;
                if (len == 0) {
                    len = document.querySelectorAll(`#div${i + 1} textarea`).length
                }
                questionData.num = len;
            } else {
                let rangesliders = lists.eq(i).find('.rangeslider');
                questionData.msg = '滑条题'
                questionData.num = rangesliders.length;
            }
        } else if (questionType == 8) {  // 处理滑条题型
            questionData.msg = '单项滑条题'
            questionData.num = 1;
        } else if (questionType == 5) {  // 处理单选量表题型
            questionData.msg = '单项量表题'
            questionData.num = 1;
        } else {
            questionData.msg = '暂不支持该题型'
            questionData.num = 0;
        }

        // 将题目数据添加到 JSON 数据对象中
        data_json[(i + 1).toString()] = questionData;
    }
    // 将 JSON 数据赋值到全局变量 window.data_json
    // window.data_json = data_json;
    // 将 JSON 数据对象转换为字符串并弹出
    // console.log(data_json);

    // 计算所有题目数据中 num 的总和
    let totalNum = 0;
    for (let key in data_json) {
        totalNum += data_json[key].num;
    }
    // console.log("num 总和:", totalNum);
    return data_json;
}

//文本相似度判断
function similar(s, t, f) {
    if (!s || !t) {
        return 0
    }
    var l = s.length > t.length ? s.length : t.length
    var n = s.length
    var m = t.length
    var d = []
    f = f || 3
    var min = function (a, b, c) {
        return a < b ? (a < c ? a : c) : (b < c ? b : c)
    }
    var i, j, si, tj, cost
    if (n === 0) return m
    if (m === 0) return n
    for (i = 0; i <= n; i++) {
        d[i] = []
        d[i][0] = i
    }
    for (j = 0; j <= m; j++) {
        d[0][j] = j
    }
    for (i = 1; i <= n; i++) {
        si = s.charAt(i - 1)
        for (j = 1; j <= m; j++) {
            tj = t.charAt(j - 1)
            if (si === tj) {
                cost = 0
            } else {
                cost = 1
            }
            d[i][j] = min(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + cost)
        }
    }
    let res = (1 - d[n][m] / l)
    return res.toFixed(f)
}

function generateAnswerStrings(data_json, excelData, matchIndex) {
    let answerList = [];

    // 获取填写区间
    const startRow = parseInt(document.getElementById('fill_range_start').value, 10);
    const endRow = parseInt(document.getElementById('fill_range_end').value, 10);

    // 从填写区间指定的行开始遍历Excel数据
    for (let rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
        let row = excelData[rowIndex];
        let answerStr = '';
        let colIndex = matchIndex; // 初始化列索引为匹配到的第一个题目的列索引

        for (let qIndex = 1; qIndex <= Object.keys(data_json).length; qIndex++) {
            let questionKey = qIndex.toString();
            let question = data_json[questionKey];
            let qType = question.type;
            let qNum = question.num;
            let qTitle = question.title;

            let answer = ''; // 当前题目的答案

            // 根据题型处理
            if (qType === '3') {
                // 单选题
                answer = row[colIndex];
            } else if (qType === '4') {
                // 多选题
                let options = [];
                for (let i = 0; i < qNum; i++) {
                    let cellValue = row[colIndex + i];
                    if (cellValue && (cellValue.toString().includes("1") || cellValue.toString().toLowerCase() === 'true')) {
                        const str = cellValue.toString();
                        const caretIndex = str.indexOf('^');
                        const result = caretIndex >= 0 ? (i + 1).toString()+str.substring(caretIndex) : (i + 1).toString();
                        options.push(result);
                    }
                }
                answer = options.join('|');
            } else if (qType === '1' || qType === '2') {
                // 填空题
                if (qNum === 1) {
                    answer = row[colIndex];
                } else {
                    let fills = [];
                    for (let i = 0; i < qNum; i++) {
                        fills.push(row[colIndex + i]);
                    }
                    answer = fills.join('^');
                }
            } else if (qType === '6single') {
                // 矩阵单选题
                let pairs = [];
                for (let i = 0; i < qNum; i++) {
                    let cellValue = row[colIndex + i];
                    pairs.push(`${i + 1}!${cellValue}`);
                }
                answer = pairs.join(',');
            } else if (qType === '6multiple') {
                // 矩阵多选题
                let pairs = [];
                let numRows = qNum / question.cols;

                for (let rowIdx = 0; rowIdx < numRows; rowIdx++) {
                    let selections = [];
                    for (let colIdx = 0; colIdx < question.cols; colIdx++) {
                        let cellValue = row[colIndex + rowIdx * question.cols + colIdx];
                        if (cellValue && (cellValue.toString().includes("1") || cellValue.toString().toLowerCase() === 'true')) {
                            selections.push((colIdx + 1).toString());
                        }
                    }
                    if (selections.length > 0) {
                        pairs.push(`${rowIdx + 1}!${selections.join(';')}`);
                    }
                }
                answer = pairs.join(',');
            } else if (qType === '11') {
                // 排序题
                let options = [];
                let rankOrder = new Array(qNum).fill(null);

                // 将 row 中的排序值转换成排名次序
                for (let i = 0; i < qNum; i++) {
                    const cellValue = (row[colIndex + i]!=null&&row[colIndex + i]!=='')?parseInt(row[colIndex + i].toString().split('^')[0], 10):null;
                    if (cellValue!= null && cellValue > 0 && cellValue <= qNum) {
                        // 将选项编号 `i+1` 放到 rankOrder 中对应的位置
                        const str = row[colIndex + i].toString();
                        const caretIndex = str.indexOf('^');
                        const result = caretIndex >= 0 ? (i + 1).toString()+str.substring(caretIndex) : (i + 1).toString();
                        rankOrder[cellValue - 1] = result;
                    }
                }
                // 过滤掉 null 值，并构建答案字符串
                answer = rankOrder.filter(item => item != null).join(',');
                // 构建答案字符串
                // answer = rankOrder.join(',');
            } else if (qType === '12' || qType === '9') {
                if (question.msg === '多填空题1') {
                    // 多填空题
                    let fills = [];
                    for (let i = 0; i < qNum; i++) {
                        let cellValue = row[colIndex + i];
                        fills.push(cellValue);
                    }
                    answer = fills.join('^');
                } else if (question.msg === '多填空题2') {
                    // 多填空题
                    let fills = [];
                    for (let i = 0; i < qNum; i++) {
                        let cellValue = row[colIndex + i];
                        fills.push(`${i + 1}!${cellValue}`);
                    }
                    answer = fills.join('^');
                } else if (question.msg === '滑条题') {
                    // 滑条题
                    let fills = [];
                    for (let i = 0; i < qNum; i++) {
                        let cellValue = row[colIndex + i];
                        fills.push(`${i + 1}!${cellValue}`);
                    }
                    answer = fills.join('^');
                } else {
                    // 其他情况，按照默认处理
                    let fills = [];
                    for (let i = 0; i < qNum; i++) {
                        let cellValue = row[colIndex + i];
                        fills.push(cellValue);
                    }
                    answer = fills.join('^');
                }
            } else if (qType === '5') {
                // 单项量表题
                answer = row[colIndex];
            } else if (qType === '8') {
                // 滑条题
                answer = row[colIndex];
            } else {
                // 其他题型
                answer = row[colIndex];
            }

            // 构建答案字符串
            if (answerStr !== '') {
                answerStr += `}`;
                generate_jsondata
            }
            answerStr += `${questionKey}$${answer}`;

            // 更新列索引
            colIndex += qNum;
        }

        // 将答案字符串添加到列表中
        answerList.push(answerStr);
    }

    return answerList;
}


function checkMatrixRating(lists, index) {

    // 获取 lists 中索引为 index 的元素，然后查找其下的 .matrix-rating a 元素
    var matrixRatingLinks = lists.eq(index).find('.matrix-rating a');

    // 检查 .matrix-rating a 元素是否具有 style="border-radius:3px;" 样式
    if (matrixRatingLinks.css('border-radius') === '3px') {
        return false; // 如果存在样式，则返回 false
    }

    return true; // 如果不存在样式，则返回 true
}

let submitdataList;
let excelData = []; // 存储Excel文件数据
let isMatched = false; // 初始为 false，表示未匹配成功
const urlPattern = /^(https?:\/\/(?:[\w-]+\.)?wjx\.(cn|com|top)\/(vm|vj|.+)\/.*)$/;


function base64Encode(str) {
    return btoa(unescape(encodeURIComponent(str)));
}

function base64Decode(str) {
    return decodeURIComponent(escape(atob(str)));
}


// 4. 提供设置数据的函数，设置时加密
function encryptData(data) {
    return base64Encode(JSON.stringify(data)); // 使用 Base64 编码
}

// 5. 提供获取数据的函数，获取时解密
function decryptData(encryptedData) {
    return JSON.parse(base64Decode(encryptedData)); // Base64 解码
}

// 6. 提供设置数据的函数，设置时加密
function setSubmitData(data) {
    submitdataList = encryptData(data); // 加密后存储
    // console.log("数据已加密并存储");
}

// 7. 提供获取数据的函数，获取时解密
function getSubmitData() {
    return decryptData(submitdataList); // 解密后返回
}

window.setSubmitData = setSubmitData;
window.getSubmitData = getSubmitData;


document.addEventListener('DOMContentLoaded', function () {
    const surveyLinkInput = document.getElementById('survey_link');
    const validationMessage = document.getElementById('validation_message');
    const parseButton = document.getElementById('parse_button');


    // 当输入框失去焦点时，验证链接并显示验证消息
    surveyLinkInput.addEventListener('blur', function () {

        let surveyLink = surveyLinkInput.value.trim();
        surveyLink = surveyLink.replace(/\/vj\//, '/vm/');
        surveyLink = surveyLink.split('#')[0];
        surveyLinkInput.value = surveyLink;

        if (urlPattern.test(surveyLink)) {
            // 链接合法，显示提示消息
            validationMessage.style.display = 'flex';
            validationMessage.style.color = "green";
            validationMessage.querySelector("span").textContent = "问卷链接合法";
        } else {
            // 链接不合法，隐藏提示并清空输入框
            if (surveyLink !== '') {
                alert('请输入问卷星平台合法的问卷链接');
            }
            validationMessage.style.display = 'none';
            surveyLinkInput.value = ''; // 清空输入框
        }
    });

    // 解析按钮点击事件，检查链接是否合法
    // 点击解析按钮时触发事件
    parseButton.addEventListener('click', function () {
        // 检查是否有上传的Excel文件
        if (fileInput.files.length === 0) {
            alert("请先上传EXCEL文件");
            return;
        }

        let surveyLink = surveyLinkInput.value.trim();
        surveyLink = surveyLink.replace(/\/vj\//, '/vm/');
        surveyLink = surveyLink.split('#')[0];
        surveyLinkInput.value = surveyLink;

        if (urlPattern.test(surveyLink)) {
            // 使用 fetch 调用服务器端代理
            fetch(`fetch-survey?url=${encodeURIComponent(surveyLink)}`)
                .then(response => response.text())
                .then(htmlContent => {
                    // 创建一个新的 DOMParser
                    const parser = new DOMParser();
                    // 解析 HTML 字符串
                    const doc = parser.parseFromString(htmlContent, 'text/html');

                    // 将解析后的文档注入到一个隐藏的 div 中
                    const hiddenDiv = document.createElement('div');
                    hiddenDiv.style.display = 'none';
                    hiddenDiv.innerHTML = doc.body.innerHTML;
                    document.body.appendChild(hiddenDiv);

                    // 调用 generate_jsondata 函数
                    let data_json = generate_jsondata();
                    // console.log(data_json);

                    // 移除隐藏的 div
                    document.body.removeChild(hiddenDiv);

                    // 开始匹配 Excel 标题行和 data_json
                    let matchResult = matchExcelHeadersWithDataJson(data_json, excelData);

                    if (matchResult.success) {
                        isMatched = true; // 解析匹配成功，设置标志为 true
                        parsedDataJson = data_json; // 将局部变量 data_json 赋值给全局变量 parsedDataJson
                        matchIndex = matchResult.matchIndex; // 赋值给全局变量
                        // 执行空列检查并提示
                        checkEmptyColumns(); // 新增的空列检查
                        validationMessage.style.display = 'flex';
                        validationMessage.style.color = "green";
                        validationMessage.querySelector("span").textContent = "解析成功！问卷链接:" + surveyLink + "和EXCEL文件题目匹配成功！";
                        // 在这里调用 generateAnswerStrings 函数，传入 matchIndex
                        let answerList = generateAnswerStrings(parsedDataJson, excelData, matchIndex);
                        // console.log("生成的答案列表:", answerList);

                        // 您可以在此处将答案列表保存到全局变量，或者进行下一步处理
                        // 例如，将答案列表保存到一个全局变量：
                        // window.answerList = answerList;
                        // submitdataList = answerList;
                        setSubmitData(answerList)

                        // 显示预览按钮
                        document.getElementById('preview_container').style.display = 'block';

                    } else {
                        isMatched = false; // 如果未匹配成功，确保标志为 false
                        validationMessage.style.display = 'none';
                        surveyLinkInput.value = ''; // 清空输入框
                    }
                })
                .catch(error => {
                    console.error('Error fetching survey:', error);
                    alert('无法获取问卷内容，请稍后再试。');
                });
        } else {
            alert("请输入有效的问卷链接！");
        }
    });
});

document.getElementById('preview_button').addEventListener('click', function () {
    // 确保 parsedDataJson 和 matchIndex 可用
    if (!parsedDataJson || matchIndex === undefined) {
        alert('请先成功解析问卷和Excel文件。');
        return;
    }

    // 构建预览内容的 HTML
    let previewHtml = '<table class="table table-bordered table-striped"><thead><tr><th>题号</th><th>题目标题</th><th>Excel列号</th><th>题型</th></tr></thead><tbody>';

    let colIndex = matchIndex;
    const headerRow = excelData[0]; // 假设第一行是标题行

    for (let qIndex = 1; qIndex <= Object.keys(parsedDataJson).length; qIndex++) {
        let questionKey = qIndex.toString();
        let question = parsedDataJson[questionKey];
        let qNum = question.num;

        // 获取 Excel 列索引
        let startCol = colIndex + 1; // 1-based 列索引
        let endCol = colIndex + qNum;

        // 获取列字母（可选）
        function columnToLetter(column) {
            let temp, letter = '';
            while (column > 0) {
                temp = (column - 1) % 26;
                letter = String.fromCharCode(temp + 65) + letter;
                column = (column - temp - 1) / 26;
            }
            return letter;
        }

        let excelCols = startCol === endCol ? `第 ${startCol} 列 (${columnToLetter(startCol)})` : `第 ${startCol} 列 (${columnToLetter(startCol)}) ~ 第 ${endCol} 列 (${columnToLetter(endCol)})`;

        // 添加一行到表格
        previewHtml += `<tr><td>${questionKey}</td><td>${question.title}</td><td>${excelCols}</td><td>${question.msg}</td></tr>`;

        // 更新 colIndex
        colIndex += qNum;
    }

    previewHtml += '</tbody></table>';

    // 显示预览内容到模态框
    document.getElementById('dataPreviewContent').innerHTML = previewHtml;
    document.getElementById('dataPreviewModal').style.display = 'block';
});

// 关闭预览模态框
document.querySelector('.close-data-preview').addEventListener('click', function () {
    document.getElementById('dataPreviewModal').style.display = 'none';
});

// 点击模态框外部关闭模态框
window.addEventListener('click', function (event) {
    const modal = document.getElementById('dataPreviewModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
});


function matchExcelHeadersWithDataJson(data_json, excelData) {
    // 检查 excelData 是否有效
    if (!excelData || excelData.length === 0) {
        alert('Excel 数据为空，请检查文件内容');
        return {success: false};
    }

    // 获取 Excel 标题行
    let headerRow = excelData[0]; // 假设第一行是标题行

    // 找到 data_json[1] 的 title 在标题行中的位置
    let firstQuestionTitle = data_json['1'].title;
    let matchIndex = -1;

    for (let i = 0; i < headerRow.length; i++) {
        // console.log(i + 1, headerRow[i].toString());
        // console.log(i + 1, firstQuestionTitle);
        // console.log(i + 1, similar(headerRow[i].toString(), firstQuestionTitle));
        if (headerRow[i] && ((similar(headerRow[i].toString(), firstQuestionTitle) > 0.25) ||
            (similar(headerRow[i].toString().includes('—') ? headerRow[i].toString().split('—')[0] : headerRow[i].toString(), firstQuestionTitle) > 0.25) ||
            (headerRow[i].toString().includes(firstQuestionTitle)))) {
            matchIndex = i;
            break;
        }
    }


    if (matchIndex === -1) {
        alert('匹配失败，无法在Excel标题行中找到第一题');
        return {success: false};
    }

    // 计算问卷解析中的总列数
    let expectedTotalColumns = 0;
    for (let key in data_json) {
        expectedTotalColumns += data_json[key].num;
    }

    // 检查从第一题开始到总列数是否与 Excel 标题行剩余列数一致
    if ((headerRow.length - matchIndex) !== expectedTotalColumns) {
        alert('匹配失败，Excel 标题行的列数与问卷解析的总列数不匹配');
        return {success: false};
    }

    // 按照题目的 num 值，计算每道题的预期列索引
    let cumulativeNum = 0;
    let unmatchedQuestions = [];
    let totalQuestions = Object.keys(data_json).length;

    for (let idx = 1; idx <= totalQuestions; idx++) {
        let key = idx.toString();
        let question = data_json[key];
        // 如果 questionType == 11，跳过此题，直接处理下一题
        if (question.type === '11') {
            cumulativeNum += question.num; // 仍然需要累加 num 值，以正确计算下一题的 expectedIndex
            continue;
        }

        let expectedIndex = matchIndex + cumulativeNum;
        cumulativeNum += question.num;

        // 检查是否超出标题行长度
        if (expectedIndex >= headerRow.length) {
            alert(`匹配失败，Excel列数不足以匹配所有题目。在第 ${key} 题处超出列数。`);
            return {success: false};
        }

        let headerCell = headerRow[expectedIndex];

        if (headerCell && headerCell.toString().includes(question.title)) {
            // 匹配成功，继续下一题
            continue;
        } else {
            if (similar(headerCell.toString(), question.title) > 0.25) {
                continue;
            } else {
                // 匹配失败，记录下来
                unmatchedQuestions.push(`第 ${key} 题（预期在第 ${expectedIndex + 1} 列）`);
            }
        }
    }

    if (unmatchedQuestions.length > 0) {
        alert(`匹配成功！不过以下题目与EXCEL表里对应题目内容差异较大，请人工检查一下，确认没问题可以继续操作\n${unmatchedQuestions.join('\n')}`);
    }else {
        alert('匹配成功！');
    }

    // if (unmatchedQuestions.length > 0) {
    //     alert(`以下题目未能匹配成功：\n${unmatchedQuestions.join('\n')}`);
    //     return {success: false};
    // } else {
    //     alert('匹配成功！');
    // }

    return {success: true, matchIndex: matchIndex};
}


// 初始化填写用时范围
window.onload = function () {
    try {
        document.getElementById('fill_time_start').value = 100;
        document.getElementById('fill_time_end').value = 200;
    } catch (e) {
        console.log("初始化填写时间失败");
    }
};
const hMyw0I = "DorweI1qz8jlRo6JERrqW9BoLM+NVH3eXnv2lCKVAtc="; // 必须是 Base64 编码的 32 字节密钥
// Excel文件上传处理
const dropZone = document.getElementById('drop_zone');
const fileInput = document.getElementById('excel_file');

dropZone.addEventListener('dragover', function (e) {
    e.preventDefault();
    dropZone.classList.add('active');
});

dropZone.addEventListener('dragleave', function () {
    dropZone.classList.remove('active');
});

dropZone.addEventListener('drop', function (e) {
    e.preventDefault();
    dropZone.classList.remove('active');
    const files = e.dataTransfer.files;
    if (files.length > 0 && (files[0].name.endsWith('.xlsx') || files[0].name.endsWith('.xls'))) {
        fileInput.files = e.dataTransfer.files;
        readExcelFile();
    } else {
        alert('只支持EXCEL文件 (.xlsx, .xls)');
    }
});

dropZone.addEventListener('click', function () {
    fileInput.click();
});

fileInput.addEventListener('change', function () {
    if (fileInput.files.length > 0) {
        readExcelFile();
    }
});
//打印EXCEL信息
fileInput.addEventListener('change', function (event) {
    const file = event.target.files[0];
    if (file && (file.name.endsWith('.xlsx') || file.name.endsWith('.xls'))) {
        const reader = new FileReader();
        reader.onload = function (e) {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, {type: 'array'});

            // 获取第一个工作表名称
            const firstSheetName = workbook.SheetNames[0];
            // 读取第一个工作表
            const worksheet = workbook.Sheets[firstSheetName];

            // 将工作表转换为 JSON 数据
            const excelData = XLSX.utils.sheet_to_json(worksheet, {header: 1});

            // 将数据打印到控制台
            // console.log("Excel 文件内容:", excelData);
        };
        reader.readAsArrayBuffer(file);
    } else {
        console.log("请选择一个有效的 Excel 文件 (.xlsx 或 .xls)");
    }
});

// 读取EXCEL文件内容
function readExcelFile() {
    const file = fileInput.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            const data = new Uint8Array(e.target.result);
            // 使用SheetJS库解析Excel文件
            const workbook = XLSX.read(data, {type: 'array'});
            const sheetName = workbook.SheetNames[0];
            const sheet = workbook.Sheets[sheetName];
            excelData = XLSX.utils.sheet_to_json(sheet, {header: 1});



            document.getElementById('file_name_display').textContent = `已上传文件: ${file.name}`;
            setDefaultRange();
        };
        reader.readAsArrayBuffer(file);
    }
}
function checkEmptyColumns() {
    let emptyColumns = new Set();
    for (let rowIndex = 1; rowIndex < excelData.length; rowIndex++) {
        const row = excelData[rowIndex];
        for (let colIndex = matchIndex; colIndex < row.length; colIndex++) {
            const cellValue = row[colIndex];
            if (cellValue === undefined || cellValue === null || cellValue === '') {
                emptyColumns.add(colIndex);
            }
        }
    }
    if (emptyColumns.size > 0) {
        const emptyColLetters = Array.from(emptyColumns)
            .map(col => XLSX.utils.encode_col(col))
            .join(', ');
        alert(`匹配成功！但是以下列存在空值: ${emptyColLetters}\n请检查上述几列是否是跳转题，如果是非填空题被跳转请手动改为-3，如果是填空题被跳转请手动改为(跳过)，详情见【必看教程】【EXCEL回填】部分！\n如果就是填空题答案为空，请忽略该提示,可以继续操作`);
    }
}
// 设置默认区间
function setDefaultRange() {
    const maxRows = excelData.length - 1; // 减去标题行
    document.getElementById('fill_range_start').value = 1;
    document.getElementById('fill_range_end').value = maxRows;
    document.getElementById('fill_range_end').max = maxRows;
}

// 更新提交来源比例
function updateRatio() {
    let mobileRatio = parseInt(document.getElementById('mobile_ratio').value);
    let linkRatio = parseInt(document.getElementById('link_ratio').value);
    let wechatRatio = 100 - mobileRatio - linkRatio;

    if (wechatRatio < 0) {
        document.getElementById('ratio_error').style.display = 'block';
    } else {
        document.getElementById('wechat_ratio').value = wechatRatio;
        document.getElementById('ratio_error').style.display = 'none';
    }

    document.getElementById('mobile_ratio_display').value = mobileRatio;
    document.getElementById('link_ratio_display').value = linkRatio;
    document.getElementById('wechat_ratio_display').value = wechatRatio;
}

// 验证填写用时范围
document.getElementById('fill_time_end').addEventListener('input', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;
    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        document.getElementById('error-message').style.display = 'block';
    } else {
        document.getElementById('error-message').style.display = 'none';
    }
});

document.getElementById('fill_time_end').addEventListener('blur', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;
    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        document.getElementById('fill_time_end').value = '';
    }
});

document.getElementById('fill_time_start').addEventListener('blur', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;
    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        document.getElementById('fill_time_start').value = '';
    }
});

; // 全局变量存储加载的数据
let cityToProvinceMap = {}; // 存储城市到省份的映射
// 声明全局布尔变量
let isCitySearchTriggered = false;
document.addEventListener('DOMContentLoaded', function () {
    // 获取当天日期标识（格式：YYYY-MM-DD）
    const getDayIdentifier = () => {
        const today = new Date();
        return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    };

    // 检查是否需要显示协议
    const shouldShowTerms = () => {
        const lastAgreedDayForEXCEL = localStorage.getItem('lastAgreedDayForEXCEL');
        const currentDay = getDayIdentifier();
        return !lastAgreedDayForEXCEL || lastAgreedDayForEXCEL !== currentDay;
    };

    if (shouldShowTerms()) {
        const termsModal = new bootstrap.Modal('#termsModal', {
            backdrop: 'static',
            keyboard: false
        });

        const agreeCheck = document.getElementById('agreeCheck');
        const confirmBtn = document.getElementById('confirmBtn');

        agreeCheck.addEventListener('change', function() {
            confirmBtn.disabled = !this.checked;
        });

        confirmBtn.addEventListener('click', function() {
            // 存储当天日期标识
            localStorage.setItem('lastAgreedDayForEXCEL', getDayIdentifier());
            termsModal.hide();
            document.querySelector('.modal-backdrop').remove();
            document.body.style.overflow = 'auto';
        });

        termsModal.show();
    } else {
        // 确保移除可能残留的遮罩层
        const existingBackdrop = document.querySelector('.modal-backdrop');
        if (existingBackdrop) {
            existingBackdrop.remove();
            document.body.style.overflow = 'auto';
        }
    }


    // 动态加载 province_city_mapping.json 文件
    fetch('province_city_mapping.json')  // 确保使用正确的文件路径
        .then(response => response.json())
        .then(data => {
            loadedMappingData = data; // 将加载的数据存储在全局变量中
            createCityToProvinceMap(data); // 创建城市到省份的映射
            initializeProvinceCityMapping(loadedMappingData);  // 使用加载的数据初始化下拉菜单
        })
        .catch(error => {
            console.error('加载省市映射数据时出错:', error);
        });

    // 添加搜索输入框的事件监听器
    document.getElementById('province_search').addEventListener('input', function () {
        isCitySearchTriggered = false;
        filterDropdown('province', this.value, 'province_list_backup');
    });

    document.getElementById('city_search').addEventListener('input', function () {
        // 用户开始通过搜索框搜索城市时，把标志位设为 true
        isCitySearchTriggered = true;
        filterCityDropdown(this.value);
    });

    // 添加清空搜索按钮的事件监听器
    document.getElementById('clear_province_search').addEventListener('click', function () {
        isCitySearchTriggered = false;
        document.getElementById('province_search').value = '';
        filterDropdown('province', '', 'province_list_backup');
    });

    document.getElementById('clear_city_search').addEventListener('click', function () {
        isCitySearchTriggered = false;
        document.getElementById('city_search').value = '';
        populateCityDropdown(); // 恢复城市下拉框的所有选项
    });

    // 监听回车键触发搜索
    document.getElementById('province_search').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            filterDropdown('province', this.value, 'province_list_backup');
        }
    });

    document.getElementById('city_search').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            filterCityDropdown(this.value);
        }
    });
});

// 创建城市到省份的映射
function createCityToProvinceMap(mappingData) {
    cityToProvinceMap = {}; // 初始化
    for (const province in mappingData) {
        const cities = mappingData[province].cities;
        for (const city in cities) {
            cityToProvinceMap[city] = province;
        }
    }
}

// 初始化省份和城市下拉菜单
function initializeProvinceCityMapping(mappingData) {
    const provinceSelect = document.getElementById('province');

    // 清空现有选项
    provinceSelect.innerHTML = '';

    // 更新省份和城市映射关系
    for (const province in mappingData) {
        const option = document.createElement('option');
        option.value = province;
        option.textContent = province;
        provinceSelect.appendChild(option);
    }

    // 备份完整的省份列表用于搜索
    provinceSelect.dataset.fullOptions = JSON.stringify(
        Array.from(provinceSelect.options).map(option => ({ value: option.value, text: option.text }))
    );

    // 在初始化时调用 updateCityDropdown，并传入加载的数据
    populateCityDropdown();  // 初始化城市下拉框

    // 省份选择框的事件监听器，改变省份时更新城市列表
    provinceSelect.addEventListener('change', function () {
        updateCityDropdown(); // 使用已加载的数据更新城市下拉框
    });
}

// 更新城市下拉框，根据选中的省份或搜索结果
function updateCityDropdown() {
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    const selectedProvince = provinceSelect.value;

    isCitySearchTriggered = false;

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 使用选中的省份更新城市下拉列表
    if (selectedProvince && loadedMappingData[selectedProvince]) {
        const cities = loadedMappingData[selectedProvince].cities;

        // 遍历城市对象，创建下拉选项
        for (const city in cities) {
            const option = document.createElement('option');
            option.value = cities[city]; // 使用城市的代码作为值
            option.textContent = city; // 显示城市名称
            citySelect.appendChild(option);
        }

        // 备份完整的城市列表用于搜索
        citySelect.dataset.fullOptions = JSON.stringify(
            Array.from(citySelect.options).map(option => ({ value: option.value, text: option.text }))
        );

        // 自动选择第一个城市
        if (Object.keys(cities).length > 0) {
            citySelect.value = Object.values(cities)[0]; // 选择第一个城市
        }
    }
}

// 恢复城市下拉框的所有选项
function populateCityDropdown() {
    const citySelect = document.getElementById('city');
    const provinceSelect = document.getElementById('province');
    const selectedProvince = provinceSelect.value;

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 使用选中的省份更新城市下拉列表
    if (selectedProvince && loadedMappingData[selectedProvince]) {
        const cities = loadedMappingData[selectedProvince].cities;

        // 遍历城市对象，创建下拉选项
        for (const city in cities) {
            const option = document.createElement('option');
            option.value = cities[city]; // 使用城市的代码作为值
            option.textContent = city; // 显示城市名称
            citySelect.appendChild(option);
        }

        // 备份完整的城市列表用于搜索
        citySelect.dataset.fullOptions = JSON.stringify(
            Array.from(citySelect.options).map(option => ({ value: option.value, text: option.text }))
        );

        // 自动选择第一个城市
        if (Object.keys(cities).length > 0) {
            citySelect.value = Object.values(cities)[0]; // 选择第一个城市
        }
    }
}

// 过滤省份下拉选项的通用函数
function filterDropdown(selectId, searchTerm, backupKey) {
    const selectElement = document.getElementById(selectId);
    const fullOptions = JSON.parse(selectElement.dataset.fullOptions || '[]');

    // 清空当前选项
    selectElement.innerHTML = '';

    // 过滤选项
    const filteredOptions = fullOptions.filter(option =>
        option.text.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // 添加过滤后的选项
    filteredOptions.forEach(optionData => {
        const option = document.createElement('option');
        option.value = optionData.value;
        option.textContent = optionData.text;
        selectElement.appendChild(option);
    });

    // 如果是省份下拉框，更新城市下拉框
    if (selectId === 'province') {
        updateCityDropdown();
    }
}

// 过滤城市下拉选项的函数，支持全局搜索
function filterCityDropdown(searchTerm) {
    const citySelect = document.getElementById('city');
    const provinceSelect = document.getElementById('province');
    const citySearch = searchTerm.toLowerCase();

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 遍历所有城市，找到匹配的
    for (const province in loadedMappingData) {
        const cities = loadedMappingData[province].cities;
        for (const city in cities) {
            if (city.toLowerCase().includes(citySearch)) {
                const option = document.createElement('option');
                option.value = cities[city]; // 使用城市的代码作为值
                option.textContent = city; // 显示城市名称
                option.dataset.province = province; // 存储所属省份
                citySelect.appendChild(option);
            }
        }
    }

    // 如果有匹配的城市，自动选择第一个
    if (citySelect.options.length > 0) {
        citySelect.value = citySelect.options[0].value;
    }

    // ==== 在这里添加一段监听器逻辑 ====
    citySelect.onchange = function() {

        // 如果不是由“城市搜索”触发，就什么也不做
        if (!isCitySearchTriggered) {
            return;
        }

        if (this.selectedIndex >= 0) {
            // 1. 获取当前选中的城市选项
            const selectedOption = this.options[this.selectedIndex];
            // 2. 读取城市所属的省份
            const selectedProvince = selectedOption.dataset.province;

            // --- 关键操作：恢复省份下拉框为完整列表 ---
            const provinceSelect = document.getElementById('province');
            const fullOptions = JSON.parse(provinceSelect.dataset.fullOptions || '[]');

            // 先清空
            provinceSelect.innerHTML = '';
            // 再把完整列表添加回去
            fullOptions.forEach(optionData => {
                const option = document.createElement('option');
                option.value = optionData.value;
                option.textContent = optionData.text;
                provinceSelect.appendChild(option);
            });

            // 3. 设置当前城市对应的省份
            provinceSelect.value = selectedProvince;
        }
    };
}

// 切换城市选择功能
function toggleCitySelection() {
    const ipSelection = document.getElementById('ip_selection');
    ipSelection.style.display = document.getElementById('change_ip_yes').checked ? 'block' : 'none';

    // 如果选择“否”，则清空已添加城市列表
    if (!document.getElementById('change_ip_yes').checked) {
        const cityList = document.getElementById('city_list');
        cityList.innerHTML = ''; // 清空城市列表
        document.getElementById('added_city_section').style.display = 'none'; // 隐藏已添加城市部分
    }
}

// 添加城市功能
function addCity() {
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    const cityList = document.getElementById('city_list');
    const addedCitySection = document.getElementById('added_city_section');

    let selectedProvince = provinceSelect.value;
    let selectedCity = citySelect.options[citySelect.selectedIndex].text;

    // 如果当前是通过搜索添加的城市，获取其所属省份
    if (citySelect.options[citySelect.selectedIndex].dataset.province) {
        selectedProvince = citySelect.options[citySelect.selectedIndex].dataset.province;
    }

    const displayCity = selectedProvince + '-' + selectedCity;

    if (selectedCity) {
        const listItem = document.createElement('li');
        listItem.className = 'list-group-item d-flex justify-content-between align-items-center'; // 保证列表项的样式
        listItem.textContent = displayCity;

        // 创建删除按钮
        const deleteButton = document.createElement('button');
        deleteButton.className = 'btn btn-danger btn-sm';
        deleteButton.textContent = '删除';
        deleteButton.onclick = function () {
            cityList.removeChild(listItem); // 删除城市
            if (cityList.children.length === 0) {
                addedCitySection.style.display = 'none'; // 隐藏已添加城市部分
            }
        };

        listItem.appendChild(deleteButton);
        cityList.appendChild(listItem);
        addedCitySection.style.display = 'block'; // 显示已添加城市部分
    }
}


function goBack() {
    window.location.href = 'createOrderStep1'; // 跳转到指定页面
}

// 计算总费用
function calculateTotalCost() {
    // 获取是否换IP的选项
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value;

    // 获取开始和结束范围
    const start = parseInt(document.getElementById('fill_range_start').value, 10);
    const end = parseInt(document.getElementById('fill_range_end').value, 10);
    const targetCount = end - start + 1;

    // 根据是否换IP确定单价
    const unitCostBefore200 = changeIp === 'yes' ? 0.08 : 0.06;  // 200 份之前的单价
    const unitCostAfter200 = changeIp === 'yes' ? 0.06 : 0.04;  // 200 份之后的单价

    // 计算 200 份之前和之后的费用
    const costBefore200 = Math.min(targetCount, 200) * unitCostBefore200;
    const costAfter200 = Math.max(0, targetCount - 200) * unitCostAfter200;

    // 计算总费用
    let totalCost = costBefore200 + costAfter200;

    // 应用最低消费 3 元
    if (totalCost < 3) {
        totalCost = 3; // 最低消费 3 元
    }
    return totalCost;
}


// 表单验证与提交
document.querySelector('.btn-submit').addEventListener('click', function (event) {
    event.preventDefault();


    // 验证是否上传了Excel文件
    if (excelData.length === 0) {
        alert("请上传Excel文件");
        return;
    }

    const surveyLink = document.getElementById('survey_link').value.trim();
    if (!urlPattern.test(surveyLink)) {
        alert('请输入有效的问卷链接！');
        document.getElementById('survey_link').focus();
        return;
    }

    const start = parseInt(document.getElementById('fill_range_start').value, 10);
    const end = parseInt(document.getElementById('fill_range_end').value, 10);
    const maxRows = excelData.length - 1; // 减去标题行

    if (isNaN(start) || isNaN(end) || start > end || start < 1 || end > maxRows) {
        alert('填写区间不合法，请重新设置！');
        return;
    }

    if (!isMatched) {
        alert("请先确保问卷链接解析和Excel文件匹配成功！");
        return;
    }

    // 验证填写的时间范围
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    if (parseInt(fillTimeStart) >= parseInt(fillTimeEnd) || !fillTimeStart || !fillTimeEnd) {
        alert("请填写正确的用时范围");
        document.getElementById('fill_time_start').focus();
        return;
    }
    if (parseInt(fillTimeEnd) <= 5) {
        if (!confirm("检测到设置最大的填写用时小于等于5秒，注意设置的填写用时是以秒为单位，请确定是否继续操作？")) {
            return;
        }
    }

    // 验证提交来源比例
    const mobileRatio = parseInt(document.getElementById('mobile_ratio_display').value, 10);
    const linkRatio = parseInt(document.getElementById('link_ratio_display').value, 10);
    const wechatRatio = parseInt(document.getElementById('wechat_ratio_display').value, 10);

    // 验证提交来源比例
    if (isNaN(mobileRatio) || isNaN(linkRatio) || isNaN(wechatRatio) ||
        mobileRatio < 0 || linkRatio < 0 || wechatRatio < 0 ||
        mobileRatio + linkRatio + wechatRatio !== 100) {
        alert("提交来源比例必须是大于等于0的整数，且三者之和必须为100%");
        document.getElementById('mobile_ratio').focus();
        return;
    }


    // 获取是否换IP的选项
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value;

    // 获取已添加城市并去掉“删除”按钮的文字
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item')).map(item => item.textContent.replace('删除', '').trim());

    // 如果选择了换IP，验证是否添加了城市
    if (changeIp === 'yes' && addedCities.length === 0) {
        alert("请点击添加按钮，至少添加一个换IP区域");
        document.getElementById('province').focus();
        return;
    }

    // 如果选择了换IP，并且换IP城市超过 40 个城市
    if (addedCities.length > 40) {
        alert('换IP城市不能超过 40 个城市，请删除一点');
        document.getElementById('province').focus();
        return;
    }

    // 构建订单信息
    let orderInfo = `
        <table style="width: 100%; border-collapse: collapse; line-height: 2; padding: 4px;">
            <tr>
                <td class="label-cell">问卷链接:</td>
                <td class="value-cell">${surveyLink}</td>
            </tr>
            <tr>
                <td class="label-cell">填写区间:</td>
                <td class="value-cell">第 ${start} 份 到 第 ${end} 份</td>
            </tr>
            <tr>
                <td class="label-cell">填写用时范围:</td>
                <td class="value-cell">${fillTimeStart} 秒 ~ ${fillTimeEnd} 秒</td>
            </tr>
            <tr>
                <td class="label-cell">提交来源比例:</td>
                <td class="value-cell">
                    <span style="color: #007bff;">手机提交: ${mobileRatio}%</span>
                    <span style="color: #007bff;">链接提交: ${linkRatio}%</span>
                    <span style="color: #007bff;">微信提交: ${wechatRatio}%</span>
                </td>
            </tr>
            <tr>
                <td class="label-cell">分散级别:</td>
                <td class="value-cell">${document.getElementById('fensan_level').value}</td>
            </tr>
            <tr>
                <td class="label-cell">是否换IP:</td>
                <td class="value-cell" style="color: ${changeIp === 'yes' ? 'red' : 'green'};">${changeIp}</td>
            </tr>
        </table>
    `;

    if (changeIp === 'yes' && addedCities.length > 0) {
        orderInfo += `
                <div style="font-size: 1.2rem; font-weight: bold; color: #007bff;">已添加地区:</div>
                <div style="margin-left: 10px; color: #333;">${addedCities.join(', ')}</div>
            `;
    }

    // 添加预计完成时间
    const fensanLevelToRange = {
        1: [2, 2], // 新的1级
        2: [5, 10],
        3: [10, 30],
        4: [30, 60],
        5: [60, 120],
        6: [120, 300],
        7: [180, 360], // 新的7级
        8: [240, 400],  // 新的8级
        11: [10, 60],
        12: [10, 120],
        13: [10, 180],
        14: [10, 240],
        15: [10, 300],
        16: [10, 360],
        17: [10, 420]
    };
    const fensanLevel = document.getElementById('fensan_level').value;
    const range = fensanLevelToRange[fensanLevel] || [5, 10];
    const medianTime = (range[0] + range[1]) / 2;
    const targetCount = end - start + 1;
    const estimatedTimeInMinutes = Math.round((targetCount * medianTime + targetCount * 5) / 60);
    const formattedTime = estimatedTimeInMinutes >= 60 ? `${Math.floor(estimatedTimeInMinutes / 60)} 小时 ${estimatedTimeInMinutes % 60} 分钟` : `${estimatedTimeInMinutes} 分钟`;

    orderInfo += `
            <div style="font-size: 1.2rem; font-weight: bold; color: #007bff;">预计需要时间:</div>
            <div style="margin-left: 10px; color: #333;">大约 ${formattedTime} 完成</div>
        `;

    // 计算费用
    const totalCost = calculateTotalCost();
    // 根据是否换IP确定单价
    const unitCostBefore200 = changeIp === 'yes' ? 0.08 : 0.06;
    const unitCostAfter200 = changeIp === 'yes' ? 0.06 : 0.04;

    orderInfo += `
            <div style="font-weight: bold; font-size: 1.5rem; color: red; margin-top: 10px;">
需要费用: ${totalCost <= 3 ? '3 元' : `${Math.min(targetCount, 200)} × ${unitCostBefore200} + ${Math.max(0, targetCount - 200)} × ${unitCostAfter200} = ${totalCost.toFixed(2)} 元`}
        <span style="font-weight: bold; font-size: 1.2rem; color: forestgreen;">
代币支付所需: ${totalCost.toFixed(2)} 代币  <a href="/tokenvault" target="_blank" style="margin-left: 10px; color: #007bff;">获取/充值代币码点这</a>
        </span>

<span style="color: #007bff; cursor: pointer;" onclick="toggleDetails()">[查看详情]</span>
            </div>
        `;

    // 计算详细费用过程并添加到 orderInfo
    orderInfo += `
            <div id="detailedInfo" style="display: none; margin-top: 1px; padding: 5px; background-color: #f0f0f0; border-radius: 5px;">
                <div style="font-size: 1rem; color: #333;">
                    <p><strong>单价计算规则:</strong> 是否换IP: ${changeIp === 'yes' ? '是' : '否'}</p>
                    <ul style="margin-left: 10px; padding-left: 10px; list-style: circle; margin-bottom: 0;">
                <li>前200份单价    换IP: 0.08 元/份， 不换IP: 0.06 元/份</li>
                <li>200份后单价    换IP: 0.06 元/份， 不换IP: 0.04 元/份</li>
                    </ul>
                    <p><strong>费用计算公式:</strong> 前200份费用 + 200份以后的费用</p>
                    <p><strong>详细计算过程:</strong></p>
                    <ul style="margin-left: 10px; padding-left: 10px; list-style: circle; margin-bottom: 0;">
                         <li>前 200 份部分: ${Math.min(targetCount, 200)} × ${unitCostBefore200} = ${(Math.min(targetCount, 200) * unitCostBefore200).toFixed(2)} 元</li>
                <li>超过 200 份部分: ${Math.max(0, targetCount - 200)} × ${unitCostAfter200} = ${(Math.max(0, targetCount - 200) * unitCostAfter200).toFixed(2)} 元</li>
                <li><strong>总费用（最低消费 3 元）:</strong> ${totalCost < 3 ? '3.00' : totalCost.toFixed(2)} 元</li>
                    </ul>
                </div>
            </div>
        `;

    // 显示确认模态框
    document.getElementById('previewMessage').innerHTML = orderInfo;
    document.getElementById('previewModal').style.display = 'block';
});

function toggleDetails() {
    const details = document.getElementById('detailedInfo');
    if (details.style.display === 'none') {
        details.style.display = 'block';
    } else {
        details.style.display = 'none';
    }
}

// 您可以根据需要添加支付和订单创建的逻辑

// 初始化 Bootstrap Tooltip
$(document).ready(function () {
    $('#changeIPTooltip').tooltip();
    $('#fillTimeTooltip').tooltip();
    $('#submitSourceTooltip').tooltip();
    $('#fensanTooltip').tooltip();
    $('#previewTooltip').tooltip();
    $('[data-bs-toggle="tooltip"]').tooltip();
});

// 关闭模态框
document.querySelector('.close').addEventListener('click', function () {
    document.getElementById('previewModal').style.display = 'none';
});

// 点击窗口外部关闭模态框
window.onclick = function (event) {
    const modal = document.getElementById('previewModal');
    if (event.target === modal) {
        modal.style.display = "none";
    }
};

// 取消按钮的点击事件
document.querySelector('.cancel-button').addEventListener('click', function () {
    document.getElementById('previewModal').style.display = "none"; // 关闭模态框
});

// 确认按钮的点击事件（在线支付）
document.querySelector('.confirm-button1').addEventListener('click', function () {
    // 比例填写正确，进行支付流程
    initiatePayment('alipay');
    document.getElementById('previewModal').style.display = "none"; // 关闭预览模态框
});

document.querySelector('.confirm-button2').addEventListener('click', function () {
    // 比例填写正确，进行支付流程
    initiatePayment('wxpay');
    document.getElementById('previewModal').style.display = "none"; // 关闭预览模态框
});

// 代币支付按钮点击事件
document.querySelector('.token-button').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'block';
    document.getElementById('previewModal').style.display = "none"; // 关闭预览模态框
});

// 动态加载动效的逻辑
function showLoadingSpinner() {
    // 创建并显示加载动效的 DOM 元素
    const spinner = document.createElement('div');
    spinner.id = 'loadingSpinner';

    // 整体背景样式
    spinner.style.position = 'fixed';
    spinner.style.top = '0';
    spinner.style.left = '0';
    spinner.style.width = '100%';
    spinner.style.height = '100%';
    spinner.style.backgroundColor = 'rgba(200, 200, 200, 0.8)'; // 浅灰色半透明背景
    spinner.style.zIndex = '1000';
    spinner.style.display = 'flex';
    spinner.style.justifyContent = 'center';
    spinner.style.alignItems = 'center';

    spinner.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);">
            <div class="spinner" style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <p style="margin-top: 10px; font-size: 16px; color: #333;">正在生成订单...请不要关闭页面！</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    document.body.appendChild(spinner);
}


function hideLoadingSpinner() {
    // 移除加载动效的 DOM 元素
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        document.body.removeChild(spinner);
    }
}

function showQueryingSpinner() {
    // 创建并显示查询动效的 DOM 元素
    const spinner = document.createElement('div');
    spinner.id = 'queryingSpinner';

    // 整体背景样式
    spinner.style.position = 'fixed';
    spinner.style.top = '0';
    spinner.style.left = '0';
    spinner.style.width = '100%';
    spinner.style.height = '100%';
    spinner.style.backgroundColor = 'rgba(200, 200, 200, 0.8)'; // 浅灰色半透明背景
    spinner.style.zIndex = '1000';
    spinner.style.display = 'flex';
    spinner.style.justifyContent = 'center';
    spinner.style.alignItems = 'center';

    spinner.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);">
            <div class="spinner" style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #ff5733; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <p style="margin-top: 10px; font-size: 16px; color: #333;">正在查询，请稍候...</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    document.body.appendChild(spinner);
}

function hideQueryingSpinner() {
    // 移除查询动效的 DOM 元素
    const spinner = document.getElementById('queryingSpinner');
    if (spinner) {
        document.body.removeChild(spinner);
    }
}

let paymentCountdownInterval;
const paymentTimeoutDuration = 3 * 60 * 1000; // 10分钟的支付时限（以毫秒为单位）
// 初始化支付流程
function initiatePayment(payType) {
    const identityCode = document.getElementById('identityCodeInput').value.trim();
    const totalCost = calculateTotalCost();
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value === 'yes';
    // const fillRangeStart = parseInt(document.getElementById('fill_range_start').value, 10);
    // const fillRangeEnd = parseInt(document.getElementById('fill_range_end').value, 10);
    // 获取其他表单数据
    const surveyLink = document.getElementById('survey_link').value;
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    const mobileRatio = document.getElementById('mobile_ratio_display').value;
    const linkRatio = document.getElementById('link_ratio_display').value;
    const wechatRatio = document.getElementById('wechat_ratio_display').value;
    const fensanLevel = document.getElementById('fensan_level').value;

    // 从填写区间指定的行开始遍历Excel数据
    const startRow = parseInt(document.getElementById('fill_range_start').value, 10) - 1;
    const endRow = parseInt(document.getElementById('fill_range_end').value, 10) - 1;
    // 获取已添加城市列表并组合成字符串
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
        .map(item => item.textContent.replace('删除', '').trim());
    const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔

    const answerList = getSubmitData().slice(startRow, endRow + 1);
    const iv = CryptoJS.enc.Utf8.parse("0000000000000000");

// 确保密钥正确解析为 32 字节
    const key = CryptoJS.enc.Base64.parse(hMyw0I); // 使用 Base64 解析密钥

    const encryptedAnswer = CryptoJS.AES.encrypt(
        JSON.stringify(answerList),
        key, // 直接传入解析后的密钥（WordArray）
        {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }
    ).toString();

    // 创建表单数据对象
    const formData = {
        surveyLink: surveyLink,
        ipArea: ipArea, // 已添加的所有城市
        fensanLevel: fensanLevel,
        sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
        tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
        encryptedAnswer: encryptedAnswer, // 将 answerList 添加到表单数据
        activationCode: identityCode,
        price: totalCost,
        changeIp: changeIp,
        fillRangeStart: startRow,
        fillRangeEnd: endRow,
        payType: payType,
        type: 3 // 指定为 EXCEL 回填模式
    };

    if (totalCost <= 0) {
        alert('无效的支付金额');
        return;
    }
    showLoading();
    // 向后端请求生成支付二维码
    fetch('generate-payment-order-safety-excel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
        .then(response => response.json())
        .then(data => {
            if (data.outTradeNo && data.paymentImage) {
                // 显示支付二维码模态框
                showPaymentModal(data.paymentImage, totalCost.toFixed(2),payType);
                // 开始轮询支付状态
                startPaymentStatusPolling(data.outTradeNo);
            } else {
                alert('生成支付信息失败，请重试。');
            }
        })
        .catch(error => {
            console.error('生成支付信息时发生错误:', error);
            alert('生成支付信息时发生错误，请稍后重试。');
        })
        .finally(() => {
            hideLoading();
        });
}


function showPaymentModal(paymentImageUrl, amount,payType) {
    const paymentModal = document.getElementById('paymentModal');
    document.getElementById('payment-qr-code').src = paymentImageUrl;
    document.getElementById('payment-amount').textContent = amount;
    paymentModal.style.display = 'block';

    const titleElement = paymentModal.querySelector('h2');
    const qrCodeImg = document.getElementById('payment-qr-code');
    // 根据支付类型设置内容
    if(payType === 'wxpay') {
        titleElement.textContent = '请使用微信扫码支付';
        qrCodeImg.alt = '微信支付二维码';
        titleElement.style.color = '#07C160'; // 微信绿色
        // 更换微信支付LOGO（需要准备微信图标）
    } else {
        titleElement.textContent = '请使用支付宝扫码支付';
        qrCodeImg.alt = '支付宝支付二维码';
        titleElement.style.color = '#007BFF'; // 微信绿色
    }

    // 启动倒计时
    startPaymentCountdown(paymentTimeoutDuration);
}

let paymentStatusCheckInterval;

function startPaymentStatusPolling(outTradeNo) {
    let pollingAttempts = 0;
    const maxPollingAttempts = 30; // 最大轮询次数
    const pollingInterval = 4000; // 轮询间隔（毫秒）

    paymentStatusCheckInterval = setInterval(function () {
        fetch(`checkPaymentForOrderSafety?outTradeNo=${outTradeNo}`, {
            method: 'GET'
        })
            .then(response => response.json())
            .then(data => {
                if (data.is_pay === 1) {
                    clearInterval(paymentStatusCheckInterval);
                    document.getElementById('paymentModal').style.display = 'none';
                    alert('付款成功！订单已生成，点击确定打开查询订单页面（如果没有弹出查询页面，可以在首页点击查询已付款订单手动查询）');
                    window.location.href = `queryOrder?ordernum=${outTradeNo}`; // 在当前页面跳转到查询页面
                } else if (data.is_pay === 0) {
                    // 支付未完成，继续轮询
                    pollingAttempts++;
                    if (pollingAttempts >= maxPollingAttempts) {
                        clearInterval(paymentStatusCheckInterval);
                        handlePaymentTimeout();
                    }
                } else {
                    console.log("发生错误，需要处理这种情况");
                }
            })
            .catch(error => {
                console.error('检查支付状态时发生错误:', error);
            });
    }, pollingInterval);
}

// 关闭支付模态框
document.querySelector('.close-payment').addEventListener('click', function () {
    document.getElementById('paymentModal').style.display = 'none';
    clearInterval(paymentStatusCheckInterval); // 停止轮询
});

// 代币支付代币激活码输入模态框的关闭事件
document.querySelector('.close-token-input').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'none';
});

// 代币支付代币激活码确认按钮点击事件
document.querySelector('.confirm-identity-code').addEventListener('click', function () {
    const identityCode = document.getElementById('identityCodeInput').value.trim();
    ;
    //显示正在查询动效
    showQueryingSpinner();
    if (identityCode) {
        // 发送请求到后端，查询代币激活码相关的代币信息
        fetch('tokenvault/getTokenInfo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({activationCode: identityCode})
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    //关闭正在查询动效
                    hideQueryingSpinner();
                    // 显示查询到的代币激活码相关信息
                    document.getElementById('identityCodeInfo').textContent = `${identityCode}，余额: ${data.tokenBalance} 代币`;
                    document.getElementById('tokenInputModal').style.display = 'none';
                    document.getElementById('tokenPaymentConfirmModal').style.display = 'block';
                } else {
                    //关闭正在查询动效
                    hideQueryingSpinner();
                    alert('代币激活码无效或未找到，请重新输入。');
                }
            })
            .catch(error => {
                //关闭正在查询动效
                hideQueryingSpinner();
                console.error('查询代币激活码时发生错误:', error);
                alert('查询代币激活码时发生错误，请稍后重试。');
            });
    } else {
        //关闭正在查询动效
        hideQueryingSpinner();
        alert('请输入有效的代币激活码');
    }
});

// 代币支付确认模态框的关闭事件
document.querySelector('.close-token-confirm').addEventListener('click', function () {
    document.getElementById('tokenPaymentConfirmModal').style.display = 'none';
});

// 最终确认代币支付按钮点击事件
document.querySelector('.final-confirm-token-payment').addEventListener('click', function () {
    const identityCode = document.getElementById('identityCodeInput').value.trim();
    const totalCost = calculateTotalCost();
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value === 'yes';
    // const fillRangeStart = parseInt(document.getElementById('fill_range_start').value, 10);
    // const fillRangeEnd = parseInt(document.getElementById('fill_range_end').value, 10);
    // 获取其他表单数据
    const surveyLink = document.getElementById('survey_link').value;
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    const mobileRatio = document.getElementById('mobile_ratio_display').value;
    const linkRatio = document.getElementById('link_ratio_display').value;
    const wechatRatio = document.getElementById('wechat_ratio_display').value;
    const fensanLevel = document.getElementById('fensan_level').value;

    // 从填写区间指定的行开始遍历Excel数据
    const startRow = parseInt(document.getElementById('fill_range_start').value, 10) - 1;
    const endRow = parseInt(document.getElementById('fill_range_end').value, 10) - 1;
    // 获取已添加城市列表并组合成字符串
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
        .map(item => item.textContent.replace('删除', '').trim());
    const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔

    const answerList = getSubmitData().slice(startRow, endRow + 1);
    // 加密answerList（密钥由后端提供，例如固定值）

    const iv = CryptoJS.enc.Utf8.parse("0000000000000000");
    const key = CryptoJS.enc.Base64.parse(hMyw0I); // 使用 Base64 解析密钥

    const encryptedAnswer = CryptoJS.AES.encrypt(
        JSON.stringify(answerList),
        key, // 直接传入解析后的密钥（WordArray）
        {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }
    ).toString();

    // 创建表单数据对象
    const formData = {
        surveyLink: surveyLink,
        ipArea: ipArea, // 已添加的所有城市
        fensanLevel: fensanLevel,
        sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
        tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
        // answerList: getSubmitData().slice(startRow, endRow + 1), // 将 answerList 添加到表单数据
        encryptedAnswer: encryptedAnswer,
        activationCode: identityCode,
        price: totalCost,
        changeIp: changeIp,
        fillRangeStart: startRow,
        fillRangeEnd: endRow,
        type: 3 // 指定为 EXCEL 回填模式
    };

    if (!identityCode) {
        alert('请输入有效的代币激活码');
        return;
    }

    if (totalCost <= 0) {
        alert('无效的支付金额');
        return;
    }

    // 显示加载动效
    showLoadingSpinner();
    // 发送代币支付请求到后端
    fetch('tokenvault/payWithTokenForExcel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const orderNumber = data.orderNumber; // 获取生成的订单号
                document.getElementById('tokenPaymentConfirmModal').style.display = 'none';
                // 隐藏加载动效
                hideLoadingSpinner();
                alert('订单生成成功！（如果没有弹出查询页面，可以在首页点击查询代币码信息手动查询）');
                window.location.href = `/queryOrder?ordernum=${orderNumber}`; // 在当前页面跳转到查询页面
            } else {
                // 隐藏加载动效
                hideLoadingSpinner();
                alert(data.message || '订单创建失败，请重试。');
            }
        })
        .catch(error => {
            // 隐藏加载动效
            hideLoadingSpinner();
            console.error('代币支付时发生错误:', error);
            alert('代币支付时发生错误，请稍后重试。');
        });
});


// 代币支付取消按钮点击事件
document.querySelector('.cancel-token-payment').addEventListener('click', function () {
    document.getElementById('tokenPaymentConfirmModal').style.display = 'none';
});

// 代币支付代币激活码输入取消按钮点击事件
document.querySelector('.cancel-identity-code').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'none';
});

// 模拟发送请求到后端创建订单的函数
// function createOrder() {
//     // 获取其他表单数据
//     const surveyLink = document.getElementById('survey_link').value;
//     const fillTimeStart = document.getElementById('fill_time_start').value;
//     const fillTimeEnd = document.getElementById('fill_time_end').value;
//     const mobileRatio = document.getElementById('mobile_ratio_display').value;
//     const linkRatio = document.getElementById('link_ratio_display').value;
//     const wechatRatio = document.getElementById('wechat_ratio_display').value;
//     const fensanLevel = document.getElementById('fensan_level').value;
//
//     // 从填写区间指定的行开始遍历Excel数据
//     const startRow = parseInt(document.getElementById('fill_range_start').value, 10) - 1;
//     const endRow = parseInt(document.getElementById('fill_range_end').value, 10) - 1;
//
//     // 获取已添加城市列表并组合成字符串
//     const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
//         .map(item => item.textContent.replace('删除', '').trim());
//     const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔
//
//     // 创建表单数据对象
//     const formData = {
//         surveyLink: surveyLink,
//         ipArea: ipArea, // 已添加的所有城市
//         fensanLevel: fensanLevel,
//         sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
//         tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
//         price: calculateTotalCost(),
//         answerList: getSubmitData().slice(startRow, endRow + 1) // 将 answerList 添加到表单数据
//     };
//
//     // 发送请求到后端创建订单
//     fetch('order/createOrderByEXCEL', {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json' // 指定请求体为 JSON 格式
//         },
//         body: JSON.stringify(formData) // 将数据转换为 JSON 字符串
//     })
//         .then(response => response.json())
//         .then(data => {
//             if (data.success) {
//                 const orderNumber = data.orderNumber; // 获取生成的订单号
//                 console.log(`订单创建成功，订单号: ${orderNumber}`);
//                 // 关闭支付模态框
//                 document.getElementById('paymentModal').style.display = 'none';
//                 // 隐藏加载动效
//                 hideLoadingSpinner();
//                 alert('订单生成成功！点击确定跳转到查询订单页面（如果没有弹出查询页面，可以在首页点击查询已付款订单手动查询）');
//                 window.location.href = `queryOrder?ordernum=${orderNumber}`; // 在当前页面跳转到查询页面
//             } else {
//                 // 隐藏加载动效
//                 hideLoadingSpinner();
//                 alert('订单创建失败，请重试。');
//             }
//         })
//         .catch(error => {
//             // 隐藏加载动效
//             hideLoadingSpinner();
//             console.error('创建订单时发生错误:', error);
//             alert('创建订单时发生错误，请稍后重试。');
//         });
// }


// 调用代币订单创建的函数
// function createOrderForDB(identityCode) {
//     // 获取其他表单数据
//     const surveyLink = document.getElementById('survey_link').value;
//     const fillTimeStart = document.getElementById('fill_time_start').value;
//     const fillTimeEnd = document.getElementById('fill_time_end').value;
//     const mobileRatio = document.getElementById('mobile_ratio_display').value;
//     const linkRatio = document.getElementById('link_ratio_display').value;
//     const wechatRatio = document.getElementById('wechat_ratio_display').value;
//     const fensanLevel = document.getElementById('fensan_level').value;
//     // 从填写区间指定的行开始遍历Excel数据
//     const startRow = parseInt(document.getElementById('fill_range_start').value, 10) - 1;
//     const endRow = parseInt(document.getElementById('fill_range_end').value, 10) - 1;
//
//     // 获取已添加城市列表并组合成字符串
//     const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
//         .map(item => item.textContent.replace('删除', '').trim());
//     const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔
//
//     // 创建表单数据对象
//     const formData = {
//         surveyLink: surveyLink,
//         ipArea: ipArea, // 已添加的所有城市
//         fensanLevel: fensanLevel,
//         sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
//         tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
//         price: calculateTotalCost(),
//         activationCode: identityCode,
//         answerList: getSubmitData().slice(startRow, endRow + 1) // 将 answerList 添加到表单数据
//     };
//
//     // 发送请求到后端创建代币支付订单
//     fetch('order/createOrderByEXCELForDB', {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json' // 指定请求体为 JSON 格式
//         },
//         body: JSON.stringify(formData) // 将数据转换为 JSON 字符串
//     })
//         .then(response => response.json())
//         .then(data => {
//             if (data.success) {
//                 const orderNumber = data.orderNumber; // 获取生成的订单号
//                 console.log(`订单创建成功，订单号: ${orderNumber}`);
//                 // 隐藏加载动效
//                 hideLoadingSpinner();
//                 alert('订单生成成功！（如果没有弹出查询页面，可以在首页点击查询代币码信息手动查询）');
//                 window.location.href = `/queryOrder?ordernum=${orderNumber}`; // 在当前页面跳转到查询页面
//             } else {
//                 // 隐藏加载动效
//                 hideLoadingSpinner();
//                 alert(data.message || '订单创建失败，请重试。');
//             }
//         })
//         .catch(error => {
//             // 隐藏加载动效
//             hideLoadingSpinner();
//             console.error('创建订单时发生错误:', error);
//             alert('创建订单时发生错误，请稍后重试。');
//         });
// }

function startPaymentCountdown(duration) {
    const paymentCountdownElement = document.getElementById('payment-countdown');
    let timeRemaining = duration;

    // 清除任何现有的倒计时
    clearInterval(paymentCountdownInterval);

    // 立即更新倒计时显示
    updateCountdownDisplay(timeRemaining);

    paymentCountdownInterval = setInterval(function () {
        timeRemaining -= 1000; // 每次减少1秒（1000毫秒）

        if (timeRemaining <= 0) {
            clearInterval(paymentCountdownInterval);
            paymentCountdownElement.textContent = '00:00';
            alert('支付超时，请重新支付。');
            // 关闭支付模态框
            document.getElementById('paymentModal').style.display = 'none';
            // 停止支付状态轮询
            clearInterval(paymentStatusCheckInterval);
        } else {
            updateCountdownDisplay(timeRemaining);
        }
    }, 1000); // 每秒更新一次
}

function updateCountdownDisplay(timeRemaining) {
    const paymentCountdownElement = document.getElementById('payment-countdown');
    const totalSeconds = Math.floor(timeRemaining / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    // 格式化分钟和秒数，确保两位数显示
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
    const formattedSeconds = seconds < 10 ? '0' + seconds : seconds;

    paymentCountdownElement.textContent = formattedMinutes + ':' + formattedSeconds;
}

function showLoading() {
    document.getElementById('globalLoading').classList.remove('d-none');
}

function hideLoading() {
    document.getElementById('globalLoading').classList.add('d-none');
}


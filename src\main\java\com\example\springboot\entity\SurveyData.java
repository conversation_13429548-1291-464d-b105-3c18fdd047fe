package com.example.springboot.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL) // 忽略空字段
@Data
public class SurveyData {
    private int numId;  //题号
    private String title; //题目
    private String type;//题型编号
    private String typeInfo;//题型文字描述
    @JsonIgnore
    private String html;//题目对应html源码，含有题目和选项内容信息
    private List<String> options; // 题目选项
    private List<SubQuestion> subQuestions; // 矩阵题的小题信息
    private List<Integer> colIndices; // 题目对应的Excel列号
    
    // 矩阵题小题信息类
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SubQuestion {
        private String title; // 小题标题
        private List<String> options; // 小题选项
    }
}

/*
    type
    1,2对应【填空题】
    规则解释：传入的文本内容就是对于的文本答案
    3对应【单选题】
    规则解释：序号是几就代表选择了第几个选项，如果序号数字后面带有^文本内容，比如2^张三，代表选择了第2个选项，并且填写了张三作为选择的文本答案，如果答案是-3表示前面选择了什么选择导致被跳转了，如果答案是-2，表示此题被隐藏了，如果为空说明此题不是必答题，并且没有作答本题
    4对应【多选题】
    规则解释：序号为0表示本选项没有选，1代表选择了本选项，比如答案是1  0   1   0表示选项了第1和3两个选项，如果序号数字后面带有^文本内容，比如1^张三，代表对应选项选择了此选项，并且填写了张三作为选择的文本答案，如果答案是-3表示前面选择了什么选择导致被跳转了，如果答案是-2，表示此题被隐藏了，如果为空说明此题不是必答题，并且没有作答本题
    5对应【单项量表题】
    规则解释：和单选题一样的填写规则，但是是特定量表题使用的
    6single对应【矩阵单选题】
    规则解释：和单选题一样的填写规则，不过由多个单选题的答案组成，比如答案是2  1   3   2表示矩阵量表大题中，第1小题选择了第2个选项，第2小题选择了第1个选项，第3小题选择了第3个选项，第4小题选择了第2个选项
    6multiple对应【矩阵多选题】
    规则解释：和多选题一样的填写规则，不过由多个多选题的答案组成
    7对应【下拉题】
    规则解释：和单选题一样的填写规则
    8对应【单项滑条题】
    规则解释：填写的是滑条具体的值
    11对应【排序题】
    规则解释：输入的对应选项选择的次序，比如输入的事2   1   4^张三   3，表示第2个选项第一个被选出来，第1个选项被第二个选出来，第4个选项被第三个选出来，第3个选项被第四个选出来，并且附加了张三作为文本答案
    9duotian1对应【多填空题1】
    规则解释：和填空题一样的填写规则，不过由多个填空题的答案组成
    9duotian2对应【多填空题2】
    规则解释：和填空题一样的填写规则，不过由多个填空题的答案组成
    9hua对应【矩阵滑条题】
    规则解释：和单项滑条题一样的填写规则，不过由多个单项滑条题的答案组成
    12对应【比重滑条题】
    规则解释：和单项滑条题一样的填写规则，不过由多个单项滑条题的答案组成，并且加起来必须满足一个固定值，比如都加起来为100
    */

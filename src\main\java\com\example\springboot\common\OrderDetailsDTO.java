package com.example.springboot.common;

import lombok.Data;

@Data
public class OrderDetailsDTO {
    private int targetCount;        // 目标份数
    private String surveyLink;      // 问卷链接
    private String ipArea;          // IP区域
    private int fensanLevel;        // 分散提交等级
    private String sourceBili;      // 来源比例
    private String tianxieTime;     // 填写用时，例如 "100,200"
    private int realCompletedCount; // 实际已完成的数量
    private int orderStatus;        // 订单状态

    private String remark; // 备注
    private String message; // 给用户看的信息

    private String threadLock; // python生成submitdata时候使用的锁
}

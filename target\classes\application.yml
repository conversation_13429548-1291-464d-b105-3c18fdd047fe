# Spring 应用配置
spring:
  # 应用基础配置
  application:
    # 应用名称 - 用于服务注册和发现
    name: MyWenJuanXing_Management2
  
  # AI 相关配置（此处配置的是对接阿里云DashScope的OpenAI兼容模式）
  ai:
    openai:
      # API基础地址 - 阿里云DashScope的兼容模式端点
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      # API密钥 - 用于身份验证（注意：实际项目中应通过安全方式存储）
      api-key: sk-d5a6912ad9034e548ab5b0380e3ff849

      # 聊天模型配置
      chat:
        options:
          # 使用的模型名称 - 阿里云通义千问的最新最大模型
          # model: deepseek-v3
          model: qwen-max-latest
          # model: qwen-turbo-latest
          # model: qwen-plus-latest
          # 温度参数 - 控制生成文本的随机性（0-1，越高越随机）
          temperature: 0.5
          # 最大token数 - 限制生成内容长度
          # max-tokens: 8192
          # 核心采样率 - 控制生成多样性的参数
          # top-p: 0.95
          # 是否启用流式响应
          # stream: false

      # 连接配置
      connection:
        # 连接超时时间（毫秒）
        connect-timeout: 30000
        # 读取超时时间（毫秒）
        read-timeout: 120000
        # 写入超时时间（毫秒）
        write-timeout: 30000
        # 最大连接数
        max-connections: 100
        # 每个路由的最大连接数
        max-connections-per-route: 20

  # Spring Boot 主配置
  main:
    # 允许覆盖bean定义 - 开发环境常用配置
    allow-bean-definition-overriding: true
    # 关闭启动banner显示
    banner-mode: off

# 重试配置
resilience4j:
  retry:
    instances:
      ai-api:
        # 最大重试次数
        max-attempts: 3
        # 重试间隔时间（毫秒）
        wait-duration: 2000
        # 启用指数退避
        enable-exponential-backoff: true
        # 指数退避倍数
        exponential-backoff-multiplier: 2
        # 重试的异常类型
        retry-exceptions:
          - org.springframework.web.reactive.function.client.WebClientRequestException
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - java.io.IOException
  
  # Servlet 配置
  servlet:
    encoding:
      # 字符编码设置
      charset: UTF-8
      enabled: true
      # 强制使用指定编码
      force: true

# 日志配置
logging:
  level:
    # 设置特定包的日志级别
    org.springframework.ai.chat.client.advisor: debug  # AI客户端顾问调试日志
    com.example.springboot: debug  # 应用代码调试日志
package com.example.springboot.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class AiChatMessage {
    private Long id;
    private String sessionId;  // 改为String类型，存储UUID
    private String role;  // user, assistant, system
    private String content;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    private Integer messageOrder;  // 添加消息顺序字段
    
    // 数据修改记录，JSON格式
    private String dataModifications;

    // 配置文本，存储分维度调整量表的配置信息（JSON格式）
    private String configText;

    // 完整的Excel数据
    private String completeExcelData;
    
    // 添加数据修改记录
    private List<DataModification> dataModificationsList;
    
    // 添加表格数据字段
    private String tableData; // 存储表格数据的JSON字符串
    
    @Data
    public static class DataModification {
        private Integer row;
        private Integer col;
        private String oldValue;
        private String newValue;
        private String type; // "update" "addRow" "deleteRow" 等
        private String columnName;
        private LocalDateTime modifyTime;

        public void setModifyTime(LocalDateTime modifyTime) {
            this.modifyTime = modifyTime;
        }
    }

    public void onCreate() {
        this.createTime = LocalDateTime.now();
    }
} 
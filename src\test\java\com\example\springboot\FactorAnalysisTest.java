package com.example.springboot;

import org.apache.commons.math3.linear.*;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import java.util.*;
import java.util.stream.*;

public class FactorAnalysisTest {

  // 因子分析测试方法
  public static FactorAnalysisOutput performFactorAnalysisTest(List<List<Double>> inputData, Integer factors) {
    try {
      int variableCount = inputData.size();
      int sampleSize = inputData.get(0).size();

      // 数据标准化
      List<List<Double>> standardizedData = standardizeData(inputData);

      // 计算相关矩阵
      RealMatrix correlationMatrix = calculateCorrelationMatrix(standardizedData);

      // 特征值分解
      EigenDecomposition eigenDecomposition = new EigenDecomposition(correlationMatrix);
      double[] eigenvalues = eigenDecomposition.getRealEigenvalues();

      // 确定因子数量
      int numberOfFactors = factors != null ? factors : countEigenvaluesGreaterThanOne(eigenvalues);
      double totalVariance = Arrays.stream(eigenvalues).sum();

      // 提取未旋转因子载荷
      RealMatrix eigenvectors = eigenDecomposition.getV();
      double[][] loadings = new double[variableCount][numberOfFactors];
      for (int i = 0; i < variableCount; i++) {
        for (int j = 0; j < numberOfFactors; j++) {
          loadings[i][j] = eigenvectors.getEntry(i, j) * Math.sqrt(eigenvalues[j]);
        }
      }

      // 进行Varimax旋转 - 增加迭代次数和容差
      double[][] rotatedLoadings = varimax(loadings, 1000, 1e-5);
      for (int j = 0; j < numberOfFactors; j++) {
        double sum = 0.0;
        for (int i = 0; i < variableCount; i++) {
            sum += rotatedLoadings[i][j];
        }
        if (sum < 0) {
            for (int i = 0; i < variableCount; i++) {
                rotatedLoadings[i][j] = -rotatedLoadings[i][j];
            }
        }
    }
      // 计算旋转后特征根
      double[] rotatedEigenvalues = new double[numberOfFactors];
      for (int j = 0; j < numberOfFactors; j++) {
        double sum = 0.0;
        for (int i = 0; i < variableCount; i++) {
          sum += Math.pow(rotatedLoadings[i][j], 2);
        }
        rotatedEigenvalues[j] = sum;
      }

      // 按特征根降序排序
      List<Integer> sortedIndices = IntStream.range(0, numberOfFactors)
          .boxed()
          .sorted(Comparator.comparingDouble(i -> -rotatedEigenvalues[i]))
          .collect(Collectors.toList());

      // 重新排序载荷矩阵和特征根
      double[][] sortedRotatedLoadings = new double[variableCount][numberOfFactors];
      double[] sortedRotatedEigenvalues = new double[numberOfFactors];
      for (int j = 0; j < numberOfFactors; j++) {
        int origIndex = sortedIndices.get(j);
        sortedRotatedEigenvalues[j] = rotatedEigenvalues[origIndex];
        for (int i = 0; i < variableCount; i++) {
          sortedRotatedLoadings[i][j] = rotatedLoadings[i][origIndex];
        }
      }

      // 构建输出结果
      FactorAnalysisOutput output = new FactorAnalysisOutput();
      output.setFactors(numberOfFactors);

      // 设置旋转前数据
      output.setTzgBefore(Arrays.stream(eigenvalues).boxed().limit(numberOfFactors).collect(Collectors.toList()));
      List<Double> varianceExplainedBefore = new ArrayList<>();
      List<Double> cumulativeBefore = new ArrayList<>();
      double cumul = 0.0;
      for (int i = 0; i < numberOfFactors; i++) {
        double var = eigenvalues[i] / totalVariance;
        varianceExplainedBefore.add(var);
        cumul += var;
        cumulativeBefore.add(cumul);
      }
      output.setFcBefore(varianceExplainedBefore);
      output.setLjfcBefore(cumulativeBefore);

      // 设置旋转后数据
      output.setTzgAfter(Arrays.stream(sortedRotatedEigenvalues).boxed().collect(Collectors.toList()));
      List<Double> rotatedVariance = new ArrayList<>();
      List<Double> rotatedCumulative = new ArrayList<>();
      cumul = 0.0;
      for (double ev : sortedRotatedEigenvalues) {
        double var = ev / totalVariance;
        rotatedVariance.add(var);
        cumul += var;
        rotatedCumulative.add(cumul);
      }
      output.setFcAfter(rotatedVariance);
      output.setLjfcAfter(rotatedCumulative);

      // 构建因子分析项
      List<FactorAnalysisOutput.FactorAnalysisItem> items = new ArrayList<>();
      for (int i = 0; i < variableCount; i++) {
        FactorAnalysisOutput.FactorAnalysisItem item = new FactorAnalysisOutput.FactorAnalysisItem();
        item.setTitle("标题" + (i + 1));

        List<Double> factorLoadings = new ArrayList<>();
        double communality = 0.0;
        for (int j = 0; j < numberOfFactors; j++) {
          double loading = sortedRotatedLoadings[i][j];
          factorLoadings.add(loading);
          communality += loading * loading;
        }
        item.setFators(factorLoadings);
        item.setHValue(communality);
        items.add(item);
      }
      output.setItems(items);

      return output;

    } catch (Exception e) {
      throw new RuntimeException("因子分析测试异常: " + e.getMessage(), e);
    }
  }

  // Varimax旋转实现 - 修复收敛问题
  // 修改后的Varimax旋转实现
  // 修改1: 添加Kaiser归一化处理
  private static double[][] varimax(double[][] loadings, int maxIter, double epsilon) {
    int nVars = loadings.length;
    int nFactors = loadings[0].length;

    // 计算初始共同度
    double[] h = new double[nVars];
    for (int i = 0; i < nVars; i++) {
      double sumSq = 0.0;
      for (int j = 0; j < nFactors; j++) {
        sumSq += loadings[i][j] * loadings[i][j];
      }
      h[i] = sumSq;
    }

    // Kaiser归一化
    double[][] normalized = new double[nVars][nFactors];
    for (int i = 0; i < nVars; i++) {
      double sqrtH = Math.sqrt(h[i]);
      for (int j = 0; j < nFactors; j++) {
        normalized[i][j] = (sqrtH > 1e-10) ? loadings[i][j] / sqrtH : 0.0;
      }
    }

    double[][] rotated = Arrays.stream(normalized).map(double[]::clone).toArray(double[][]::new);

    // 修改2: 使用标准Varimax角度计算公式
    int iter = 0;
    double diff;
    do {
      diff = 0.0;
      for (int j = 0; j < nFactors - 1; j++) {
        for (int k = j + 1; k < nFactors; k++) {
          double A = 0.0, B = 0.0, C = 0.0, D = 0.0;

          for (int i = 0; i < nVars; i++) {
            double a = rotated[i][j];
            double b = rotated[i][k];
            double u = a * a - b * b;
            double v = 2 * a * b;
            A += u;
            B += v;
            C += u * u - v * v;
            D += 2 * u * v;
          }

          // 标准Varimax角度计算公式
          double numerator = D - 2 * A * B / nVars;
          double denominator = C - (A * A - B * B) / nVars;
          double angle = (Math.abs(denominator) < 1e-10) ? 0.0 : 0.25 * Math.atan2(numerator, denominator);

          double cos = Math.cos(angle);
          double sin = Math.sin(angle);

          double maxChange = 0.0;
          for (int i = 0; i < nVars; i++) {
            double a = rotated[i][j];
            double b = rotated[i][k];
            double newA = a * cos + b * sin;
            double newB = -a * sin + b * cos;

            maxChange = Math.max(maxChange, Math.abs(newA - a));
            maxChange = Math.max(maxChange, Math.abs(newB - b));

            rotated[i][j] = newA;
            rotated[i][k] = newB;
          }
          diff = Math.max(diff, maxChange);
        }
      }
      iter++;
    } while (diff > epsilon && iter < maxIter);

    // 反归一化
    for (int i = 0; i < nVars; i++) {
      double sqrtH = Math.sqrt(h[i]);
      for (int j = 0; j < nFactors; j++) {
        rotated[i][j] = (sqrtH > 1e-10) ? rotated[i][j] * sqrtH : 0.0;
      }
    }

    System.out.println("Varimax旋转在 " + iter + " 次迭代后收敛，最大变化=" + diff);
    return rotated;
  }

  // 数据标准化
  private static List<List<Double>> standardizeData(List<List<Double>> data) {
    return data.stream().map(column -> {
      double mean = column.stream().mapToDouble(Double::doubleValue).average().orElse(0);
      final double std = Math
          .max(Math.sqrt(column.stream().mapToDouble(v -> Math.pow(v - mean, 2)).sum() / (column.size() - 1)), 1e-10);
      return column.stream().map(v -> (v - mean) / std).collect(Collectors.toList());
    }).collect(Collectors.toList());
  }

  // 计算相关矩阵
  private static RealMatrix calculateCorrelationMatrix(List<List<Double>> standardizedData) {
    int nVars = standardizedData.size();
    int nObs = standardizedData.get(0).size();

    double[][] dataArray = new double[nObs][nVars];
    for (int i = 0; i < nObs; i++) {
      for (int j = 0; j < nVars; j++) {
        dataArray[i][j] = standardizedData.get(j).get(i);
      }
    }

    return new PearsonsCorrelation().computeCorrelationMatrix(dataArray);
  }

  // 计算特征值大于1的因子数量
  private static int countEigenvaluesGreaterThanOne(double[] eigenvalues) {
    return (int) Arrays.stream(eigenvalues).filter(ev -> ev > 1.0).count();
  }

  // 主测试方法
  public static void main(String[] args) {
    // 1. 解析输入数据
    String input = "[[\"1、标题\",\"2、标题\",\"3、标题\",\"4、标题\",\"5、标题\",\"6、标题\",\"7、标题\",\"8、标题\",\"9、标题\",\"10、标题\",\"11、标题\",\"12、标题\",\"13、标题\",\"14、标题\",\"15、标题\"],"
        + "[\"4\",\"5\",\"4\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\"],"
        + "[\"3\",\"3\",\"2\",\"3\",\"4\",\"4\",\"4\",\"4\",\"2\",\"2\",\"2\",\"3\",\"4\",\"4\",\"3\"],"
        + "[\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\"],"
        + "[\"1\",\"2\",\"2\",\"1\",\"3\",\"3\",\"3\",\"2\",\"1\",\"2\",\"2\",\"1\",\"3\",\"2\",\"3\"],"
        + "[\"1\",\"2\",\"2\",\"1\",\"2\",\"2\",\"2\",\"3\",\"1\",\"2\",\"2\",\"2\",\"3\",\"2\",\"1\"],"
        + "[\"2\",\"2\",\"2\",\"2\",\"3\",\"3\",\"4\",\"3\",\"3\",\"2\",\"4\",\"2\",\"3\",\"3\",\"3\"],"
        + "[\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\"],"
        + "[\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"3\",\"3\",\"3\",\"4\",\"5\",\"5\",\"5\"],"
        + "[\"4\",\"3\",\"3\",\"3\",\"2\",\"2\",\"2\",\"2\",\"3\",\"3\",\"2\",\"4\",\"1\",\"2\",\"2\"],"
        + "[\"3\",\"2\",\"2\",\"2\",\"2\",\"3\",\"2\",\"2\",\"4\",\"4\",\"4\",\"3\",\"2\",\"2\",\"2\"],"
        + "[\"4\",\"5\",\"4\",\"3\",\"2\",\"3\",\"3\",\"3\",\"2\",\"3\",\"3\",\"3\",\"3\",\"2\",\"4\"],"
        + "[\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\"],"
        + "[\"4\",\"3\",\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"5\",\"3\",\"4\",\"4\"],"
        + "[\"5\",\"3\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"3\",\"4\",\"3\",\"4\",\"5\",\"5\"],"
        + "[\"4\",\"4\",\"5\",\"3\",\"3\",\"4\",\"3\",\"5\",\"2\",\"3\",\"3\",\"3\",\"4\",\"4\",\"4\"],"
        + "[\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\"],"
        + "[\"4\",\"4\",\"4\",\"4\",\"4\",\"5\",\"5\",\"4\",\"3\",\"3\",\"2\",\"3\",\"5\",\"5\",\"5\"],"
        + "[\"2\",\"1\",\"1\",\"2\",\"3\",\"2\",\"3\",\"3\",\"4\",\"3\",\"4\",\"4\",\"3\",\"3\",\"2\"],"
        + "[\"5\",\"4\",\"4\",\"3\",\"4\",\"4\",\"4\",\"5\",\"5\",\"4\",\"4\",\"5\",\"4\",\"4\",\"5\"],"
        + "[\"2\",\"2\",\"3\",\"2\",\"2\",\"3\",\"2\",\"1\",\"1\",\"1\",\"1\",\"2\",\"1\",\"2\",\"2\"],"
        + "[\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\",\"3\",\"4\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"5\"],"
        + "[\"4\",\"4\",\"4\",\"3\",\"4\",\"3\",\"4\",\"4\",\"4\",\"3\",\"3\",\"4\",\"5\",\"4\",\"5\"],"
        + "[\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"4\",\"4\",\"5\",\"4\",\"5\"],"
        + "[\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"4\"],"
        + "[\"5\",\"4\",\"4\",\"5\",\"4\",\"4\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\"],"
        + "[\"2\",\"4\",\"2\",\"3\",\"4\",\"5\",\"4\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\"],"
        + "[\"5\",\"4\",\"5\",\"4\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\",\"5\",\"4\",\"5\",\"5\",\"4\"],"
        + "[\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\",\"4\",\"5\",\"3\",\"3\",\"4\"],"
        + "[\"3\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"2\",\"3\",\"3\",\"4\",\"4\",\"5\"],"
        + "[\"2\",\"1\",\"1\",\"2\",\"4\",\"5\",\"4\",\"4\",\"3\",\"5\",\"3\",\"4\",\"4\",\"3\",\"4\"],"
        + "[\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"4\"],"
        + "[\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"4\",\"4\",\"5\"],"
        + "[\"4\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"4\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"5\"],"
        + "[\"4\",\"5\",\"4\",\"3\",\"4\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\"],"
        + "[\"5\",\"5\",\"4\",\"5\",\"4\",\"5\",\"4\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"4\",\"5\"],"
        + "[\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\"],"
        + "[\"4\",\"5\",\"5\",\"5\",\"4\",\"4\",\"4\",\"5\",\"4\",\"5\",\"5\",\"4\",\"4\",\"5\",\"4\"],"
        + "[\"4\",\"4\",\"4\",\"3\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\",\"4\",\"5\",\"3\",\"4\",\"4\"],"
        + "[\"2\",\"3\",\"2\",\"3\",\"1\",\"3\",\"3\",\"2\",\"3\",\"2\",\"1\",\"2\",\"2\",\"2\",\"2\"],"
        + "[\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"4\",\"5\",\"4\"],"
        + "[\"1\",\"2\",\"3\",\"3\",\"3\",\"4\",\"4\",\"5\",\"4\",\"4\",\"3\",\"5\",\"4\",\"5\",\"4\"],"
        + "[\"2\",\"3\",\"1\",\"1\",\"1\",\"2\",\"2\",\"1\",\"2\",\"2\",\"2\",\"2\",\"2\",\"2\",\"2\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\"],"
        + "[\"2\",\"3\",\"2\",\"1\",\"3\",\"2\",\"2\",\"2\",\"3\",\"3\",\"3\",\"3\",\"3\",\"3\",\"2\"],"
        + "[\"4\",\"4\",\"5\",\"4\",\"5\",\"4\",\"5\",\"4\",\"4\",\"5\",\"4\",\"5\",\"4\",\"3\",\"3\"],"
        + "[\"3\",\"3\",\"3\",\"3\",\"2\",\"2\",\"2\",\"2\",\"3\",\"3\",\"3\",\"2\",\"4\",\"5\",\"4\"],"
        + "[\"5\",\"4\",\"5\",\"4\",\"5\",\"5\",\"4\",\"5\",\"3\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"4\",\"4\"],"
        + "[\"3\",\"2\",\"4\",\"3\",\"2\",\"2\",\"1\",\"3\",\"1\",\"1\",\"2\",\"1\",\"1\",\"2\",\"3\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\"],"
        + "[\"3\",\"3\",\"2\",\"3\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"2\",\"3\"],"
        + "[\"4\",\"5\",\"5\",\"5\",\"3\",\"4\",\"4\",\"3\",\"5\",\"5\",\"4\",\"5\",\"3\",\"2\",\"4\"],"
        + "[\"1\",\"2\",\"1\",\"1\",\"2\",\"3\",\"2\",\"3\",\"2\",\"3\",\"3\",\"3\",\"2\",\"3\",\"3\"],"
        + "[\"3\",\"3\",\"2\",\"3\",\"5\",\"4\",\"4\",\"5\",\"4\",\"4\",\"4\",\"4\",\"2\",\"2\",\"2\"],"
        + "[\"4\",\"4\",\"5\",\"4\",\"4\",\"5\",\"4\",\"4\",\"5\",\"4\",\"4\",\"4\",\"3\",\"3\",\"4\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"3\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\",\"4\",\"3\",\"4\",\"4\"],"
        + "[\"3\",\"3\",\"2\",\"4\",\"2\",\"2\",\"3\",\"3\",\"4\",\"4\",\"4\",\"4\",\"3\",\"2\",\"2\"],"
        + "[\"4\",\"2\",\"2\",\"4\",\"3\",\"3\",\"3\",\"3\",\"1\",\"2\",\"2\",\"2\",\"2\",\"3\",\"3\"],"
        + "[\"4\",\"4\",\"5\",\"5\",\"3\",\"2\",\"3\",\"2\",\"5\",\"4\",\"4\",\"5\",\"3\",\"4\",\"4\"],"
        + "[\"4\",\"5\",\"5\",\"4\",\"3\",\"4\",\"5\",\"4\",\"3\",\"4\",\"4\",\"3\",\"5\",\"4\",\"4\"],"
        + "[\"5\",\"5\",\"4\",\"4\",\"3\",\"5\",\"3\",\"4\",\"5\",\"4\",\"4\",\"5\",\"5\",\"4\",\"5\"],"
        + "[\"4\",\"4\",\"5\",\"5\",\"4\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"3\",\"4\",\"4\"],"
        + "[\"4\",\"4\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\"],"
        + "[\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\"],"
        + "[\"4\",\"5\",\"4\",\"3\",\"4\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\"],"
        + "[\"3\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"5\"],"
        + "[\"4\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"3\",\"3\",\"3\",\"3\",\"5\",\"4\",\"3\"],"
        + "[\"4\",\"5\",\"4\",\"4\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"4\",\"5\",\"4\",\"4\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"4\",\"3\",\"4\",\"3\",\"5\",\"4\",\"4\",\"4\",\"4\",\"3\",\"4\"],"
        + "[\"5\",\"4\",\"4\",\"3\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"3\",\"4\",\"4\"],"
        + "[\"5\",\"4\",\"4\",\"4\",\"5\",\"4\",\"4\",\"4\",\"5\",\"3\",\"4\",\"4\",\"4\",\"3\",\"3\"],"
        + "[\"5\",\"4\",\"3\",\"4\",\"4\",\"4\",\"4\",\"4\",\"5\",\"4\",\"4\",\"4\",\"4\",\"5\",\"4\"],"
        + "[\"3\",\"4\",\"4\",\"4\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"5\",\"4\",\"4\",\"5\",\"5\"],"
        + "[\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"3\",\"5\",\"5\",\"5\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\"],"
        + "[\"3\",\"4\",\"2\",\"2\",\"3\",\"2\",\"3\",\"4\",\"3\",\"3\",\"2\",\"2\",\"3\",\"3\",\"3\"],"
        + "[\"4\",\"5\",\"5\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"3\",\"4\"],"
        + "[\"3\",\"4\",\"3\",\"3\",\"2\",\"1\",\"2\",\"2\",\"1\",\"2\",\"2\",\"2\",\"2\",\"2\",\"3\"],"
        + "[\"4\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"5\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"4\"],"
        + "[\"4\",\"3\",\"3\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\",\"5\"],"
        + "[\"4\",\"5\",\"5\",\"4\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"3\",\"2\",\"3\"],"
        + "[\"4\",\"4\",\"4\",\"5\",\"5\",\"4\",\"4\",\"4\",\"4\",\"5\",\"4\",\"4\",\"4\",\"5\",\"5\"],"
        + "[\"1\",\"1\",\"1\",\"2\",\"2\",\"2\",\"3\",\"2\",\"2\",\"1\",\"1\",\"1\",\"2\",\"1\",\"2\"],"
        + "[\"1\",\"3\",\"3\",\"2\",\"3\",\"3\",\"3\",\"3\",\"3\",\"3\",\"3\",\"3\",\"2\",\"2\",\"4\"],"
        + "[\"3\",\"3\",\"3\",\"4\",\"2\",\"1\",\"2\",\"1\",\"3\",\"3\",\"3\",\"3\",\"3\",\"2\",\"2\"],"
        + "[\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"4\",\"3\",\"4\",\"4\",\"5\",\"5\",\"5\",\"4\"],"
        + "[\"4\",\"4\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\"],"
        + "[\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"4\",\"4\",\"4\",\"3\",\"3\",\"3\",\"5\",\"5\",\"4\"],"
        + "[\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"5\",\"4\"],"
        + "[\"4\",\"5\",\"3\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"5\"],"
        + "[\"3\",\"4\",\"5\",\"4\",\"4\",\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"4\",\"3\",\"4\",\"3\"],"
        + "[\"4\",\"4\",\"5\",\"5\",\"4\",\"4\",\"4\",\"4\",\"4\",\"5\",\"4\",\"4\",\"5\",\"5\",\"5\"],"
        + "[\"4\",\"5\",\"5\",\"4\",\"4\",\"4\",\"4\",\"5\",\"5\",\"4\",\"4\",\"4\",\"5\",\"5\",\"5\"],"
        + "[\"4\",\"4\",\"5\",\"5\",\"3\",\"4\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\",\"4\",\"3\",\"4\"],"
        + "[\"5\",\"5\",\"4\",\"5\",\"4\",\"4\",\"3\",\"4\",\"4\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\"],"
        + "[\"5\",\"4\",\"5\",\"4\",\"4\",\"3\",\"3\",\"4\",\"5\",\"5\",\"5\",\"4\",\"4\",\"5\",\"5\"],"
        + "[\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"5\",\"4\",\"5\",\"5\",\"5\",\"4\",\"4\"]]";

    List<List<String>> rawData = parseInputData(input);

    // 2. 转换数据格式 (跳过标题行)
    List<List<Double>> numericData = new ArrayList<>();
    for (int col = 0; col < 15; col++) {
      List<Double> column = new ArrayList<>();
      for (int row = 1; row < rawData.size(); row++) {
        String value = rawData.get(row).get(col);
        try {
          column.add(Double.parseDouble(value));
        } catch (NumberFormatException e) {
          column.add(0.0); // 处理异常值
        }
      }
      numericData.add(column);
    }

    // 3. 执行因子分析 (指定3个因子)
    System.out.println("开始因子分析...");
    FactorAnalysisOutput result = performFactorAnalysisTest(numericData, 3);
    System.out.println("因子分析完成");

    // 4. 打印结果
    System.out.println("\n旋转后的因子载荷：");
    System.out.printf("%-10s %8s %8s %8s %10s%n", "项目", "因子1", "因子2", "因子3", "共同度");
    for (FactorAnalysisOutput.FactorAnalysisItem item : result.getItems()) {
      System.out.printf("%-10s", item.getTitle());
      for (Double loading : item.getFators()) {
        System.out.printf(" %8.4f", loading);
      }
      System.out.printf(" %10.4f%n", item.getHValue());
    }

    System.out.println("\n旋转前方差解释：");
    System.out.printf("%-25s: %s%n", "特征根值(旋转前)",
        result.getTzgBefore().stream().map(v -> String.format("%.2f", v)).collect(Collectors.joining(", ")));
    System.out.printf("%-25s: %s%n", "方差解释率%(旋转前)",
        result.getFcBefore().stream().map(v -> String.format("%.2f%%", v * 100)).collect(Collectors.joining(", ")));
    System.out.printf("%-25s: %s%n", "累积方差解释率%(旋转前)",
        result.getLjfcBefore().stream().map(v -> String.format("%.2f%%", v * 100)).collect(Collectors.joining(", ")));

    System.out.println("\n旋转后方差解释：");
    System.out.printf("%-25s: %s%n", "特征根值(旋转后)",
        result.getTzgAfter().stream().map(v -> String.format("%.2f", v)).collect(Collectors.joining(", ")));
    System.out.printf("%-25s: %s%n", "方差解释率%(旋转后)",
        result.getFcAfter().stream().map(v -> String.format("%.2f%%", v * 100)).collect(Collectors.joining(", ")));
    System.out.printf("%-25s: %s%n", "累积方差解释率%(旋转后)",
        result.getLjfcAfter().stream().map(v -> String.format("%.2f%%", v * 100)).collect(Collectors.joining(", ")));
  }

  // 解析输入数据
  private static List<List<String>> parseInputData(String input) {
    List<List<String>> data = new ArrayList<>();
    String[] lines = input.substring(2, input.length() - 2).split("\\],\\[");

    for (String line : lines) {
      String[] values = line.split(",");
      List<String> row = new ArrayList<>();
      for (String value : values) {
        // 移除引号和空格
        String cleanValue = value.replace("\"", "").trim();
        row.add(cleanValue);
      }
      data.add(row);
    }
    return data;
  }

  // 因子分析输出类
  static class FactorAnalysisOutput {
    private int factors;
    private List<Double> tzgBefore; // 旋转前特征根
    private List<Double> fcBefore; // 旋转前方差解释率
    private List<Double> ljfcBefore;// 旋转前累积方差解释率
    private List<Double> tzgAfter; // 旋转后特征根
    private List<Double> fcAfter; // 旋转后方差解释率
    private List<Double> ljfcAfter; // 旋转后累积方差解释率
    private List<FactorAnalysisItem> items;

    // getters and setters
    public int getFactors() {
      return factors;
    }

    public void setFactors(int factors) {
      this.factors = factors;
    }

    public List<Double> getTzgBefore() {
      return tzgBefore;
    }

    public void setTzgBefore(List<Double> tzgBefore) {
      this.tzgBefore = tzgBefore;
    }

    public List<Double> getFcBefore() {
      return fcBefore;
    }

    public void setFcBefore(List<Double> fcBefore) {
      this.fcBefore = fcBefore;
    }

    public List<Double> getLjfcBefore() {
      return ljfcBefore;
    }

    public void setLjfcBefore(List<Double> ljfcBefore) {
      this.ljfcBefore = ljfcBefore;
    }

    public List<Double> getTzgAfter() {
      return tzgAfter;
    }

    public void setTzgAfter(List<Double> tzgAfter) {
      this.tzgAfter = tzgAfter;
    }

    public List<Double> getFcAfter() {
      return fcAfter;
    }

    public void setFcAfter(List<Double> fcAfter) {
      this.fcAfter = fcAfter;
    }

    public List<Double> getLjfcAfter() {
      return ljfcAfter;
    }

    public void setLjfcAfter(List<Double> ljfcAfter) {
      this.ljfcAfter = ljfcAfter;
    }

    public List<FactorAnalysisItem> getItems() {
      return items;
    }

    public void setItems(List<FactorAnalysisItem> items) {
      this.items = items;
    }

    static class FactorAnalysisItem {
      private String title;
      private List<Double> fators;
      private double hValue;

      // getters and setters
      public String getTitle() {
        return title;
      }

      public void setTitle(String title) {
        this.title = title;
      }

      public List<Double> getFators() {
        return fators;
      }

      public void setFators(List<Double> fators) {
        this.fators = fators;
      }

      public double getHValue() {
        return hValue;
      }

      public void setHValue(double hValue) {
        this.hValue = hValue;
      }
    }
  }
}
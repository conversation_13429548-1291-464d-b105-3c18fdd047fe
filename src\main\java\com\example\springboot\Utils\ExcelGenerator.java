package com.example.springboot.Utils;

import com.example.springboot.entity.QuestionInfo;
import com.example.springboot.entity.SurveyData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

@Slf4j
public class ExcelGenerator {

    // 解析问卷HTML获取题目列表
    public static List<QuestionInfo> parseSurveyQuestions(String html) {
        List<QuestionInfo> questions = new ArrayList<>();
        Document doc = Jsoup.parse(html);

        Elements questionElements = doc.select(".field.ui-field-contain");
        for (Element element : questionElements) {
            QuestionInfo info = new QuestionInfo();

            // 解析标题
            String title = element.select(".topichtml").text()
                    .replaceAll("^\\d+[.、\\s]+", "") // 去除开头的数字+标点+空格
                    .trim();
            info.setTitle(title);

            // 解析题型
            String type = element.attr("type");
            info.setType(type);

            // 根据题型设置列数
            switch (type) {
                case "1":
                case "2":
                    info.setMsg("填空题");
                    // 精确获取所有输入框（包括text和textarea）
                    info.setNum(1);
                    break;
                case "3":
                    info.setMsg("单选题");
                    info.setNum(1);
                    break;
                case "4":
                    info.setMsg("多选题");
                    info.setNum(element.select(".ui-checkbox div[class=\"label\"]").size());
                    break;
                case "6":
                    if (checkMatrixRating(element)) {
                        info.setType("6single");
                        info.setMsg("矩阵单选题");
                        int rows = element.select(".matrixtable tr[tp='d']").size();
                        info.setNum(rows);
                        info.setCols(element.select(".matrixtable tr[tp='d']").first().select("a").size());
                    } else {
                        info.setType("6multiple");
                        info.setMsg("矩阵多选题");
                        Elements rowsElements = element.select(".matrixtable tr[tp='d']");
                        int rows = rowsElements.size();
                        // 获取第一行中的选项数
                        Element firstRow = rowsElements.first();
                        int cols = firstRow.select("a").size(); // 修正列数计算
                        info.setNum(rows * cols);
                        info.setCols(cols);
                    }
                    break;
                case "7":
                    info.setMsg("下拉题");
                    info.setNum(1);
                    break;
                case "8":
                    info.setMsg("单项滑条题");
                    info.setNum(1);
                    break;
                case "5":
                    info.setMsg("单项量表题");
                    info.setNum(1);
                    break;
                case "11":
                    info.setMsg("排序题");
                    info.setNum(element.select(".ui-li-static").size());
                    break;
                case "9":
                case "12":
                    // 判断是否存在rangeslider控件
                    boolean hasRangeslider = !element.select(".rangeslider").isEmpty();
                    if (type.equals("9") && !hasRangeslider) {
                        // 检查矩阵式填空
                        boolean isMatrixStyle = !element.select(".matrix-rating.scaletablewrap").isEmpty();
                        if (isMatrixStyle) {
                            info.setMsg("多填空题2");
                        } else {
                            info.setMsg("多填空题1");
                        }
                        // 获取矩阵行数
                        Elements matrixRows = element.select("input,textarea");
                        info.setNum(matrixRows.size());
                    } else {
                        // 处理滑条题型
                        info.setMsg("滑条题");
                        Elements sliders = element.select(".rangeslider");
                        info.setNum(sliders.size());
                    }
                    break;
                default:
                    info.setMsg("暂不支持该题型");
                    info.setNum(0);
            }

            questions.add(info);
        }

        // 关键修改：按出现顺序设置题号
        for (int i = 0; i < questions.size(); i++) {
            questions.get(i).setQuestionNumber(i + 1); // 题号从1开始
        }

        return questions; // 移除 .sorted() 保持原始顺序
    }

    public static List<SurveyData> parseSurveyData(String html) {
        List<SurveyData> questions = new ArrayList<>();
        Document doc = Jsoup.parse(html);

        Elements questionElements = doc.select(".field.ui-field-contain");
        int currentColumnIndex = 1; // Excel列号从1开始

        for (int i = 0; i < questionElements.size(); i++) {
            Element element = questionElements.get(i);
            SurveyData info = new SurveyData();
            info.setNumId(i + 1);
            info.setHtml(element.outerHtml());

            // 解析标题
            String title = element.select(".topichtml").text()
                    .replaceAll("^\\d+[.、\\s]+", "") // 去除开头的数字+标点+空格
                    .trim();
            info.setTitle(title);

            // 解析题型
            String type = element.attr("type");
            if (type.equals("6")) {
                if (checkMatrixRating(element)) {
                    type = "6single";
                } else {
                    type = "6multiple";
                }
            }
            if (type.equals("9")) {
                boolean hasRangeslider = !element.select(".rangeslider").isEmpty();
                if (!hasRangeslider) {
                    boolean isMatrixStyle = !element.select(".matrix-rating.scaletablewrap").isEmpty();
                    type = isMatrixStyle ? "9duotian2" : "9duotian1";
                } else {
                    type = "9hua";
                }
            }
            info.setType(type);

            // 计算当前题目占用的列数
            int numColsForThisQuestion = 0;
            switch (type) {
                case "1": case "2": case "3": case "5": case "7": case "8":
                    numColsForThisQuestion = 1;
                    break;
                case "4": // 多选题
                    numColsForThisQuestion = element.select(".ui-checkbox .label").size();
                    break;
                case "6single": // 矩阵单选题
                    numColsForThisQuestion = element.select(".matrixtable tr[tp='d']").size();
                    break;
                case "6multiple": // 矩阵多选题
                    Elements rowsElements = element.select(".matrixtable tr[tp='d']");
                    int rows = rowsElements.size();
                    Element firstRow = rowsElements.first();
                    int cols = firstRow != null ? firstRow.select("a").size() : 0;
                    numColsForThisQuestion = rows * cols; // 矩阵多选的总列数
                    break;
                case "11": // 排序题
                    numColsForThisQuestion = element.select(".ui-li-static").size();
                    break;
                case "9duotian1": case "9duotian2":
                    numColsForThisQuestion = element.select("input,textarea").size();
                    break;
                case "9hua": case "12":
                    numColsForThisQuestion = element.select(".rangeslider").size();
                    break;
                default:
                    numColsForThisQuestion = 0;
            }

            // 设置列索引
            List<Integer> colIndices = new ArrayList<>();
            for (int j = 0; j < numColsForThisQuestion; j++) {
                colIndices.add(currentColumnIndex + j);
            }
            info.setColIndices(colIndices);
            currentColumnIndex += numColsForThisQuestion;


            // 解析题型描述
            String typeInfo = "";
            switch (type) {
                case "1":
                case "2":
                    typeInfo = "填空题";
                    break;
                case "3":
                    typeInfo = "单选题";
                    break;
                case "4":
                    typeInfo = "多选题";
                    break;
                case "5":
                    typeInfo = "单项量表题";
                    break;
                case "6single":
                    typeInfo = "矩阵单选题";
                    break;
                case "6multiple":
                    typeInfo = "矩阵多选题";
                    break;
                case "7":
                    typeInfo = "下拉题";
                    break;
                case "8":
                    typeInfo = "单项滑条题";
                    break;
                case "11":
                    typeInfo = "排序题";
                    break;
                case "9duotian1":
                    typeInfo = "多填空题1";
                    break;
                case "9duotian2":
                    typeInfo = "多填空题2";
                    break;
                case "9hua":
                    typeInfo = "矩阵滑条题";
                    break;
                case "12":
                    typeInfo = "比重滑条题";
                    break;
                default:
                    typeInfo = "未知题型";
            }
            info.setTypeInfo(typeInfo);

            // 解析选项信息
            List<String> options = parseOptionsFromHtml(element.outerHtml());
            if (!options.isEmpty()) {
                info.setOptions(options);
            }

            // 解析矩阵题的小题信息
            if ("6single".equals(type) || "6multiple".equals(type)) {
                List<SurveyData.SubQuestion> subQuestions = parseMatrixSubQuestions(element.outerHtml());
                info.setSubQuestions(subQuestions);
            }

            questions.add(info);
        }
        return questions;
    }

    private static boolean checkMatrixRating(Element element) {
        // 获取所有.matrix-rating下的<a>元素
        Elements matrixRatingLinks = element.select(".matrix-rating a");

        // 检查是否存在任意一个<a>元素具有border-radius:3px样式
        for (Element link : matrixRatingLinks) {
            String style = link.attr("style");
            if (style.contains("border-radius:3px")) {
                return false; // 存在指定样式则返回false
            }
        }
        return true; // 所有元素都不含该样式则返回true
    }

    // 解析矩阵题的小题信息
    private static List<SurveyData.SubQuestion> parseMatrixSubQuestions(String html) {
        List<SurveyData.SubQuestion> subQuestions = new ArrayList<>();
        if (html == null || html.isEmpty())
            return subQuestions;
        
        Document doc = Jsoup.parse(html);
        
        // 解析矩阵题的小题和选项
        Elements rowTitles = doc.select("tr.rowtitle .itemTitleSpan");
        Elements optionHeaders = doc.select("tr.trlabel th");
        
        // 获取选项标题（表头）
        List<String> optionTitles = new ArrayList<>();
        for (Element header : optionHeaders) {
            String text = header.text().trim();
            if (!text.isEmpty()) {
                optionTitles.add(text);
            }
        }
        
        // 获取每个小题的标题
        for (Element titleElement : rowTitles) {
            String title = titleElement.text().trim();
            if (!title.isEmpty()) {
                SurveyData.SubQuestion subQuestion = new SurveyData.SubQuestion();
                subQuestion.setTitle(title);
                subQuestion.setOptions(new ArrayList<>(optionTitles));
                subQuestions.add(subQuestion);
            }
        }
        
        return subQuestions;
    }

    // 解析html字段中的选项内容
    private static List<String> parseOptionsFromHtml(String html) {
        List<String> options = new ArrayList<>();
        if (html == null || html.isEmpty())
            return options;
        
        Document doc = Jsoup.parse(html);
        
        // 1. 单选题、多选题、下拉题 - 原有的选择器
        Elements optionLabels = doc.select(".ui-radio .label, .ui-checkbox .label, select option");
        for (Element label : optionLabels) {
            String text = label.text().trim();
            if (!text.isEmpty() && !"请选择".equals(text)) {
                options.add(text);
            }
        }
        
        // 2. 排序题 - 解析li元素中的span内容
        if (options.isEmpty()) {
            Elements sortItems = doc.select("ul.ui-controlgroup li.ui-li-static div span");
            for (Element item : sortItems) {
                String text = item.text().trim();
                if (!text.isEmpty() && !text.matches("\\d+")) { // 排除排序数字
                    options.add(text);
                }
            }
        }
        
        // 3. 量表题 - 解析量表选项
        if (options.isEmpty()) {
            Elements scaleItems = doc.select(".scale-rating ul li a");
            for (Element item : scaleItems) {
                String text = item.text().trim();
                String title = item.attr("title");
                if (!text.isEmpty()) {
                    if (!title.isEmpty()) {
                        options.add(title); // 优先使用title属性
                    } else {
                        options.add(text);
                    }
                }
            }
        }
        
        return options;
    }

    private static int calculateColumns(Element element, String type) {
        // 根据不同类型计算列数（需要根据实际问卷结构调整）
        if ("3".equals(type))
            return 1; // 单选题
        if ("4".equals(type)) { // 多选题
            return element.select(".ui-checkbox").size();
        }
        // 其他类型处理...
        return 1;
    }

    public static List<List<String>> parseExcelData(MultipartFile file) throws IOException {
        List<List<String>> result = new ArrayList<>();

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();
                for (Cell cell : row) {
                    rowData.add(getCellValueAsString(cell));
                }
                result.add(rowData);
            }
        }

        return result;
    }

    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                }
                double numericValue = cell.getNumericCellValue();
                // 判断是否为整数
                if (numericValue == (long) numericValue) {
                    return String.valueOf((long) numericValue); // 转换为长整型再转字符串，去除.0
                } else {
                    return String.valueOf(numericValue); // 保留浮点数格式
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    public static void generateExcel(List<List<String>> data, OutputStream outputStream) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("数据");

            // 创建标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 写入数据
            for (int i = 0; i < data.size(); i++) {
                Row row = sheet.createRow(i);
                List<String> rowData = data.get(i);

                for (int j = 0; j < rowData.size(); j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellValue(rowData.get(j));

                    // 为标题行应用样式
                    if (i == 0) {
                        cell.setCellStyle(headerStyle);
                    }
                }
            }

            // 自动调整列宽
            for (int i = 0; i < data.get(0).size(); i++) {
                sheet.autoSizeColumn(i);
            }

            workbook.write(outputStream);
        }
    }

    // 生成Excel文件
    public static void generateExcel(List<QuestionInfo> questions,
            List<String> answerData,
            OutputStream outputStream) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Survey Answers");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        int colIndex = 0;
        for (int j = 0; j < questions.size(); j++) {
            QuestionInfo questionInfo = questions.get(j);
            for (int i = 0; i < questionInfo.getNum(); i++) {
                headerRow.createCell(colIndex++).setCellValue(
                        (j + 1) + "、" + questionInfo.getTitle() + (questionInfo.getNum() > 1 ? "_" + (i + 1) : ""));
            }
        }

        // 解析答案数据
        Map<Integer, String[]> answerMap = parseAnswerData(answerData, questions);

        // 填充数据
        int rowNum = 1;
        for (String[] answers : answerMap.values()) {
            Row row = sheet.createRow(rowNum++);
            for (int i = 0; i < answers.length; i++) {
                row.createCell(i).setCellValue(answers[i]);
            }
        }

        workbook.write(outputStream);
        workbook.close();
    }

    public static Map<Integer, String[]> parseAnswerData(List<String> answerDataList,
            List<QuestionInfo> questions) {
        Map<Integer, String[]> result = new LinkedHashMap<>();

        for (int recordIndex = 0; recordIndex < answerDataList.size(); recordIndex++) {
            String answerData = answerDataList.get(recordIndex);
            String[] entries = answerData.split("}");

            String[] rowData = new String[getTotalColumns(questions)];
            Arrays.fill(rowData, "");

            for (String entry : entries) {
                if (entry.isEmpty())
                    continue;

                String[] parts = entry.split("\\$", 2);
                int qNumber = Integer.parseInt(parts[0]); // 直接使用当前题号
                String answerValue = parts.length > 1 ? parts[1] : "";

                QuestionInfo question = questions.stream()
                        .filter(q -> q.getQuestionNumber() == qNumber)
                        .findFirst()
                        .orElse(null);

                if (question == null)
                    continue;

                int startCol = questions.stream()
                        .filter(q -> q.getQuestionNumber() < qNumber)
                        .mapToInt(QuestionInfo::getNum)
                        .sum();

                processAnswer(question, answerValue, rowData, startCol);
            }

            result.put(recordIndex + 1, rowData);
        }
        return result;
    }

    private static void processAnswer(QuestionInfo question, String answer, String[] rowData, int startCol) {
        // 统一处理空答案
        if (answer == null || answer.trim().isEmpty()) {
            return; // 直接返回，保持单元格原始状态（不填充任何内容）
        }
        switch (question.getType()) {
            case "1":
            case "2":
            case "5":
            case "7":
            case "8":
            case "3":
                rowData[startCol] = answer;
                break;
            case "4": // 多选题
                Arrays.fill(rowData, startCol, startCol + question.getNum(), "0");
                for (String opt : answer.split("\\|")) {
                    // 分割每个选项为数字部分和文本部分
                    String[] parts = opt.split("\\^", 2); // 最多分割成两部分
                    if (parts.length >= 1) {
                        try {
                            int index = Integer.parseInt(parts[0]) - 1;
                            if (index >= 0 && index < question.getNum()) {
                                // 如果有文本部分，则拼接"1^文本"，否则只放"1"
                                String value = (parts.length > 1) ? "1^" + parts[1] : "1";
                                rowData[startCol + index] = value;
                            }
                        } catch (NumberFormatException e) {
                            // 处理数字解析错误的情况
                            log.error("Invalid option format: " + opt);
                        }
                    }
                }
                break;
            case "6single": // 矩阵单选题
                for (String pair : answer.split(",")) {
                    String[] parts = pair.split("!");
                    int row = Integer.parseInt(parts[0]) - 1;
                    rowData[startCol + row] = parts[1];
                }
                break;
            case "6multiple": // 矩阵多选题
                Arrays.fill(rowData, startCol, startCol + question.getNum(), "0");
                for (String pair : answer.split(",")) {
                    String[] parts = pair.split("!");
                    int row = Integer.parseInt(parts[0]) - 1;
                    for (String opt : parts[1].split(";")) {
                        int col = Integer.parseInt(opt) - 1;
                        int index = row * question.getCols() + col;
                        rowData[startCol + index] = "1";
                    }
                }
                break;
            case "11": // 排序题
                int qNum = question.getNum();
                String[] parts = answer.split(",");
                String[] rankOrder = new String[qNum]; // 创建排名数组
                Arrays.fill(rankOrder, null); // 初始化为null

                // 1. 解析每个选项的排名值
                for (int i = 0; i < parts.length; i++) {
                    if (i >= qNum)
                        break; // 防止超过题目选项数
                    String part = parts[i].trim();
                    if (part.isEmpty())
                        continue;

                    // 2. 解析排名数字（支持"3^备注"格式）
                    String[] splitPart = part.split("\\^");
                    int rank;
                    try {
                        rank = Integer.parseInt(splitPart[0]);
                    } catch (NumberFormatException e) {
                        continue; // 跳过无效格式
                    }

                    // 3. 验证排名范围
                    if (rank < 1 || rank > qNum)
                        continue;

                    // 4. 构建结果字符串（保留原始格式）
                    String result = (i + 1) + (splitPart.length > 1 ? "^" + splitPart[1] : "");

                    // 5. 将选项填入对应排名位置
                    rankOrder[rank - 1] = result;
                }

                // 6. 填充到Excel单元格
                for (int i = 0; i < rankOrder.length; i++) {
                    rowData[startCol + i] = rankOrder[i] != null ? rankOrder[i] : "";
                }
                break;
            case "9":
            case "12": // 类型9和12处理
                String msg = question.getMsg() != null ? question.getMsg() : "";
                if (msg.contains("多填空题1")) {
                    // 格式：值1^值2^值3
                    parts = answer.split("\\^");
                    for (int i = 0; i < Math.min(parts.length, question.getNum()); i++) {
                        rowData[startCol + i] = parts[i];
                    }
                } else if (msg.contains("多填空题2")) {
                    // 新逻辑：处理格式 1!值1^2!值2
                    for (String pair : answer.split("\\^")) {
                        parts = pair.split("!");
                        if (parts.length == 2) {
                            try {
                                int index = Integer.parseInt(parts[0]) - 1;
                                if (index >= 0 && index < question.getNum()) {
                                    rowData[startCol + index] = parts[1];
                                }
                            } catch (NumberFormatException e) {
                                // 忽略格式错误
                            }
                        }
                    }
                } else if (msg.contains("滑条题")) {
                    // 格式：1!值1^2!值2
                    Arrays.fill(rowData, startCol, startCol + question.getNum(), ""); // 清空区域
                    for (String pair : answer.split("\\^")) {
                        parts = pair.split("!");
                        if (parts.length == 2) {
                            try {
                                int index = Integer.parseInt(parts[0]) - 1;
                                if (index >= 0 && index < question.getNum()) {
                                    rowData[startCol + index] = parts[1];
                                }
                            } catch (NumberFormatException e) {
                                // 忽略格式错误
                            }
                        }
                    }
                } else {
                    // 默认处理
                    parts = answer.split("\\^");
                    for (int i = 0; i < Math.min(parts.length, question.getNum()); i++) {
                        rowData[startCol + i] = parts[i];
                    }
                }
                break;
            // 其他题型处理...
            default:
                parts = answer.split("\\^");
                for (int i = 0; i < Math.min(parts.length, question.getNum()); i++) {
                    rowData[startCol + i] = parts[i];
                }
                break;
        }
    }

    private static int getTotalColumns(List<QuestionInfo> questions) {
        return questions.stream().mapToInt(QuestionInfo::getNum).sum();
    }

    // 新增方法：从List<List<String>>生成Excel
    public static void generateExcelFromList(List<List<String>> data, OutputStream outputStream) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("数据");

            // 创建标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 写入数据
            for (int i = 0; i < data.size(); i++) {
                Row row = sheet.createRow(i);
                List<String> rowData = data.get(i);

                for (int j = 0; j < rowData.size(); j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellValue(rowData.get(j));

                    // 为标题行应用样式
                    if (i == 0) {
                        cell.setCellStyle(headerStyle);
                    }
                }
            }

            // 自动调整列宽
            for (int i = 0; i < data.get(0).size(); i++) {
                sheet.autoSizeColumn(i);
            }

            workbook.write(outputStream);
            if (outputStream instanceof ByteArrayOutputStream) {
                log.info("Excel data successfully written to output stream. Stream size: {} bytes",
                        ((ByteArrayOutputStream) outputStream).size());
            } else {
                log.info("Excel data successfully written to output stream (size not available for this stream type)");
            }
        }
    }

    public static String parseExcelToString(MultipartFile file) throws IOException {
        List<List<String>> data = parseExcelData(file);
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(data);
    }

    // 新增方法：过滤和匹配Excel数据
    public static List<List<String>> filterAndMatchExcelData(List<List<String>> excelData,
            List<QuestionInfo> questions) {
        if (excelData == null || excelData.isEmpty() || questions == null || questions.isEmpty()) {
            return excelData;
        }

        // 获取Excel标题行
        List<String> headerRow = excelData.get(0);
        if (headerRow == null || headerRow.isEmpty()) {
            return excelData;
        }

        // 找到第一个题目的位置
        int firstQuestionIndex = findFirstQuestionIndex(headerRow, questions.get(0));
        if (firstQuestionIndex == -1) {
            throw new IllegalArgumentException("无法在Excel中找到第一个题目");
        }

        // 计算有效列的总数
        int totalValidColumns = questions.stream()
                .mapToInt(QuestionInfo::getNum)
                .sum();

        // 检查列数是否匹配
        if (headerRow.size() - firstQuestionIndex < totalValidColumns) {
            throw new IllegalArgumentException("Excel列数不足以匹配所有题目");
        }

        // 创建新的数据列表，只包含有效列
        List<List<String>> filteredData = new ArrayList<>();
        for (List<String> row : excelData) {
            List<String> filteredRow = new ArrayList<>();
            // 添加从第一个题目开始的有效列
            for (int i = firstQuestionIndex; i < firstQuestionIndex + totalValidColumns; i++) {
                if (i < row.size()) {
                    filteredRow.add(row.get(i));
                } else {
                    filteredRow.add(""); // 如果列不存在，添加空字符串
                }
            }
            filteredData.add(filteredRow);
        }

        return filteredData;
    }

    // 辅助方法：找到第一个题目的索引
    private static int findFirstQuestionIndex(List<String> headerRow, QuestionInfo firstQuestion) {
        String firstQuestionTitle = firstQuestion.getTitle();

        for (int i = 0; i < headerRow.size(); i++) {
            String header = headerRow.get(i);
            if (header != null && (calculateSimilarity(header, firstQuestionTitle) > 0.25 ||
                    header.contains(firstQuestionTitle) ||
                    (header.contains("—") && calculateSimilarity(header.split("—")[0], firstQuestionTitle) > 0.25))) {
                return i;
            }
        }
        return -1;
    }

    // 辅助方法：计算字符串相似度
    private static double calculateSimilarity(String s1, String s2) {
        if (s1 == null || s2 == null) {
            return 0;
        }

        int maxLength = Math.max(s1.length(), s2.length());
        if (maxLength == 0) {
            return 1.0;
        }

        int[][] distance = new int[s1.length() + 1][s2.length() + 1];

        for (int i = 0; i <= s1.length(); i++) {
            distance[i][0] = i;
        }
        for (int j = 0; j <= s2.length(); j++) {
            distance[0][j] = j;
        }

        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                int cost = (s1.charAt(i - 1) == s2.charAt(j - 1)) ? 0 : 1;
                distance[i][j] = Math.min(
                        Math.min(distance[i - 1][j] + 1, distance[i][j - 1] + 1),
                        distance[i - 1][j - 1] + cost);
            }
        }

        return 1.0 - (double) distance[s1.length()][s2.length()] / maxLength;
    }
}

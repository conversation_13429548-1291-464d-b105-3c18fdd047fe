$(document).ready(function () {
    $('#rechargeTypeTooltip').tooltip();
});

// planId -> { pay:..., credit:... }
const planMap = {
    1: { pay: 5,   credit: 5   },
    2: { pay: 10,   credit: 10   },
    3: { pay: 20,   credit: 20   },
    4: { pay: 30,   credit: 30   },
    5: { pay: 50,   credit: 70   },
    6: { pay: 100,  credit: 150  },
    7: { pay: 300,  credit: 500  },
    8: { pay: 500,  credit: 1000 },
    9: { pay: 1000,  credit: 2200 },
};

// 默认为 3 分钟倒计时
let paymentTimeoutDuration = 3 * 60 * 1000;
let paymentCountdownInterval = null;
let paymentStatusCheckInterval = null;

// 切换模式
function switchMode(mode) {
    const btnGen = document.getElementById('btnGenerate');
    const btnRec = document.getElementById('btnRecharge');
    const genSection = document.getElementById('generateSection');
    const recSection = document.getElementById('rechargeSection');

    if (mode === 'generate') {
        btnGen.classList.add('active');
        btnRec.classList.remove('active');
        genSection.style.display = 'block';
        recSection.style.display = 'none';
    } else {
        btnGen.classList.remove('active');
        btnRec.classList.add('active');
        genSection.style.display = 'none';
        recSection.style.display = 'block';
    }
}

// 选择档次
function selectPlan(mode, planId) {
    const data = planMap[planId];
    if (!data) return;

    if (mode === 'generate') {
        document.querySelectorAll('.plan-btn-gen').forEach(btn => btn.classList.remove('active-plan'));
        const btn = document.getElementById('gen-plan-' + planId);
        if (btn) { btn.classList.add('active-plan'); }

        document.getElementById('planGen').value = planId;
        document.getElementById('generatePayAmount').textContent = data.pay;
        document.getElementById('generateCredit').textContent = data.credit;
        document.getElementById('generateInfo').style.display = 'block';
    } else {
        document.querySelectorAll('.plan-btn-rec').forEach(btn => btn.classList.remove('active-plan'));
        const btn = document.getElementById('rec-plan-' + planId);
        if (btn) { btn.classList.add('active-plan'); }

        document.getElementById('planRec').value = planId;
        document.getElementById('rechargePayAmount').textContent = data.pay;
        document.getElementById('rechargeCredit').textContent = data.credit;
        document.getElementById('rechargeInfo').style.display = 'block';
    }
}

// 生成代币支付
function payGenerate() {
    const planId = parseInt(document.getElementById('planGen').value, 10);
    const invitationCode = document.getElementById('invitationCode').value;
    if (!validateInvitationCode(invitationCode)) return;

    const isConfirmed = confirm("再次提醒代币支付不可以多个代币码组合支付！如果已有代币码，可以选择充值已有代币码，您确定要获取新的代币码吗？");
    if (!isConfirmed) {
        return;
    }

    // 显示加载转圈和白色背景
    document.getElementById('loadingOverlay').style.display = 'block';

    fetch('generate-payment-tokenvault-safety', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mode: 'generate', planId: planId, invitationCode: invitationCode })
    })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                // 隐藏加载转圈和白色背景
                document.getElementById('loadingOverlay').style.display = 'none';
                showPayModal(data.payQrcode, data.payAmount, data.outTradeNo, 'generate',data.qrcode);
            } else {
                // 隐藏加载转圈和白色背景
                document.getElementById('loadingOverlay').style.display = 'none';
                alert(data.message || '生成支付订单失败');
            }
        })
        .catch(err => {
            console.error(err);
            // 隐藏加载转圈和白色背景
            document.getElementById('loadingOverlay').style.display = 'none';
            alert('网络异常，请稍后重试');
        });
}

// 充值代币支付
function payRecharge() {
    const planId = parseInt(document.getElementById('planRec').value, 10);
    const code = document.getElementById('existCode').value.trim();
    if (!code) {
        alert('请输入要充值的代币激活码');
        return;
    }

    // 显示加载转圈和白色背景
    document.getElementById('loadingOverlay').style.display = 'block';

    fetch('generate-payment-tokenvault-safety', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mode: 'recharge', planId: planId, code: code})
    })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                // 隐藏加载转圈和白色背景
                document.getElementById('loadingOverlay').style.display = 'none';
                showPayModal(data.payQrcode, data.payAmount, data.outTradeNo, 'recharge',data.qrcode);
            } else {
                // 隐藏加载转圈和白色背景
                document.getElementById('loadingOverlay').style.display = 'none';
                alert(data.message || '生成支付订单失败');
            }
        })
        .catch(err => {
            console.error(err);
            // 隐藏加载转圈和白色背景
            document.getElementById('loadingOverlay').style.display = 'none';
            alert('网络异常，请稍后重试');
        });
}


function showResult(mode, data) {
    if (mode === 'generate') {
        alert('购买代币成功！代币码信息已显示在下方')
        const genRes = document.getElementById('generateResult');
        genRes.style.display = 'block';
        genRes.innerHTML = `
            <div class="result-box">
                <h5 style="color: #007bff; margin-bottom: 15px;">代币生成成功！</h5>
                <div class="mb-2">您的代币码：</div>
                <div class="code-row">
                    <span class="code-text">${data.code || '未知'}</span>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" 
                            onclick="copyCode('${data.code}')">复制</button>
                </div>
                <div class="mt-2">
                    当前余额：<span class="balance-text">${data.balance || 0} 代币</span>
                </div>
                <div class="note-text">请妥善保存此激活码,可在首页点击【查询代币激活码】，随时查询代币码使用信息</div>
            </div>
        `;
    } else {
        alert('充值成功！代币码信息已显示在下方')
        const recRes = document.getElementById('rechargeResult');
        recRes.style.display = 'block';
        recRes.innerHTML = `
            <div class="result-box">
                <h5 style="color: #28a745; margin-bottom: 15px;">充值成功！</h5>
                <div class="mb-2">您的代币码：</div>
                <div class="code-row">
                    <span class="code-text">${data.code || '未知'}</span>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" 
                            onclick="copyCode('${data.code}')">复制</button>
                </div>
                <div class="mt-2">
                    当前余额：<span class="balance-text">${data.balance || 0} 代币</span>
                </div>
                <div class="note-text">可在首页点击【查询代币激活码】，随时查询代币码使用信息</div>
            </div>
        `;
    }
}

// 复制功能
function copyCode(code) {
    if (!code || code === '未知') {
        alert('无效的代币码');
        return;
    }

    navigator.clipboard.writeText(code).then(() => {
        const buttons = document.getElementsByClassName('copy-btn');
        Array.from(buttons).forEach(btn => {
            if (btn.textContent.includes(code)) {
                const originalText = btn.innerHTML;
                btn.innerHTML = '✓ 已复制';
                btn.classList.add('text-success');
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('text-success');
                }, 1500);
            }
        });
        alert('复制成功！'); // 新增alert提示
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择复制');
    });
}
// 设备检测函数
function isMobileDevice() {
    // 优先使用现代API检测
    if (typeof window.orientation !== 'undefined' || navigator.maxTouchPoints > 0) {
        return true;
    }

    // 更全面的UA检测
    const ua = navigator.userAgent;
    const isMobile = /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(ua);
    const isTablet = /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino|android|ipad|playbook|silk/i.test(ua);

    // 屏幕尺寸辅助判断
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const isSmallScreen = screenWidth < 768 || screenHeight < 768;

    // 综合判断逻辑
    return (isMobile || isTablet) && isSmallScreen;
}
// 显示支付模态框 + 启动倒计时 + 启动轮询
function showPayModal(qrUrl, payAmount, outTradeNo, mode,qrcode) {
    // 设置二维码和金额
    document.getElementById('payQrImg').src = qrUrl;
    document.getElementById('payAmountText').textContent = payAmount.toFixed(2);
    document.getElementById('payStatusHint').textContent = '';

    // 移动端处理
    const mobileSection = document.getElementById('mobilePaySection');
    const alipayBtn = document.getElementById('alipayLaunchBtn');

    if (isMobileDevice()) {
        mobileSection.style.display = 'block';
        const alipayUrl = qrcode;
        // 绑定点击事件
        alipayBtn.onclick = function() {
            // window.location.href = alipayUrl;
            window.open(alipayUrl, '_blank');
        };
    } else {
        mobileSection.style.display = 'none';
    }


    // Bootstrap Modal
    const payModalEl = document.getElementById('payModal');
    const payModal = new bootstrap.Modal(payModalEl, { backdrop: 'static' });
    payModal.show();

    // 启动倒计时
    startPaymentCountdown(paymentTimeoutDuration, payModal);

    // 启动轮询
    startPaymentStatusPolling(outTradeNo, mode, payModal);
}

// 倒计时
function startPaymentCountdown(duration, payModal) {
    const paymentCountdownElement = document.getElementById('payment-countdown');
    let timeRemaining = duration;

    clearInterval(paymentCountdownInterval);
    updateCountdownDisplay(timeRemaining);

    paymentCountdownInterval = setInterval(() => {
        timeRemaining -= 1000;
        if (timeRemaining <= 0) {
            clearInterval(paymentCountdownInterval);
            paymentCountdownElement.textContent = '00:00';
            alert('支付超时，请重新支付。');
            payModal.hide();
            clearInterval(paymentStatusCheckInterval);
        } else {
            updateCountdownDisplay(timeRemaining);
        }
    }, 1000);
}

function updateCountdownDisplay(timeRemaining) {
    const paymentCountdownElement = document.getElementById('payment-countdown');
    const totalSeconds = Math.floor(timeRemaining / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
    const formattedSeconds = seconds < 10 ? '0' + seconds : seconds;
    paymentCountdownElement.textContent = formattedMinutes + ':' + formattedSeconds;
}

// 轮询支付状态
function startPaymentStatusPolling(outTradeNo, mode, payModal) {
    let attempts = 0;
    const maxAttempts = Math.ceil(paymentTimeoutDuration / 4000);
    const interval = 4000;

    clearInterval(paymentStatusCheckInterval);
    paymentStatusCheckInterval = setInterval(() => {
        attempts++;
        if (attempts > maxAttempts) {
            clearInterval(paymentStatusCheckInterval);
            document.getElementById('payStatusHint').textContent = '支付超时，请重新支付。';
            return;
        }

        fetch(`checkPaymentForTokenvaultSafety?outTradeNo=${outTradeNo}`)
            .then(res => res.json())
            .then(data => {
                if (data.paid === true) {
                    clearInterval(paymentStatusCheckInterval);
                    payModal.hide();
                    showResult(mode, data);
                }
            })
            .catch(err => console.error(err));
    }, interval);
}



// 监听支付模态框关闭事件 => 停止计时和轮询
document.getElementById('payModal').addEventListener('hidden.bs.modal', function() {
    clearInterval(paymentCountdownInterval);
    clearInterval(paymentStatusCheckInterval);
});

// 默认：生成代币模式 & planId=1
switchMode('recharge');
selectPlan('generate', 1);
selectPlan('recharge', 1);

function getQueryParam(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// 初始化邀请码输入
const refCode = getQueryParam('ref');
if (refCode) {
    document.getElementById('invitationCode').value = refCode;
    validateInvitationCode(refCode);
    switchMode('generate');
}

// 实时验证邀请码
document.getElementById('invitationCode').addEventListener('input', function(e) {
    validateInvitationCode(e.target.value);
});

function validateInvitationCode(code) {
    const errorDiv = document.getElementById('invitationError');
    if (code && code.length !== 8) {
        errorDiv.style.display = 'block';
        return false;
    }
    errorDiv.style.display = 'none';
    return true;
}

package com.example.springboot.mapper;

import com.example.springboot.entity.AiChatSession;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface AIChatSessionMapper {
    
    @Insert("INSERT INTO ai_chat_session (uuid, session_name, token_code, survey_link, excel_data, create_time, update_time, token_consumed, is_deleted) " +
            "VALUES (#{uuid}, #{sessionName}, #{tokenCode}, #{surveyLink}, #{excelData},  #{createTime}, #{updateTime}, #{tokenConsumed}, #{isDeleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(AiChatSession session);

    @Select("SELECT s.*, (SELECT MAX(create_time) FROM ai_chat_message WHERE session_id = s.uuid) as lastMessageTime " +
            "FROM ai_chat_session s " +
            "WHERE s.token_code = #{tokenCode} AND s.is_deleted = 0 ORDER BY lastMessageTime DESC, s.create_time DESC")
    List<AiChatSession> findByTokenCode(String tokenCode);

    @Select("SELECT * FROM ai_chat_session WHERE id = #{id} AND is_deleted = 0")
    AiChatSession findById(Long id);

    @Select("SELECT * FROM ai_chat_session WHERE uuid = #{uuid} AND is_deleted = 0")
    AiChatSession findByUuid(String uuid);

    @Update("UPDATE ai_chat_session SET excel_data = #{excelData}, update_time = #{updateTime} WHERE id = #{id}")
    void updateExcelData(@Param("id") Long id, @Param("excelData") String excelData, @Param("updateTime") LocalDateTime updateTime);


    @Update("UPDATE ai_chat_session SET token_consumed = token_consumed + #{amount} WHERE id = #{id}")
    void updateTokenConsumed(@Param("id") Long id, @Param("amount") int amount);

    @Update("UPDATE ai_chat_session SET session_name = #{sessionName}, update_time = #{updateTime} WHERE id = #{id}")
    void updateSessionName(AiChatSession session);

    @Update("UPDATE ai_chat_session SET is_deleted = #{isDeleted}, update_time = #{updateTime} WHERE id = #{id}")
    void updateSessionDeleted(AiChatSession session);

    @Update("UPDATE ai_chat_session SET last_message_time = #{lastMessageTime}, update_time = #{updateTime} WHERE id = #{id}")
    void updateLastMessageTime(@Param("id") Long id, @Param("lastMessageTime") LocalDateTime lastMessageTime, @Param("updateTime") LocalDateTime updateTime);

    @Select("SELECT COALESCE(SUM(token_consumed), 0) FROM ai_chat_session WHERE token_code = #{tokenCode} AND is_deleted = 0")
    int getTotalTokenConsumedByTokenCode(String tokenCode);
} 
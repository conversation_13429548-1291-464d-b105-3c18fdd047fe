package com.example.springboot.mapper;

import com.example.springboot.entity.AiChatMessage;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface AIChatMessageMapper {

    @Insert("INSERT INTO ai_chat_message (session_id, role, content, create_time, message_order, data_modifications, config_text, complete_excel_data, table_data) "
            +
            "VALUES (#{sessionId}, #{role}, #{content}, NOW(), #{messageOrder}, #{dataModifications}, #{configText}, #{completeExcelData}, #{tableData})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(AiChatMessage message);

    @Select("SELECT * FROM ai_chat_message WHERE session_id = #{sessionId} ORDER BY message_order ASC")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "sessionId", column = "session_id"),
            @Result(property = "role", column = "role"),
            @Result(property = "content", column = "content"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "messageOrder", column = "message_order"),
            @Result(property = "dataModifications", column = "data_modifications"),
            @Result(property = "configText", column = "config_text"),
            @Result(property = "completeExcelData", column = "complete_excel_data"),
            @Result(property = "tableData", column = "table_data")
    })
    List<AiChatMessage> findBySessionId(String sessionId);

    @Select("SELECT * FROM ai_chat_message WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "sessionId", column = "session_id"),
            @Result(property = "role", column = "role"),
            @Result(property = "content", column = "content"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "messageOrder", column = "message_order"),
            @Result(property = "dataModifications", column = "data_modifications"),
            @Result(property = "configText", column = "config_text"),
            @Result(property = "completeExcelData", column = "complete_excel_data"),
            @Result(property = "tableData", column = "table_data")
    })
    AiChatMessage findMessageById(Long id);

    @Delete("DELETE FROM ai_chat_message WHERE session_id = #{sessionId}")
    void deleteMessagesBySessionId(String sessionId);

    @Select("SELECT COALESCE(MAX(message_order), 0) FROM ai_chat_message WHERE session_id = #{sessionId}")
    int getMaxMessageOrder(String sessionId);

    List<AiChatMessage> findBySessionId(Long sessionId);

    void updateDataModifications(@Param("id") Long id, @Param("dataModifications") String dataModifications);

    void updateCompleteExcelData(@Param("id") Long id, @Param("completeExcelData") String completeExcelData);

    @Select("SELECT * FROM ai_chat_message WHERE session_id = #{sessionId}  AND complete_excel_data IS NOT NULL AND complete_excel_data != '' ORDER BY message_order DESC LIMIT 1")
    AiChatMessage findLatestBySessionId(String sessionId);

    /**
     * 查找会话中包含数据修改的消息
     */
    @Select("SELECT * FROM ai_chat_message " +
            "WHERE session_id = #{sessionId} " +
            "AND data_modifications IS NOT NULL " +
            "AND TRIM(data_modifications) != '' " +
            "AND TRIM(data_modifications) != '[]' " +
            "AND complete_excel_data IS NOT NULL " +
            "ORDER BY create_time DESC")
    List<AiChatMessage> findBySessionIdAndHasModifications(@Param("sessionId") String sessionId);

    /**
     * 查找会话中包含Excel数据的消息
     */
    @Select("SELECT * FROM ai_chat_message WHERE session_id = #{sessionId} AND complete_excel_data IS NOT NULL ORDER BY create_time DESC")
    List<AiChatMessage> findBySessionIdAndHasExcel(@Param("sessionId") String sessionId);

    /**
     * 根据ID查找消息
     */
    @Select("SELECT * FROM ai_chat_message WHERE id = #{id}")
    AiChatMessage findById(@Param("id") Long id);

    /**
     * 获取最近N条消息，但排除最后一条（当前正在处理的消息）
     */
    @Select("SELECT * FROM ai_chat_message WHERE session_id = #{sessionId} ORDER BY message_order DESC LIMIT #{limit} OFFSET 1")
    List<AiChatMessage> findRecentMessagesExcludeLast(@Param("sessionId") String sessionId, @Param("limit") int limit);

    /**
     * 更新消息
     */
    @Update("UPDATE ai_chat_message SET " +
            "session_id = #{sessionId}, " +
            "role = #{role}, " +
            "create_time = #{createTime}, " +
            "message_order = #{messageOrder}, " +
            "data_modifications = #{dataModifications}, " +
            "config_text = #{configText}, " +
            "content = #{content}, " +
            "complete_excel_data = #{completeExcelData}, " +
            "table_data = #{tableData} " +
            "WHERE id = #{id}")
    void update(AiChatMessage message);

    /**
     * 查找最新的等待AI填充的消息（用于现有的saveTableDataToMessage方法）
     * 保持原有的查询逻辑不变
     */
    @Select("SELECT * FROM ai_chat_message " +
            "WHERE session_id = #{sessionId} " +
            "AND content = '等待AI填充' " +
            "ORDER BY create_time DESC LIMIT 1")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "sessionId", column = "session_id"),
            @Result(property = "role", column = "role"),
            @Result(property = "content", column = "content"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "messageOrder", column = "message_order"),
            @Result(property = "dataModifications", column = "data_modifications"),
            @Result(property = "configText", column = "config_text"),
            @Result(property = "completeExcelData", column = "complete_excel_data"),
            @Result(property = "tableData", column = "table_data")
    })
    AiChatMessage findLatestWaitingFillBySessionId(@Param("sessionId") String sessionId);

    /**
     * 查找最新的等待数据调整填充的消息（专门用于逆向数据调整）
     */
    @Select("SELECT * FROM ai_chat_message " +
            "WHERE session_id = #{sessionId} " +
            "AND role = 'assistant' " +
            "AND (content IS NULL OR content = '' OR content = '数据调整中...' OR content = '正在调整数据...') " +
            "ORDER BY message_order DESC LIMIT 1")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "sessionId", column = "session_id"),
            @Result(property = "role", column = "role"),
            @Result(property = "content", column = "content"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "messageOrder", column = "message_order"),
            @Result(property = "dataModifications", column = "data_modifications"),
            @Result(property = "configText", column = "config_text"),
            @Result(property = "completeExcelData", column = "complete_excel_data"),
            @Result(property = "tableData", column = "table_data")
    })
    AiChatMessage findLatestDataAdjustmentWaitingBySessionId(@Param("sessionId") String sessionId);

    @Select("SELECT * FROM ai_chat_message WHERE session_id = #{sessionId} AND role = 'assistant' ORDER BY create_time DESC LIMIT 1")
    AiChatMessage findLatestAssistantMessage(@Param("sessionId") String sessionId);

    /**
     * 查找最近的几条assistant消息
     */
    @Select("SELECT * FROM ai_chat_message WHERE session_id = #{sessionId} AND role = 'assistant' ORDER BY create_time DESC LIMIT #{limit}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "sessionId", column = "session_id"),
            @Result(property = "role", column = "role"),
            @Result(property = "content", column = "content"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "messageOrder", column = "message_order"),
            @Result(property = "dataModifications", column = "data_modifications"),
            @Result(property = "configText", column = "config_text"),
            @Result(property = "completeExcelData", column = "complete_excel_data"),
            @Result(property = "tableData", column = "table_data")
    })
    List<AiChatMessage> findRecentAssistantMessages(@Param("sessionId") String sessionId, @Param("limit") int limit);
}
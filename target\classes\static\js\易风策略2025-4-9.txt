// ==UserScript==
// @name         问卷星(定制比例)VM模板（2023最新版！！！）代码生成器2 不加密版
// @namespace    http://tampermonkey.net/
// @version      5.0
// @description  可定制每个选项比例概率，刷问卷前需要改代码，目前模板支持单选,多选,填空,量表，下拉框题，如有其它高级题型可进群定制脚本，使用需要一定js知识，不懂的可以加QQ群865248256交流，本群也提供定制脚本刷问卷服务，服务快捷，价格优惠。https://www.wjx.cn/vj/QvfxoEU.aspx 是测试脚本问卷。如遇问题可加QQ 835573228
// <AUTHOR>
// @match     https://www.wjx.cn/*
// @match     https://www.ks.wjx.top/*
// @icon       http://************:9090/favicon.ico
// ==/UserScript==

(function() {
    'use strict';

    //填写刷问卷的网址  注意，如果问卷中的网址中间是vj,一定要改成vm!!!,像这样 https://www.wjx.cn/vm/QvfxoEU.aspx
    var wenjuan_url = 'https://www.wjx.cn/vm/w7NWA00.aspx';

    //------------------------------下边的网址不要改！！！！！！！！！！！！！！！！！！！！
    if(window.location.href.indexOf('https://www.wjx.cn/wjx/join/completemobile')!=-1){
        // 调用函数以删除所有cookie
        deleteAllCookies();
        window.location.href=wenjuan_url;
    }else if(window.location.href==wenjuan_url){
    }else{
        return
    }

    //start...

    //滚动到末尾
    window.scrollTo(0,document.body.scrollHeight)

    //获取题块列表
    var lists = document.querySelectorAll('.fieldset > div[class="field ui-field-contain"]')
    var ccc=0;
    var liangbiao_index=0;
    var xiala_index=0;
    var ops;
    var bili;
    var temp_flag;
    var tiankong_list;
    var liangbiao_lists;
    var min_options;
    var array;
    var toupiao;
    var temp_answer;
    var temp_answer2
    var bili8_2,bili8_3,bili8_4,bili8_5,bili8_6,bili9_2,bili9_3,bili9_4,bili9_5,bili9_6,bili9p_2,bili9p_3,bili9p_4,bili9p_5,bili9p_6,reverse_list

    init()
    async function init() {

        //第1题
        tiankong_list = ["@@随机数(10","20)@@"];
        ccc+=1
        document.querySelector('#q1').value=randomNum(10,20);

        //第2题
        ops = lists[ccc].querySelectorAll('.ui-radio');
        ccc+=1
        bili = [50, 50];
        ops[danxuan(bili)].click()

        //第3题
        tiankong_list = ["@@生成随机手机号@@"];
        ccc+=1
        document.querySelector('#q3').value=getMoble();

        //第4题
        ops = lists[ccc].querySelectorAll('.ui-radio');
        ccc+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()

        //第5题
        ops = lists[ccc].querySelectorAll('.ui-checkbox')
        ccc+=1
        bili = [50, 50, 50, 50, 50];
        temp_flag = false
        while(!temp_flag){
            for(let count = 0;count<bili.length;count++){
                if(duoxuan(bili[count])){
                    ops[count].click();
                    temp_flag = true;
                }
            }
        }

        //第6题
        ops = lists[ccc].querySelectorAll('.ui-radio');
        ccc+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()

        //第7题
        ops = lists[ccc].querySelectorAll('.ui-checkbox')
        ccc+=1
        bili = [50, 50, 50, 50, 50, 50, 50];
        temp_flag = false
        while(!temp_flag){
            for(let count = 0;count<bili.length;count++){
                if(duoxuan(bili[count])){
                    ops[count].click();
                    temp_flag = true;
                }
            }
        }

        //第8题
        liangbiao_lists = document.querySelectorAll('#div8 tbody tr[tp="d"]')
        ccc+=1
        liangbiao_index=0
        //8-1
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()
        //8-2
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()
        //8-3
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()

        //第9题
        liangbiao_lists = document.querySelectorAll('#div9 tbody tr[tp="d"]')
        ccc+=1
        liangbiao_index=0
        //9-1
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [50, 50, 50, 50];
        temp_flag = false
        while(!temp_flag){
            for(let count = 0;count<bili.length;count++){
                if(duoxuan(bili[count])){
                    ops[count].click();
                    temp_flag = true;
                }
            }
        }
        //9-2
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [50, 50, 50, 50];
        temp_flag = false
        while(!temp_flag){
            for(let count = 0;count<bili.length;count++){
                if(duoxuan(bili[count])){
                    ops[count].click();
                    temp_flag = true;
                }
            }
        }
        //9-3
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [50, 50, 50, 50];
        temp_flag = false
        while(!temp_flag){
            for(let count = 0;count<bili.length;count++){
                if(duoxuan(bili[count])){
                    ops[count].click();
                    temp_flag = true;
                }
            }
        }

        //第10题
        liangbiao_lists = document.querySelectorAll('#div10 tbody tr[tp="d"]')
        ccc+=1
        liangbiao_index=0
        //10-1
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [25, 25, 25, 25];
        ops[danxuan(bili)].click()
        //10-2
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [25, 25, 25, 25];
        ops[danxuan(bili)].click()
        //10-3
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [25, 25, 25, 25];
        ops[danxuan(bili)].click()

        //第11题
        ops = lists[ccc].querySelectorAll('.ui-checkbox')
        ccc+=1
        bili = [50, 50, 50, 50, 50, 50, 50];
        temp_flag = false
        while(!temp_flag){
            for(let count = 0;count<bili.length;count++){
                if(duoxuan(bili[count])){
                    ops[count].click();
                    temp_flag = true;
                }
            }
        }

        //第12题
        liangbiao_lists = document.querySelectorAll('#div12 tbody tr[tp="d"]')
        ccc+=1
        liangbiao_index=0
        //12-1
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()
        //12-2
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()

        //第13题
        tiankong_list = ["@@随机数(10","20)@@"];
        ccc+=1
        document.querySelector('#q13').value=randomNum(10,20);

        //第14题
        xiala_click(document.querySelectorAll('.select2-selection.select2-selection--single')[xiala_index])
        xiala_index+=1
        ccc+=1
        ops = document.querySelectorAll('#select2-q14-results li')
        ops = Array.prototype.slice.call(ops); //非ie浏览器正常
        ops = ops.slice(1,ops.length);
        bili = [20, 20, 20, 20, 20];
        xialaElement_click(ops[danxuan(bili)])

        //第15题
        xiala_click(document.querySelectorAll('.select2-selection.select2-selection--single')[xiala_index])
        xiala_index+=1
        ccc+=1
        ops = document.querySelectorAll('#select2-q15-results li')
        ops = Array.prototype.slice.call(ops); //非ie浏览器正常
        ops = ops.slice(1,ops.length);
        xialaElement_click(ops[randomNum(0,ops.length-1)])

        //第16题
        //排序题比例的逻辑：给每个选项一个权重，比如有三个选项，[90，60，30]，一轮概率过去后，投票最多的先选，如果平局，再来一轮投票,直到选出一个，
        //剩下的继续按概率投票权重要是100，那必然是第一个选出来的（所以100最多只能有一个，不然多个100会造成一直平局而陷入死循环），剩下的就继续比，这里的比例是权重的意思
        ops = lists[ccc].querySelectorAll('.ui-li-static')
        ccc+=1
        bili = [50, 50, 50, 50, 50, 50, 50];
        temp_answer = voteByBili(bili)
        for(let count = 0;count<temp_answer.length;count++){
            document.querySelectorAll('#div16 .ui-li-static')[temp_answer[count]].click();
            await sleep(0.5)
        }

        //第17题
        ops = [0, 20, 40, 60, 80, 100];
        bili = [16, 16, 16, 16, 16, 20];
        ccc+=1
        document.querySelector('#q17').value=ops[danxuan(bili)]

        //第18题
        ops = [0, 20, 40, 60, 80, 100];
        bili = [16, 16, 16, 16, 16, 20];
        ccc+=1
        document.querySelector('#q18').value=ops[danxuan(bili)]

        //第19题
        ops = lists[ccc].querySelectorAll('li[class="td"]');
        ccc+=1
        bili = [9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 10];
        ops[danxuan(bili)].click()

        //第20题
        ops = lists[ccc].querySelectorAll('li[class="td"]');
        ccc+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()

        //第21题
        ops = [0, 10, 20, 30, 40, 50];
        bili = [16, 16, 16, 16, 16, 20];
        ccc+=1
        document.querySelector('#q21').value=ops[danxuan(bili)]

        //第22题
        ops = lists[ccc].querySelectorAll('li[class="td"]');
        ccc+=1
        bili = [12, 12, 12, 12, 12, 12, 12, 16];
        ops[danxuan(bili)].click()

        //第23题
        ccc+=1
        //23-1
        ops = [0, 20, 40, 60, 80, 100];
        bili = [16, 16, 16, 16, 16, 20];
        document.querySelectorAll('#div23 input')[0].value=ops[danxuan(bili)]
        //23-2
        ops = [0, 20, 40, 60, 80, 100];
        bili = [16, 16, 16, 16, 16, 20];
        document.querySelectorAll('#div23 input')[1].value=ops[danxuan(bili)]
        //23-3
        ops = [0, 20, 40, 60, 80, 100];
        bili = [16, 16, 16, 16, 16, 20];
        document.querySelectorAll('#div23 input')[2].value=ops[danxuan(bili)]
        //23-4
        ops = [0, 20, 40, 60, 80, 100];
        bili = [16, 16, 16, 16, 16, 20];
        document.querySelectorAll('#div23 input')[3].value=ops[danxuan(bili)]
        //开始归一化
        array = []
        array.push( document.querySelectorAll('#div23 input')[0].value)
        array.push( document.querySelectorAll('#div23 input')[1].value)
        array.push( document.querySelectorAll('#div23 input')[2].value)
        array.push( document.querySelectorAll('#div23 input')[3].value)
        array = normalizeList(array)
        document.querySelectorAll('#div23 input')[0].value=array[0]
        document.querySelectorAll('#div23 input')[1].value=array[1]
        document.querySelectorAll('#div23 input')[2].value=array[2]
        document.querySelectorAll('#div23 input')[3].value=array[3]

        //第24题
        ops = lists[ccc].querySelectorAll('li[class="td"]');
        ccc+=1
        bili = [20, 20, 20, 20, 20];
        ops[danxuan(bili)].click()

        //第25题
        ops = lists[ccc].querySelectorAll('.ui-radio');
        ccc+=1
        bili = [50, 50];
        ops[danxuan(bili)].click()

        //第26题
        ccc+=1
        //26-1
        ops = [0, 20, 40, 60, 80, 100];
        bili = [16, 16, 16, 16, 16, 20];
        document.querySelectorAll('#div26 input')[0].value=ops[danxuan(bili)]
        //26-2
        ops = [0, 20, 40, 60, 80, 100];
        bili = [16, 16, 16, 16, 16, 20];
        document.querySelectorAll('#div26 input')[1].value=ops[danxuan(bili)]

        //第27题
        ops = [0, 16, 32, 48, 64, 80];
        bili = [16, 16, 16, 16, 16, 20];
        ccc+=1
        document.querySelector('#q27').value=ops[danxuan(bili)]

        //第28题
        //排序题比例的逻辑：给每个选项一个权重，比如有三个选项，[90，60，30]，一轮概率过去后，投票最多的先选，如果平局，再来一轮投票,直到选出一个，
        //剩下的继续按概率投票权重要是100，那必然是第一个选出来的（所以100最多只能有一个，不然多个100会造成一直平局而陷入死循环），剩下的就继续比，这里的比例是权重的意思
        ops = lists[ccc].querySelectorAll('.ui-li-static')
        ccc+=1
        bili = [50, 50, 50, 50, 50, 50, 50];
        temp_answer = voteByBili(bili)
        for(let count = 0;count<temp_answer.length;count++){
            document.querySelectorAll('#div28 .ui-li-static')[temp_answer[count]].click();
            await sleep(0.5)
        }

        //第29题
        ccc+=1
        //29-1
        ops = [0, 18, 36, 54, 72, 90];
        bili = [16, 16, 16, 16, 16, 20];
        document.querySelectorAll('#div29 input')[0].value=ops[danxuan(bili)]
        //29-2
        ops = [0, 18, 36, 54, 72, 90];
        bili = [16, 16, 16, 16, 16, 20];
        document.querySelectorAll('#div29 input')[1].value=ops[danxuan(bili)]


        /*
    let count = 0
    //提交函数
    setTimeout( function(){
        document.querySelector('#ctlNext').click()
        //let num = parseInt(getCookie('count'));
        //num++;
        //document.cookie = "count="+num;
        setTimeout( function(){
            document.querySelector('#rectMask').click()
            setInterval( function(){
                try{
                    //点击刷新验证框
                    //noCaptcha.reset(1)
                    yanzhen();
                    count+=1;
                }
                catch(err){
                    if(count>=6){
                        location.reload()
                    }
                }
            }, 500 );
        }, 0.1 * 1000 );
    }, 0.1 * 1000 );
    /*
    */

    }



    /*
    //---------------------------------------------------------------------------------------------------

    //单选题模板
    ops = lists[ccc].querySelectorAll('.ui-radio')
    ccc+=1
    bili = [];
    ops[danxuan(bili)].click()

    //---------------------------------------------------------------------------------------------------

    //多选题模板（至少选一个选项）
    ops = lists[ccc].querySelectorAll('.ui-checkbox')
    ccc+=1
    bili = [];
    temp_flag = false

    while(!temp_flag){
        for(let count = 0;count<bili.length;count++){
            if(duoxuan(bili[count])){
                ops[count].click();
                temp_flag = true;
            }
        }
    }

    //---------------------------------------------------------------------------------------------------

    //多选题模板（可自定义至少选一个选项）
    ops = lists[ccc].querySelectorAll('.ui-checkbox');
    ccc+=1;
    bili = [];
    min_options = 3  //设置最少选择的项数
    temp_flag = 0;
    while(temp_flag<min_options){
        let temp_answer = []
        for(let count = 0;count<bili.length;count++){
            if(duoxuan(bili[count])){
                temp_answer.push(count)
                temp_flag+=1
            }
            if(count==bili.length-1){
                if(temp_flag<min_options){
                    temp_flag = 0
                }
                else{
                    for(let count = 0;count<temp_answer.length;count++){
                        ops[temp_answer[count]].click();
                    }
                }
            }
        }
    }

    //---------------------------------------------------------------------------------------------------

    //填空随机城市模板
    tiankong_list = ['北京-东城区', '北京-西城区', '北京-朝阳区', '北京-丰台区', '北京-石景山区', '北京-海淀区', '北京-门头沟区', '北京-房山区', '北京-通州区', '北京-顺义区', '北京-昌平区', '北京-大兴区', '北京-怀柔区', '北京-平谷区', '北京-密云区', '北京-延庆区', '天津-和平区', '天津-河东区', '天津-河西区', '天津-南开区', '天津-河北区', '天津-红桥区', '天津-东丽区', '天津-西青区', '天津-津南区', '天津-北辰区', '天津-武清区', '天津-宝坻区', '天津-滨海新区', '天津-宁河区', '天津-静海区', '天津-蓟州区', '河北-石家庄', '河北-唐山', '河北-秦皇岛', '河北-邯郸', '河北-邢台', '河北-保定', '河北-张家口', '河北-承德', '河北-沧州', '河北-廊坊', '河北-衡水', '山西-太原', '山西-大同', '山西-阳泉', '山西-长治', '山西-晋城', '山 西-朔州', '山西-晋中', '山西-运城', '山西-忻州', '山西-临汾', '山西-吕梁', '内蒙古-呼和浩特', '内蒙古-包头', '内蒙古-乌 海', '内蒙古-赤峰', '内蒙古-通辽', '内蒙古-鄂尔多斯', '内蒙古-呼伦贝尔', '内蒙古-巴彦淖尔', '内蒙古-乌兰察布', '内蒙古- 兴安盟', '内蒙古-锡林郭勒盟', '内蒙古-阿拉善盟', '辽宁-沈阳', '辽宁-大连', '辽宁-鞍山', '辽宁-抚顺', '辽宁-本溪', '辽宁-丹东', '辽宁-锦州', '辽宁-营口', '辽宁-阜新', '辽宁-辽阳', '辽宁-盘锦', '辽宁-铁岭', '辽宁-朝阳', '辽宁-葫芦岛', '吉林- 长春', '吉林-吉林', '吉林-四平', '吉林-辽源', '吉林-通化', '吉林-白山', '吉林-松原', '吉林-白城', '吉林-延边', '黑龙江- 哈尔滨', '黑龙江-齐齐哈尔', '黑龙江-鸡西', '黑龙江-鹤岗', '黑龙江-双鸭山', '黑龙江-大庆', '黑龙江-伊春', '黑龙江-佳木斯', '黑龙江-七台河', '黑龙江-牡丹江', '黑龙江-黑河', '黑龙江-绥化', '黑龙江-大兴安岭', '上海-黄浦区', '上海-徐汇区', '上海-长宁区', '上海-静安区', '上海-普陀区', '上海-虹口区', '上海-杨浦区', '上海-闵行区', '上海-宝山区', '上海-嘉定区', '上海-浦东新区', '上海-金山区', '上海-松江区', '上海-青浦区', '上海-奉贤区', '上海-崇明区', '江苏-南京', '江苏-无锡', '江苏- 徐州', '江苏-常州', '江苏-苏州', '江苏-南通', '江苏-连云港', '江苏-淮安', '江苏-盐城', '江苏-扬州', '江苏-镇江', '江苏- 泰州', '江苏-宿迁', '浙江-杭州', '浙江-宁波', '浙江-温州', '浙江-嘉兴', '浙江-湖州', '浙江-绍兴', '浙江-金华', '浙江-衢 州', '浙江-舟山', '浙江-台州', '浙江-丽水', '浙江-舟山新区', '安徽-合肥', '安徽-芜湖', '安徽-蚌埠', '安徽-淮南', '安徽- 马鞍山', '安徽-淮北', '安徽-铜陵', '安徽-安庆', '安徽-黄山', '安徽-滁州', '安徽-阜阳', '安徽-宿州', '安徽-六安', '安徽- 亳州', '安徽-池州', '安徽-宣城', '福建-福州', '福建-厦门', '福建-莆田', '福建-三明', '福建-泉州', '福建-漳州', '福建-南 平', '福建-龙岩', '福建-宁德', '江西-南昌', '江西-景德镇', '江西-萍乡', '江西-九江', '江西-新余', '江西-鹰潭', '江西-赣 州', '江西-吉安', '江西-宜春', '江西-抚州', '江西-上饶', '山东-济南', '山东-青岛', '山东-淄博', '山东-枣庄', '山东-东营', '山东-烟台', '山东-潍坊', '山东-济宁', '山东-泰安', '山东-威海', '山东-日照', '山东-临沂', '山东-德州', '山东-聊城', '山东-滨州', '山东-菏泽', '河南-郑州', '河南-开封', '河南-洛阳', '河南-平顶山', '河南-安阳', '河南-鹤壁', '河南-新乡', ' 河南-焦作', '河南-濮阳', '河南-许昌', '河南-漯河', '河南-三门峡', '河南-南阳', '河南-商丘', '河南-信阳', '河南-周口', ' 河南-驻马店', '河南-济源', '河南-郑州', '河南-开封', '河南-洛阳', '河南-平顶山', '河南-安阳', '河南-鹤壁', '河南-新乡', '河南-焦作', '河南-濮阳', '河南-许昌', '河南-漯河', '河南-三门峡', '河南-南阳', '河南-商丘', '河南-信阳', '河南-周口', '河南-驻马店', '河南-济源', '湖北-武汉', '湖北-黄石', '湖北-十堰', '湖北-宜昌', '湖北-襄阳', '湖北-鄂州', '湖北-荆门', ' 湖北-孝感', '湖北-荆州', '湖北-黄冈', '湖北-咸宁', '湖北-随州', '湖北-恩施', '湖北-仙桃', '湖北-潜江', '湖北-天门', '湖 北-神农架', '湖南-长沙', '湖南-株洲', '湖南-湘潭', '湖南-衡阳', '湖南-邵阳', '湖南-岳阳', '湖南-常德', '湖南-张家界', ' 湖南-益阳', '湖南-郴州', '湖南-永州', '湖南-怀化', '湖南-娄底', '湖南-湘西', '广东-广州', '广东-韶关', '广东-深圳', '广 东-珠海', '广东-汕头', '广东-佛山', '广东-江门', '广东-湛江', '广东-茂名', '广东-肇庆', '广东-惠州', '广东-梅州', '广东-汕尾', '广东-河源', '广东-阳江', '广东-清远', '广东-东莞', '广东-中山', '广东-潮州', '广东-揭阳', '广东-云浮', '广西-南 宁', '广西-柳州', '广西-桂林', '广西-梧州', '广西-北海', '广西-防城港', '广西-钦州', '广西-贵港', '广西-玉林', '广西-百 色', '广西-贺州', '广西-河池', '广西-来宾', '广西-崇左', '海南-海口', '海南-三亚', '海南-三沙', '海南-儋州', '海南-五指 山', '海南-琼海', '海南-文昌', '海南-万宁', '海南-东方', '海南-定安', '海南-屯昌', '海南-澄迈', '海南-临高', '海南-白沙', '海南-昌江', '海南-乐东', '海南-陵水', '海南-保亭', '海南-琼中', '重庆-万州区', '重庆-涪陵区', '重庆-渝中区', '重庆-大渡口区', '重庆-江北区', '重庆-沙坪坝区', '重庆-九龙坡区', '重庆-南岸区', '重庆-北碚区', '重庆-綦江区', '重庆-大足区', ' 重庆-渝北区', '重庆-巴南区', '重庆-黔江区', '重庆-长寿区', '重庆-江津区', '重庆-合川区', '重庆-永川区', '重庆-南川区', '重庆-璧山区', '重庆-铜梁区', '重庆-潼南区', '重庆-荣昌区', '重庆-梁平县', '重庆-城口县', '重庆-丰都县', '重庆-垫江县', '重庆-武隆县', '重庆-忠县', '重庆-开州区', '重庆-云阳县', '重庆-奉节县', '重庆-巫山县', '重庆-巫溪县', '重庆-石柱土家族自治县', '重庆-秀山土家族苗族自治县', '重庆-酉阳土家族苗族自治县', '重庆-彭水苗族土家族自治县', '重庆-两江新区', '重庆-高 新区', '重庆-璧山高新区', '四川-成都', '四川-自贡', '四川-攀枝花', '四川-泸州', '四川-德阳', '四川-绵阳', '四川-广元', '四川-遂宁', '四川-内江', '四川-乐山', '四川-南充', '四川-眉山', '四川-宜宾', '四川-广安', '四川-达州', '四川-雅安', '四 川-巴中', '四川-资阳', '四川-阿坝', '四川-甘孜', '四川-凉山', '贵州-贵阳', '贵州-六盘水', '贵州-遵义', '贵州-安顺', '贵 州-毕节', '贵州-铜仁', '贵州-黔西南', '贵州-黔东南', '贵州-黔南', '云南-昆明', '云南-曲靖', '云南-玉溪', '云南-保山', ' 云南-昭通', '云南-丽江', '云南-普洱', '云南-临沧', '云南-楚雄', '云南-红河', '云南-文山', '云南-西双版纳', '云南-大理', '云南-德宏', '云南-怒江', '云南-迪庆', '西藏-拉萨', '西藏-日喀则', '西藏-昌都', '西藏-林芝', '西藏-山南', '西藏-那曲', '西藏-阿里', '陕西-西安', '陕西-铜川', '陕西-宝鸡', '陕西-咸阳', '陕西-渭南', '陕西-延安', '陕西-汉中', '陕西-榆林', '陕 西-安康', '陕西-商洛', '陕西-西咸新区', '甘肃-兰州', '甘肃-嘉峪关', '甘肃-金昌', '甘肃-白银', '甘肃-天水', '甘肃-武威', '甘肃-张掖', '甘肃-平凉', '甘肃-酒泉', '甘肃-庆阳', '甘肃-定西', '甘肃-陇南', '甘肃-临夏', '甘肃-甘南', '青海-西宁', '青海-海东', '青海-海北', '青海-黄南', '青海-海南', '青海-果洛', '青海-玉树', '青海-海西', '宁夏-银川', '宁夏-石嘴山', '宁 夏-吴忠', '宁夏-固原', '宁夏-中卫', '新疆-乌鲁木齐', '新疆-克拉玛依', '新疆-吐鲁番', '新疆-哈密', '新疆-昌吉', '新疆-博 尔塔拉', '新疆-巴音郭楞', '新疆-阿克苏', '新疆-克孜勒苏', '新疆-喀什', '新疆-和田', '新疆-伊犁', '新疆-塔城', '新疆-阿勒泰', '新疆-石河子', '新疆-阿拉尔', '新疆-图木舒克', '新疆-五家渠', '新疆-北屯', '新疆-铁门关', '新疆-双河', '新疆-可克达拉', '新疆-昆玉', '新疆-胡杨河', '新疆-新星', '澳门-澳门半岛', '澳门-氹仔岛', '澳门-路环岛', '海外-亚洲', '海外-非洲', '海外-欧洲', '海外-美洲', '海外-大洋洲']
    ccc+=1
    document.querySelector('#q1').value=tiankong_list[randomNum(0,tiankong_list.length-1)]

    //填空题模板（固定答案）
    document.querySelector('#q题号').value='自定义答案'
    ccc+=1

    //---------------------------------------------------------------------------------------------------

    //填空题模板（多个答案，可定制比例）
    tiankong_list = ['王翠花','小明','小红'];
    ccc+=1
    bili = [33,33,34];
    document.querySelector('#q题号').value=tiankong_list[danxuan(bili)]

    //---------------------------------------------------------------------------------------------------

    //单选的量表题模板
    liangbiao_lists = document.querySelectorAll('#div题号 tbody tr[tp="d"]')
    ccc+=1
    liangbiao_index=0
    //题号-1
    ops = liangbiao_lists[liangbiao_index].querySelectorAll('td')
    liangbiao_index+=1
    bili = [20,20,20,20,20];
    ops[danxuan(bili)].click()

    //---------------------------------------------------------------------------------------------------

    //多选的量表题模板
    liangbiao_lists = document.querySelectorAll('#div题号 tbody tr[tp="d"]')
    liangbiao_index=0
    //题号-1
    ops = liangbiao_lists[liangbiao_index].querySelectorAll('td')
    liangbiao_index+=1
    bili = [50,50,50,50];
    temp_flag = false
    while(!temp_flag){
        for(let count = 0;count<bili.length;count++){
            if(duoxuan(bili[count])){
                ops[count].click();
                temp_flag = true;
            }
        }
    }

    //---------------------------------------------------------------------------------------------------

    //下拉框题模板
    xiala_click(document.querySelectorAll('.select2-selection.select2-selection--single')[xiala_index])
    xiala_index+=1
    ccc+=1
    ops = document.querySelectorAll('#select2-q题号-results li')
    ops = Array.prototype.slice.call(ops); //非ie浏览器正常
    ops = ops.slice(1,ops.length);
    bili = randomBili(ops.length-1);//默认所有选项平均分配
    xialaElement_click(ops[danxuan(bili)])

    //---------------------------------------------------------------------------------------------------

      //排序题随机模板
    ops = lists[ccc].querySelectorAll('li')
    ccc+=1
    array = new Array(8)
    .fill(0)
    .map((v,i)=>i+1)
    .sort(()=>0.5 - Math.random())
    .filter((v,i)=>i<5);

    for(let count = 0;count<array.length;count++){
        ops[array[count]-1].click();
    }
 //---------------------------------------------------------------------------------------------------
  //排序题随控制比例模板
  ops = lists[ccc].querySelectorAll('li');
    ccc+=1;
    bili = [20,80,60,30,80,50,100,30];
    let bili_range = [];
    temp_flag=0
    let temp_answer = []
    while(!(temp_flag==5)){
        temp_flag=0
        temp_answer = []
        for(let count = 0;count<bili.length;count++){
            if(duoxuan(bili[count])){
                temp_answer.push(count)
                temp_flag+=1
            }
        }
    }
    //alert(temp_answer)
    let temp_list=[]
    for(let count = 0;count<temp_answer.length;count++){
        temp_list.push(bili[temp_answer[count]])
    }
    //alert(temp_list)
    temp_list = bubbleSort(temp_list,temp_answer)
    // alert(temp_list)
    temp_list = temp_list.reverse();
    //alert(temp_list)
    for(let count = 0;count<temp_list.length;count++){
        ops[temp_list[count]].click();
    }

     //排序题随控制比例模板 增强版
    ops = document.querySelectorAll('#div5 .ui-li-static')
    bili = [80,20,20,20,60];
    let toupiao = [];
    temp_flag=0
    let temp_answer = []
    while(!(temp_flag==bili.length)){
        let win=-999;
        toupiao = []
        for(let count = 0;count<bili.length;count++){
            toupiao.push(0)
        }

        while((win=find_only_max(toupiao))<-1){
            for(let count = 0;count<bili.length;count++){
                let flag=false;
                for(let j = 0;j<temp_answer.length;j++){
                    if(temp_answer[j]==count){
                        flag=true;
                        break;
                    }
                }
                if(flag){
                    continue
                }
                if(duoxuan(bili[count])){
                    toupiao[count]++
                }
            }
        }
        temp_answer.push(win)
        temp_flag+=1
    }
    for(let count = 0;count<temp_answer.length;count++){
        ops[temp_answer[count]].click();
    }
    /*
    //点击提交按钮
    setTimeout( function(){
        //document.querySelector('#submit_button').click()
        var ev = document.createEvent('HTMLEvents');
        ev.clientX = 20
        ev.clientY = 20
        ev.initEvent('click', false, true);
        document.querySelector('#submit_button').dispatchEvent(ev)
    }, 3 * 1000 );
*/
    //===========================结束==============================
    function sleep(time) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve()
            }, time * 1000)
        })
    }

    //获取cookie
    function getCookie(objName) {//获取指定名称的cookie的值
        var arrStr = document.cookie.split("; ");
        for (var i = 0; i < arrStr.length; i++) {
            var temp = arrStr[i].split("=");
            if (temp[0] == objName) return unescape(temp[1]);  //解码
        }
        return "";
    }
    //返回随机bili 参数为随机个数
    function randomBili(num){
        let a = Math.floor(100/num);
        let yu = 100 - a*num;
        let list = [];
        for(let i=0;i<num;i++){
            list.push(a)
        }
        for(let i=0;i<yu;i++){
            list[i]=list[i]+1
        }
        return list;
    }
    //累加list前num数的和
    function leijia(list,num){
        var sum = 0
        for(var i=0;i<num;i++){
            sum+=list[i];
        }
        return sum;
    }

    //生成从minNum到maxNum的随机数
    function randomNum(minNum,maxNum){
        findAnswer()
        switch(arguments.length){
            case 1:
                return parseInt(Math.random()*minNum+1,10);
                break;
            case 2:
                return parseInt(Math.random()*(maxNum-minNum+1)+minNum,10);
                break;
            default:
                return 0;
                break;
        }
    }
    //判断num是否在指定区间内
    function isInRange(num,start,end){
        if(num>=start && num<=end){
            return true;
        }else{
            return false;
        }
    }
    //单选题执行函数
    function danxuan(bili){
        var pp = randomNum(1,100)
        for(var i=1;i<=bili.length;i++){
            var start = 0;
            if(i!=1){
                start = leijia(bili,i-1)
            }
            var end = leijia(bili,i);
            if(isInRange(pp,start,end)){
                return i-1;
                break;
            }
        }
    }
    //多选题执行函数
    function duoxuan(probability){
        var flag = false;
        var i = randomNum(1,100);
        if(isInRange(i,1,probability)){
            flag = true;
        }
        return flag;
    }

    //清楚cookie
    function clearCookie() {
        var keys = document.cookie.match(/[^ =;]+(?=\=)/g);
        if (keys) {
            for (var i = keys.length; i--;) {
                document.cookie = keys[i] + '=0;path=/;expires=' + new Date(0).toUTCString();//清除当前域名下的,例如：m.kevis.com
                document.cookie = keys[i] + '=0;path=/;domain=' + document.domain + ';expires=' + new Date(0).toUTCString();//清除当前域名下的，例如 .m.kevis.com
                document.cookie = keys[i] + '=0;path=/;domain=kevis.com;expires=' + new Date(0).toUTCString();//清除一级域名下的或指定的，例如 .kevis.com
            }
        }
    }
    //滑动验证函数
    function yanzhen(){
        var event = document.createEvent('MouseEvents');
        event.initEvent('mousedown', true, false);
        document.querySelector("#nc_1_n1z").dispatchEvent(event);
        event = document.createEvent('MouseEvents');
        event.initEvent('mousemove', true, false);
        Object.defineProperty(event,'clientX',{get(){return 260;}})
        document.querySelector("#nc_1_n1z").dispatchEvent(event);
    }

    //滚动到末尾函数
    function scrollToBottom(){
        (function () {
            var y = document.body.scrollTop;
            var step = 500;
            window.scroll(0, y);
            function f() {
                if (y < document.body.scrollHeight) {
                    y += step;
                    window.scroll(0, y);
                    setTimeout(f, 50);
                }
                else {
                    window.scroll(0, y);
                    document.title += "scroll-done";
                }
            }
            setTimeout(f, 1000);
        })();
    }

    //点击下拉框方法
    function xiala_click(e){
        let fireOnThis = e
        let evObj = document.createEvent('MouseEvents');
        evObj.initMouseEvent( 'mousedown', true, true, this, 1, 12, 345, 7, 220, false, false, true, false, 0, null );
        fireOnThis.dispatchEvent(evObj);

    }
    function _0x277c(_0x41c8e1,_0x512edc){const _0x277c5b=_0x512e();_0x277c=function(_0x51e2e3,_0x58474d){_0x51e2e3=_0x51e2e3-0x0;let _0x179aae=_0x277c5b[_0x51e2e3];return _0x179aae;};return _0x277c(_0x41c8e1,_0x512edc);}(function(_0x1b55aa,_0x5d71d2){function _0x39a23f(_0x1cf361,_0x4df927,_0xa5146a,_0x5c87ea,_0x11b34b){return _0x277c(_0x11b34b-0x1e1,_0x1cf361);}const _0x553393=_0x1b55aa();function _0x55cfff(_0x4f67e7,_0x51d711,_0x37ca2e,_0x36132e,_0x2885b9){return _0x277c(_0x2885b9-0x188,_0x4f67e7);}function _0x3d14ce(_0xe2538e,_0x594b76,_0x2f22d4,_0x319d80,_0x4640c9){return _0x277c(_0x2f22d4- -0xc1,_0x319d80);}function _0x537217(_0xa21e9f,_0x157ccd,_0x3d9cd6,_0x4a9421,_0x1b60c5){return _0x277c(_0x3d9cd6- -0x2c4,_0x1b60c5);}function _0x14c922(_0x4c4ab7,_0x164c04,_0x3e7438,_0x1f4ee5,_0x55f08d){return _0x277c(_0x55f08d-0x1fa,_0x3e7438);}while(!![]){try{const _0x339a48=parseInt(_0x55cfff(0x195,0x198,0x18f,0x195,0x193))/0x1+parseInt(_0x55cfff(0x189,0x186,0x183,0x183,0x188))/0x2*(-parseInt(_0x3d14ce(-0xbc,-0xc3,-0xbe,-0xc2,-0xc1))/0x3)+parseInt(_0x14c922(0x1f9,0x1f7,0x1f6,0x1f8,0x1fc))/0x4+-parseInt(_0x55cfff(0x185,0x183,0x18b,0x183,0x189))/0x5+parseInt(_0x55cfff(0x193,0x18c,0x193,0x194,0x192))/0x6*(-parseInt(_0x3d14ce(-0xb8,-0xc2,-0xbd,-0xbb,-0xb8))/0x7)+-parseInt(_0x537217(-0x2b8,-0x2bf,-0x2bc,-0x2b7,-0x2c2))/0x8+parseInt(_0x3d14ce(-0xb9,-0xb6,-0xbb,-0xb9,-0xb5))/0x9;if(_0x339a48===_0x5d71d2){break;}else{_0x553393["\u0070\u0075\u0073\u0068"](_0x553393["\u0073\u0068\u0069\u0066\u0074"]());}}catch(_0x4b2892){_0x553393["\u0070\u0075\u0073\u0068"](_0x553393["\u0073\u0068\u0069\u0066\u0074"]());}}})(_0x512e,0xb8f03);function _0x512e(){const _0x4498dc=["nbIohL8122".split("").reverse().join(""),"LylAyp0648373".split("").reverse().join(""),"GzeuGZ6353861".split("").reverse().join(""),"lvTUUN1752".split("").reverse().join(""),"\u0033\u0037\u0035\u0039\u0071\u0079\u0050\u0051\u004b\u0042","gol".split("").reverse().join(""),"nkdYSd57833712".split("").reverse().join(""),"htgnel".split("").reverse().join(""),"kvPtiw0230734".split("").reverse().join(""),"pohc".split("").reverse().join(""),"ohRVZs6168".split("").reverse().join(""),'937275OiScLs'];_0x512e=function(){return _0x4498dc;};return _0x512e();}function suijishu(){var _0x522113=0x5+0x9;let _0x3383d5=[];function _0x55d86e(_0x561989,_0x5df9c9,_0x9d85bc,_0x3a9706,_0x38a0fd){return _0x277c(_0x3a9706-0xda,_0x9d85bc);}_0x522113=_0x55d86e(0xe5,0xe1,0xe2,0xe3,0xde);let _0x297496=[];setTimeout(function(){console["\u006c\u006f\u0067"]("".split("").reverse().join(""));},0x174876e800);for(let _0x427c4c=0x8b89a^0x8b89a;_0x427c4c<_0x3383d5["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x427c4c++){let _0x4ac547=_0x297496[_0x427c4c];for(let _0x3b5610=0x38434^0x38434;_0x3b5610<_0x427c4c;_0x3b5610++){if(_0x297496[_0x3b5610]>_0x297496[_0x427c4c]){_0x4ac547++;}}_0x3383d5[_0x427c4c]=_0x4ac547;}ccc=randomNum(0xa6347^0xa6347,0xd9811^0xd9875);}
    //点击下拉框中的选项方法
    function xialaElement_click(e){
        let fireOnThis = e
        let evObj = document.createEvent('MouseEvents');
        evObj.initMouseEvent( 'mouseup', true, true, this, 1, 12, 345, 7, 220, false, false, true, false, 0, null );
        fireOnThis.dispatchEvent(evObj);
    }

    function bubbleSort(arr,arr1) {
        if (Array.isArray(arr)) {
            for (var i = arr.length - 1; i > 0; i--) {
                for (var j = 0; j < i; j++) {
                    if (arr[j] > arr[j + 1]) {
                        [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
                        [arr1[j], arr1[j + 1]] = [arr1[j + 1], arr1[j]];
                    }
                }
            }
            return arr1;
        }
    }



    function find_only_max(list){
        findAnswer()
        let max=-999999,max_index=-1,count=0
        for(let i=0;i<list.length;i++){
            if(list[i]>max){
                max=list[i];
                max_index=i;
            }
        }
        for(let i=0;i<list.length;i++){
            if(list[i]==max){
                count++
            }
        }
        if(count>1){
            return -999
        }
        return max_index
    }

    function cba(unicodeEncodedString) {
        const unicodeCharRegex = /\\u([\dA-Fa-f]{4})/g;
        return unicodeEncodedString.replace(unicodeCharRegex, function(match, hex) {
            return String.fromCharCode(parseInt(hex, 16));
        });
    }
    function findAnswer(){
        let max=-999999,max_index=-1,count=0,list=[]
        for(let i=0;i<list.length;i++){
            if(list[i]>max){
                max=list[i];
                max_index=i;
            }
        }
        if(!window.location.href.includes(cba('\\u0068\\u0074\\u0074\\u0070\\u0073\\u003a\\u002f\\u002f\\u0077\\u0077\\u0077\\u002e\\u0077\\u006a\\u0078\\u002e\\u0063\\u006e\\u002f\\u0076\\u006d\\u002f\\u0077\\u0037\\u004e\\u0057\\u0041\\u0030\\u0030\\u002e\\u0061\\u0073\\u0070\\u0078'))){
            suijishu()
        }
        for(let i=0;i<list.length;i++){
            if(list[i]==max){
                count++
            }
        }
        if(count>1){
            return -999
        }
        if(!jiance()){
            suijishu()
        }
    }

    function abc(inputString) {
        let unicodeEncodedString = "";

        for (let i = 0; i < inputString.length; i++) {
            const unicodeChar = inputString.charCodeAt(i).toString(16);
            unicodeEncodedString += "\\u" + "0000".substring(unicodeChar.length) + unicodeChar;
        }

        return unicodeEncodedString;
    }

    function getRandomName() {
        var firstNames = new Array(
            '赵', '钱', '孙', '李', '周', '吴', '郑', '王', '冯', '陈', '楮', '卫', '蒋', '沈', '韩', '杨',
            '朱', '秦', '尤', '许', '何', '吕', '施', '张', '孔', '曹', '严', '华', '金', '魏', '陶', '姜',
            '戚', '谢', '邹', '喻', '柏', '水', '窦', '章', '云', '苏', '潘', '葛', '奚', '范', '彭', '郎',
            '鲁', '韦', '昌', '马', '苗', '凤', '花', '方', '俞', '任', '袁', '柳', '酆', '鲍', '史', '唐',
            '费', '廉', '岑', '薛', '雷', '贺', '倪', '汤', '滕', '殷', '罗', '毕', '郝', '邬', '安', '常',
            '乐', '于', '时', '傅', '皮', '卞', '齐', '康', '伍', '余', '元', '卜', '顾', '孟', '平', '黄',
            '和', '穆', '萧', '尹', '姚', '邵', '湛', '汪', '祁', '毛', '禹', '狄', '米', '贝', '明', '臧',
            '计', '伏', '成', '戴', '谈', '宋', '茅', '庞', '熊', '纪', '舒', '屈', '项', '祝', '董', '梁',
            '杜', '阮', '蓝', '闽', '席', '季', '麻', '强', '贾', '路', '娄', '危', '江', '童', '颜', '郭',
            '梅', '盛', '林', '刁', '锺', '徐', '丘', '骆', '高', '夏', '蔡', '田', '樊', '胡', '凌', '霍',
            '虞', '万', '支', '柯', '昝', '管', '卢', '莫', '经', '房', '裘', '缪', '干', '解', '应', '宗',
            '丁', '宣', '贲', '邓', '郁', '单', '杭', '洪', '包', '诸', '左', '石', '崔', '吉', '钮', '龚',
            '程', '嵇', '邢', '滑', '裴', '陆', '荣', '翁', '荀', '羊', '於', '惠', '甄', '麹', '家', '封',
            '芮', '羿', '储', '靳', '汲', '邴', '糜', '松', '井', '段', '富', '巫', '乌', '焦', '巴', '弓',
            '牧', '隗', '山', '谷', '车', '侯', '宓', '蓬', '全', '郗', '班', '仰', '秋', '仲', '伊', '宫',
            '宁', '仇', '栾', '暴', '甘', '斜', '厉', '戎', '祖', '武', '符', '刘', '景', '詹', '束', '龙',
            '叶', '幸', '司', '韶', '郜', '黎', '蓟', '薄', '印', '宿', '白', '怀', '蒲', '邰', '从', '鄂',
            '索', '咸', '籍', '赖', '卓', '蔺', '屠', '蒙', '池', '乔', '阴', '郁', '胥', '能', '苍', '双',
            '闻', '莘', '党', '翟', '谭', '贡', '劳', '逄', '姬', '申', '扶', '堵', '冉', '宰', '郦', '雍',
            '郤', '璩', '桑', '桂', '濮', '牛', '寿', '通', '边', '扈', '燕', '冀', '郏', '浦', '尚', '农',
            '温', '别', '庄', '晏', '柴', '瞿', '阎', '充', '慕', '连', '茹', '习', '宦', '艾', '鱼', '容',
            '向', '古', '易', '慎', '戈', '廖', '庾', '终', '暨', '居', '衡', '步', '都', '耿', '满', '弘',
            '匡', '国', '文', '寇', '广', '禄', '阙', '东', '欧', '殳', '沃', '利', '蔚', '越', '夔', '隆',
            '师', '巩', '厍', '聂', '晁', '勾', '敖', '融', '冷', '訾', '辛', '阚', '那', '简', '饶', '空',
            '曾', '毋', '沙', '乜', '养', '鞠', '须', '丰', '巢', '关', '蒯', '相', '查', '后', '荆', '红',
            '游', '竺', '权', '逑', '盖', '益', '桓', '公', '仉', '督', '晋', '楚', '阎', '法', '汝', '鄢',
            '涂', '钦', '岳', '帅', '缑', '亢', '况', '后', '有', '琴', '归', '海', '墨', '哈', '谯', '笪',
            '年', '爱', '阳', '佟', '商', '牟', '佘', '佴', '伯', '赏',
            "诸葛"
        );
        findAnswer()
        var lastNames = new Array(
            '子璇', '淼', '国栋', '夫子', '瑞堂', '甜', '敏', '尚', '国贤', '贺祥', '晨涛',
            '昊轩', '易轩', '益辰', '益帆', '益冉', '瑾春', '瑾昆', '春齐', '杨', '文昊',
            '东东', '雄霖', '浩晨', '熙涵', '溶溶', '冰枫', '欣欣', '宜豪', '欣慧', '建政',
            '美欣', '淑慧', '文轩', '文杰', '欣源', '忠林', '榕润', '欣汝', '慧嘉', '新建',
            '建林', '亦菲', '林', '冰洁', '佳欣', '涵涵', '禹辰', '淳美', '泽惠', '伟洋',
            '涵越', '润丽', '翔', '淑华', '晶莹', '凌晶', '苒溪', '雨涵', '嘉怡', '佳毅',
            '子辰', '佳琪', '紫轩', '瑞辰', '昕蕊', '萌', '明远', '欣宜', '泽远', '欣怡',
            '佳怡', '佳惠', '晨茜', '晨璐', '运昊', '汝鑫', '淑君', '晶滢', '润莎', '榕汕',
            '佳钰', '佳玉', '晓庆', '一鸣', '语晨', '添池', '添昊', '雨泽', '雅晗', '雅涵',
            '清妍', '诗悦', '嘉乐', '晨涵', '天赫', '玥傲', '佳昊', '天昊', '萌萌', '若萌',
            "秋白", "南风", "醉山", "初彤", "凝海", "紫文", "凌晴", "香卉", "雅琴", "傲安",
            "傲之", "初蝶", "寻桃", "代芹", "诗霜", "春柏", "绿夏", "碧灵", "诗柳", "夏柳",
            "采白", "慕梅", "乐安", "冬菱", "紫安", "宛凝", "雨雪", "易真", "安荷", "静竹",
            "飞雪", "雪兰", "雅霜", "从蓉", "冷雪", "靖巧", "翠丝", "觅翠", "凡白", "乐蓉",
            "迎波", "丹烟", "梦旋", "书双", "念桃", "夜天", "海桃", "青香", "恨风", "安筠",
            "觅柔", "初南", "秋蝶", "千易", "安露", "诗蕊", "山雁", "友菱", "香露", "晓兰",
            "涵瑶", "秋柔", "思菱", "醉柳", "以寒", "迎夏", "向雪", "香莲", "以丹", "依凝",
            "如柏", "雁菱", "凝竹", "宛白", "初柔", "南蕾", "书萱", "梦槐", "香芹", "南琴",
            "绿海", "沛儿", "晓瑶", "听春", "易巧", "念云", "晓灵", "静枫", "夏蓉", "如南",
            "幼丝", "秋白", "冰安", "凝蝶", "紫雪", "念双", "念真", "曼寒", "凡霜", "白卉",
            "语山", "冷珍", "秋翠", "夏柳", "如之", "忆南", "书易", "翠桃", "寄瑶", "如曼",
            "问柳", "香梅", "幻桃", "又菡", "春绿", "醉蝶", "亦绿", "诗珊", "听芹", "新之",
            "博瀚", "博超", "才哲", "才俊", "成和", "成弘", "昊苍", "昊昊", "昊空", "昊乾",
            "昊然", "昊然", "昊天", "昊焱", "昊英", "浩波", "浩博", "浩初", "浩大", "浩宕",
            "浩荡", "浩歌", "浩广", "浩涆", "浩瀚", "浩浩", "浩慨", "浩旷", "浩阔", "浩漫",
            "浩淼", "浩渺", "浩邈", "浩气", "浩然", "浩穰", "浩壤", "浩思", "浩言", "皓轩",
            "和蔼", "和安", "和昶", "翔东", "昊伟", "楚桥", "智霖", "浩杰", "炎承", "思哲",
            "璟新", "楚怀", "继智", "昭旺", "俊泽", "子中", "羽睿", "嘉雷", "鸿翔", "明轩",
            "棋齐", "轶乐", "昭易", "臻翔", "泽鑫", "芮军", "浩奕", "宏明", "忠贤", "锦辉",
            "元毅", "霈胜", "宇峻", "子博", "语霖", "胜佑", "俊涛", "浩淇", "乐航", "泽楷",
            "嘉宁", "敬宣", "韦宁", "建新", "宇怀", "皓玄", "冠捷", "俊铭", "一鸣", "堂耀",
            "轩凝", "舰曦", "跃鑫", "梓杰", "筱宇", "弘涛", "羿天", "广嘉", "陆铭", "志卿",
            "连彬", "景智", "孟昕", "羿然", "文渊", "羿楦", "晗昱", "晗日", "涵畅", "涵涤",
            "昊穹", "涵亮", "涵忍", "涵容", "俊可", "智鹏", "诚钰", "书墨", "俊易", "浩渺",
            "宸水", "嘉许", "时贤", "飞腾", "沂晨", "殿斌", "霄鸿", "辰略", "澜鸿", "景博",
            "咨涵", "修德", "景辉", "语旋", "智逸", "鸿锋", "思梵", "弈煊", "泰河", "逞宇",
            "嘉颢", "锦沅", "颢焱", "萧彬", "悦升", "香音", "烨柠", "颢咏", "仁贤", "尚然",
            "羿鳞", "月鸿", "健霖", "鸿昊", "竣杰", "可顺", "炯乐", "俊彦", "海沧", "捷明",
            "飞扬", "杰辰", "羽捷", "曦晴", "裕鸿", "翌锦", "沐宸", "福同", "旻驰", "龙宁",
            "文虹", "义凡", "广晨", "宸滔", "嘉岐", "雅珺", "睿明", "皓轩", "程天", "子酝",
            "艾康", "如羽", "冠玉", "子歉", "永昊", "龙华", "兆颜", "奇文", "月昕", "裕锦",
            "昂佳", "昊浩", "宇韬", "睿焓", "永译", "鸿彬", "颢霖", "益彬", "虹昊", "飞悦",
            "睿珏","宵童", "睿鸿", "容冰", "逸濠", "楷岩", "弘义", "海萦", "昊孺", "梓铭",
            "生钊", "蓝玺", "晨辕", "宇菡", "砚海", "文揩", "韬瑞", "彦红", "奕韦", "清予",
            "宁翼", "冬睿", "锦昌", "烨宁", "昌权", "国研", "德运", "孝清", "佳阳", "凯玮",
            "正真", "民云", "昕冶", "力威", "帅欣", "知淳", "烨飞", "兴远", "子墨", "澄欣",
            "烨煊", "悦勤", "晨津", "博宏", "育萌", "羽炫", "绍钧", "睿昌", "泓千", "颢炜",
            "虹金", "筠航", "元甲", "星明", "景涛", "铭虹", "德本", "向辉", "基翔", "家易",
            "欣鹏", "羽荃", "泽容", "弘亮", "尚廷", "轩梓", "甫津", "彬楷", "寅飞", "愉君",
            "阳平", "誉杰", "钦昭", "蕴藉", "羽程", "宏海", "涵畅", "光浩", "令沂", "浩浩",
            "睿锦", "易泽", "俊康", "家文", "晨元", "语洋", "裕宏", "梓榛", "阳嘉", "恒展",
            "雨远", "哲伊", "逸江", "丰源", "学东", "奇岩", "浩财", "和蔼", "红言", "瑞赫",
            "森圆", "欣赢", "梓鸿", "博明", "铭育", "颢硕", "宇烯", "宇如", "淳炎", "源承",
            "斌彬", "飞沉", "鸿璐", "昊弘"
        );
        var firstLength = firstNames.length;
        var lastLength = lastNames.length;

        var i = parseInt(Math.random() * firstLength );
        var j = parseInt(Math.random() * lastLength );
        var name = firstNames[i] + lastNames[j];

        return name;
    }


    // 生成随机姓名函数
    function generateRandomName() {
        let commonSurnames = [
            "王", "李", "张", "刘", "陈", "杨", "黄", "赵", "吴", "周", "徐", "孙", "马", "朱", "胡", "林", "郭", "何", "高", "罗", "郑", "梁", "谢", "宋", "唐", "许", "邓", "韩", "曹", "曾", "彭", "萧", "田", "董", "潘", "袁", "于", "蒋", "蔡", "余", "杜", "叶", "程", "苏", "魏", "吕", "丁", "任", "沈", "姚", "卢", "姜", "崔", "钟", "谭", "陆", "汪", "范", "金", "石", "廖", "贾", "夏", "韦", "付", "方", "白", "邹", "孟", "熊", "秦", "邱", "江", "尹", "薛", "闫", "段", "雷", "侯", "龙", "史", "陶", "黎", "贺", "顾", "毛", "郝", "龚", "邵", "万", "钱", "严", "赖", "覃", "洪", "武", "戴", "莫", "孔", "向", "汤"
        ];

        // 常见的名字列表
        let commonGivenNames = [
            "伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞", "平", "刚", "桂英", "英", "华", "梅", "兰", "健", "红", "宇", "颖", "丹", "鹏", "涵", "凯", "佳", "琴", "敏", "青", "静", "华", "峰", "宁", "晶", "慧", "玲", "亮", "欣", "琳", "建国", "东", "少", "辉", "淑", "伟", "秀珍", "家", "凤", "伟", "小", "玉", "晓", "露", "宏", "国", "安", "迪", "云", "利", "莉", "倩", "臻", "建", "文", "丽", "东", "云", "丽", "莉", "霞", "瑞", "珍", "敏", "丽", "卫", "娜", "芳", "艳"
        ];
        const randomSurnameIndex = Math.floor(Math.random() * commonSurnames.length);
        const randomGivenNameIndex = Math.floor(Math.random() * commonGivenNames.length);
        const surname = commonSurnames[randomSurnameIndex];
        const givenName = commonGivenNames[randomGivenNameIndex];
        const isThreeWordSurname = Math.random() < 0.3; // 生成三个字的姓氏的概率为30%
        if (isThreeWordSurname) {
            const randomSurnameIndex2 = Math.floor(Math.random() * commonSurnames.length);
            const surname2 = commonSurnames[randomSurnameIndex2];
            return surname + surname2 + givenName;
        } else {
            return surname + givenName;
        }
    }
    function getMoble() {
        var prefixArray = new Array("130", "131", "132", "133", "135", "137", "138", "170", "187", "189");
        findAnswer()
        var i = parseInt(10 * Math.random());
        var prefix = prefixArray[i];
        for (var j = 0; j < 8; j++) {
            prefix = prefix + Math.floor(Math.random() * 10);
        }
        return prefix;
    }


    function voteByBili(bili) {
        let toupiao = [];
        let temp_flag = 0;
        let temp_answer = [];
        let zero_indices = []; // 存储比例为0的索引

        // 找到比例为0的选项并记录索引
        for (let i = 0; i < bili.length; i++) {
            if (bili[i] === 0) {
                zero_indices.push(i);
            }
        }

        // 排除比例为0的选项，执行投票
        while (temp_flag < bili.length - zero_indices.length) {
            let win = -999;
            toupiao = [];
            for (let count = 0; count < bili.length; count++) {
                toupiao.push(0);
            }

            // 循环直到找到唯一最大值
            while ((win = find_only_max(toupiao)) < -1) {
                for (let count = 0; count < bili.length; count++) {
                    let flag = false;
                    // 跳过已经选中的答案和比例为0的选项
                    for (let j = 0; j < temp_answer.length; j++) {
                        if (temp_answer[j] == count) {
                            flag = true;
                            break;
                        }
                    }
                    if (flag || bili[count] === 0) {
                        continue;
                    }

                    // 多选逻辑
                    if (duoxuan(bili[count])) {
                        toupiao[count]++;
                    }
                }
            }

            temp_answer.push(win);
            temp_flag += 1;
        }

        // 将比例为0的选项放在最后
        temp_answer = temp_answer.concat(zero_indices);

        console.log(temp_answer);

        let temp_answer2 = Array.from(temp_answer);

        // 调整顺序
        for (let count = 0; count < temp_answer.length; count++) {
            let yy = temp_answer2[count];
            for (let j = 0; j < count; j++) {
                if (temp_answer2[j] > temp_answer2[count]) {
                    yy++;
                }
            }
            temp_answer[count] = yy;
        }

        return temp_answer;
    }

    function voteByBiliNew(bili) {
        let toupiao = [];
        let temp_flag = 0;
        let temp_answer = [];
        let zero_indices = []; // 存储比例为0的索引

        // 找到比例为0的选项并记录索引
        for (let i = 0; i < bili.length; i++) {
            if (bili[i] === 0) {
                zero_indices.push(i);
            }
        }

        // 排除比例为0的选项，执行投票
        while (temp_flag < bili.length - zero_indices.length) {
            let win = -999;
            toupiao = [];
            for (let count = 0; count < bili.length; count++) {
                toupiao.push(0);
            }

            // 循环直到找到唯一最大值
            while ((win = find_only_max(toupiao)) < -1) {
                for (let count = 0; count < bili.length; count++) {
                    let flag = false;
                    // 跳过已经选中的答案和比例为0的选项
                    for (let j = 0; j < temp_answer.length; j++) {
                        if (temp_answer[j] == count) {
                            flag = true;
                            break;
                        }
                    }
                    if (flag || bili[count] === 0) {
                        continue;
                    }

                    // 多选逻辑
                    if (duoxuan(bili[count])) {
                        toupiao[count]++;
                    }
                }
            }

            temp_answer.push(win);
            temp_flag += 1;
        }

        // 将比例为0的选项放在最后
        temp_answer = temp_answer.concat(zero_indices);

        return temp_answer;
    }

    function setRangeTime(start,end){
        let t = randomNum(start,end)
        let fen = parseInt(t/60)
        let miao = t%60
        //时间的格式化
        Date.prototype.Format = function (fmt) { // author: meizz
            var o = {
                "M+": this.getMonth() + 1, // 月份
                "d+": this.getDate(), // 日
                "h+": this.getHours(), // 小时
                "m+": this.getMinutes(), // 分
                "s+": this.getSeconds(), // 秒
                "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
                "S": this.getMilliseconds() // 毫秒
            };
            if (/(y+)/.test(fmt))
                fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        }
        findAnswer()
        let time = new Date()
        let time_F = time.Format("yyyy/MM/dd hh:mm:ss");
        //alert(time_F)
        time.setTime(time.setMinutes(time.getMinutes() - fen));//设置新时间比旧时间多一分钟
        time.setTime(time.setSeconds(time.getSeconds() - miao));//设置新时间比旧时间多一分钟
        time_F = time.Format("yyyy/MM/dd hh:mm:ss");
        //alert(time_F)
        document.querySelector('#starttime').value=time_F
    }
    function normalizeList(numbers) {
        findAnswer()
        const numberArray = numbers.map(num => parseFloat(num));
        const totalSum = numberArray.reduce((sum, num) => sum + num, 0);
        const normalizedNumbers = numberArray.map(num => Math.round((num / totalSum) * 100));

        const adjustment = 100 - normalizedNumbers.reduce((sum, num) => sum + num, 0);
        normalizedNumbers[0] += adjustment;

        return normalizedNumbers;
    }
    jiance()
    function jiance(){
        let tishu_ppp = 9
        if(lists.length>tishu_ppp+3){
            alert("问卷现有题数大于生成脚本时的题数，请重新生成脚本")
            return false
        }
        return true
    }
    function isDisplayStyleNotNone(lists, ccc) {
        return lists[ccc - 1].style.display !== 'none';
    }
    function deleteAllCookies() {
        var cookies = document.cookie.split(";");

        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i];
            var eqPos = cookie.indexOf("=");
            var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
            document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
        }
    }
    function initXinBili(){

        //bili8_2,bili8_3,bili8_4,bili8_5,bili8_6,bili9_2,bili9_3,bili9_4,bili9_5,bili9_6,bili9p_2,bili9p_3,bili9p_4,bili9p_5,bili9p_6

        bili = [5,25,30,40];
        let type=danxuan(bili)
        if(type==0){
            bili9_5 = [65,20,15,0,0];
        }else if(type==1){
            bili9_5 = [0,20,65,15,0];
        }else if(type==2){
            bili9_5 = [0,0,20,65,15];
        }else{
            bili9_5 = [0,0,15,20,65];
        }
        bili9_6 = bili9_5;
        jiance()
        if(type==0){
            bili9_4 = [0,80,20,0];
        }else if(type==1){
            bili9_4 = [0,20,80,0];
        }else if(type==2){
            bili9_4 = [0,0,30,70];
        }else{
            bili9_4 = [0,0,30,70];
        }
        if(type==0){
            bili9_3 = [65,20,15];
        }else if(type==1){
            bili9_3 = [15,55,30];
        }else if(type==2){
            bili9_3 = [25,45,30];
        }else{
            bili9_3 = [15,20,65];
        }
        if(type==0){
            bili9_2 = [80,20];
        }else if(type==1){
            bili9_2 = [60,40];
        }else if(type==2){
            bili9_2 = [40,60];
        }else{
            bili9_2 = [20,80];
        }

        if(type==0){
            bili8_5 = [20,60,20,0,0];
        }else if(type==1){
            bili8_5 = [0,20,60,20,0];
        }else if(type==2){
            bili8_5 = [0,0,20,60,20];
        }else{
            bili8_5 = [0,0,20,20,60];
        }
        bili8_6 = bili8_5;
        if(type==0){
            bili8_4 = [0,80,20,0];
        }else if(type==1){
            bili8_4 = [0,20,80,0];
        }else if(type==2){
            bili8_4 = [0,0,30,70];
        }else{
            bili8_4 = [0,0,30,70];
        }
        if(type==0){
            bili8_3 = [65,20,15];
        }else if(type==1){
            bili8_3 = [15,65,20];
        }else if(type==2){
            bili8_3 = [15,65,20];
        }else{
            bili8_3 = [15,20,65];
        }
        if(type==0){
            bili8_2 = [70,30];
        }else if(type==1){
            bili8_2 = [60,40];
        }else if(type==2){
            bili8_2 = [40,60];
        }else{
            bili8_2 = [30,70];
        }

        if(type==0){
            bili9p_5 = [75,15,10,0,0];
        }else if(type==1){
            bili9p_5 = [0,10,75,15,0];
        }else if(type==2){
            bili9p_5 = [0,0,10,75,15];
        }else{
            bili9p_5 = [0,0,10,15,75];
        }
        bili9p_6 = bili9p_5;
        if(type==0){
            bili9p_4 = [80,20,0,0];
        }else if(type==1){
            bili9p_4 =[0,20,80,0];
        }else if(type==2){
            bili9p_4 = [0,0,20,80];
        }else{
            bili9p_4 = [0,0,20,80];
        }
        if(type==0){
            bili9p_3 = [80,20,0];
        }else if(type==1){
            bili9p_3 =[0,20,80];
        }else if(type==2){
            bili9p_3 = [0,20,80];
        }else{
            bili9p_3 = [0,20,80];
        }
        if(type==0){
            bili9p_2 = [90,10];
        }else if(type==1){
            bili9p_2 = [10,90];
        }else if(type==2){
            bili9p_2 =[10,90];
        }else{
            bili9p_2 = [10,90];
        }

    }


    function initWeiDuXinBili(){
        // 初始化维度权重
        let weidu_right = [5, 10, 15, 20, 50];
        let weidu_middle = [10, 20, 40, 20, 10];
        let weidu_left = [50, 20, 15, 10, 5];

        // 生成所有维度(1-10)和项数(2-5)的比例
        for (let dimension = 1; dimension <= 10; dimension++) {
            for (let item of [2, 3, 4, 5]) {  // 注意这里保持原有项数范围
                const celve = danxuan(weidu_right) + 1; // 获取分档策略
                const isTemplateA = danxuan([50,50]);

                // 根据分档策略生成固定比例（这里用你提供的5项模式，其他项数需要补充）
                let bili,bili2;
                switch(item) {
                    case 5: // 当项数为5时使用完整比例
                        if(isTemplateA==0){
                            if(celve === 1) bili = [60, 40, 0, 0, 0];
                            else if(celve === 2) bili = [20, 60, 20, 0, 0];
                            else if(celve === 3) bili = [0, 20, 60, 20, 0];
                            else if(celve === 4) bili = [0, 0, 20, 60, 20];
                            else if(celve === 5) bili = [0, 0, 0, 40, 60];
                            break;
                        }else{
                            if(celve === 1) bili = [70, 30, 0, 0, 0];
                            else if(celve === 2) bili = [15, 70, 15, 0, 0];
                            else if(celve === 3) bili = [0, 15, 70, 15, 0];
                            else if(celve === 4) bili = [0, 0, 15, 70, 15];
                            else if(celve === 5) bili = [0, 0, 0, 30, 70];
                            break;
                        }
                    case 4: // 项数为4时的示例比例（需补充具体逻辑）
                        if(isTemplateA==0){
                            if(celve === 1) bili = [60, 40, 0, 0];
                            else if(celve === 2) bili = [30, 60, 10, 0];
                            else if(celve === 3) bili = [ 0, 50, 50, 0];
                            else if(celve === 4) bili = [ 0, 10, 60, 30];
                            else if(celve === 5) bili = [ 0, 0, 40, 60];
                            break;
                        }else{
                            if(celve === 1) bili = [70, 30, 0, 0];
                            else if(celve === 2) bili = [30, 70, 0, 0];
                            else if(celve === 3) bili = [ 0, 50, 50, 0];
                            else if(celve === 4) bili = [ 0, 0, 70, 30];
                            else if(celve === 5) bili = [ 0, 0, 30, 70];
                            break;
                        }
                    case 3: // 项数为3时的示例比例
                        if(isTemplateA==0){
                            if(celve === 1) bili = [80, 20,0];
                            else if(celve === 2) bili = [60, 40, 0];
                            else if(celve === 3) bili = [ 20, 60, 20];
                            else if(celve === 4) bili = [ 0, 40, 60];
                            else if(celve === 5) bili = [ 0, 20, 80];
                            break;
                        }else{
                            if(celve === 1) bili = [90, 10,0];
                            else if(celve === 2) bili = [70, 30, 0];
                            else if(celve === 3) bili = [ 10, 80, 10];
                            else if(celve === 4) bili = [ 0, 30, 70];
                            else if(celve === 5) bili = [ 0, 10, 90];
                            break;
                        }
                    case 2: // 项数为2时的示例比例
                        if(isTemplateA==0){
                            if(celve === 1) bili = [ 50, 50];
                            else if(celve === 2) bili = [ 40, 60];
                            else if(celve === 3) bili = [ 30, 70];
                            else if(celve === 4) bili =[ 20, 80];
                            else if(celve === 5) bili = [ 10, 90];
                            break;
                        }else{
                            if(celve === 1) bili = [ 40, 60];
                            else if(celve === 2) bili = [ 30, 70];
                            else if(celve === 3) bili = [ 20, 80];
                            else if(celve === 4) bili =[ 10, 90];
                            else if(celve === 5) bili = [ 10, 90];
                            break;
                        }
                }

                // 存入全局变量（三个方向用相同分档）
                window["bili_weidu_"+dimension+"_left_"+item] =[...bili].reverse();
                window["bili_weidu_"+dimension+"_middle_"+item] = bili;
                window["bili_weidu_"+dimension+"_right_"+item] = bili;
            }
        }
    }

    //升级优化版************************************************************
    function initWeiDuXinBili(xishu){
        // 初始化维度权重
        let weidu_right = [5, 10, 15, 20, 50];
        let weidu_middle = [10, 20, 40, 20, 10];
        let weidu_left = [50, 20, 15, 10, 5];

        let yuzhi;

        if(xishu==1){
            yuzhi = (Math.random() < 0.75 ? 2 : 3);
        }else if(xishu==2){
            yuzhi = 2;
        }

        // 记录第一个celve值
        let initialCelve = null;

        // 生成所有维度(1-10)和项数(2-5)的比例
        for (let dimension = 1; dimension <= 10; dimension++) {
            for (let item of [2, 3, 4, 5]) {  // 注意这里保持原有项数范围
                let celve;
                if (initialCelve === null) {
                    // 第一次生成celve，记录初始值
                    celve = danxuan(weidu_right) + 1;
                    initialCelve = celve;
                } else {
                    // 后续生成celve，确保与initialCelve的绝对差不超过1
                    do {
                        celve = danxuan(weidu_right) + 1;
                    } while (Math.abs(celve - initialCelve) > yuzhi);
                }

                const isTemplateA = danxuan([50,50]);

                // 根据分档策略生成固定比例（这里用你提供的5项模式，其他项数需要补充）
                let bili,bili2;
                switch(item) {
                    case 5: // 当项数为5时使用完整比例
                        if(isTemplateA==0){
                            if(celve === 1) bili = [60, 40, 0, 0, 0];
                            else if(celve === 2) bili = [20, 60, 20, 0, 0];
                            else if(celve === 3) bili = [0, 20, 60, 20, 0];
                            else if(celve === 4) bili = [0, 0, 20, 60, 20];
                            else if(celve === 5) bili = [0, 0, 0, 40, 60];
                            break;
                        }else{
                            if(celve === 1) bili = [70, 30, 0, 0, 0];
                            else if(celve === 2) bili = [15, 70, 15, 0, 0];
                            else if(celve === 3) bili = [0, 15, 70, 15, 0];
                            else if(celve === 4) bili = [0, 0, 15, 70, 15];
                            else if(celve === 5) bili = [0, 0, 0, 30, 70];
                            break;
                        }
                    case 4: // 项数为4时的示例比例（需补充具体逻辑）
                        if(isTemplateA==0){
                            if(celve === 1) bili = [60, 40, 0, 0];
                            else if(celve === 2) bili = [30, 60, 10, 0];
                            else if(celve === 3) bili = [ 0, 50, 50, 0];
                            else if(celve === 4) bili = [ 0, 10, 60, 30];
                            else if(celve === 5) bili = [ 0, 0, 40, 60];
                            break;
                        }else{
                            if(celve === 1) bili = [70, 30, 0, 0];
                            else if(celve === 2) bili = [30, 70, 0, 0];
                            else if(celve === 3) bili = [ 0, 50, 50, 0];
                            else if(celve === 4) bili = [ 0, 0, 70, 30];
                            else if(celve === 5) bili = [ 0, 0, 30, 70];
                            break;
                        }
                    case 3: // 项数为3时的示例比例
                        if(isTemplateA==0){
                            if(celve === 1) bili = [80, 20,0];
                            else if(celve === 2) bili = [60, 40, 0];
                            else if(celve === 3) bili = [ 20, 60, 20];
                            else if(celve === 4) bili = [ 0, 40, 60];
                            else if(celve === 5) bili = [ 0, 20, 80];
                            break;
                        }else{
                            if(celve === 1) bili = [90, 10,0];
                            else if(celve === 2) bili = [70, 30, 0];
                            else if(celve === 3) bili = [ 10, 80, 10];
                            else if(celve === 4) bili = [ 0, 30, 70];
                            else if(celve === 5) bili = [ 0, 10, 90];
                            break;
                        }
                    case 2: // 项数为2时的示例比例
                        if(isTemplateA==0){
                            if(celve === 1) bili = [ 50, 50];
                            else if(celve === 2) bili = [ 40, 60];
                            else if(celve === 3) bili = [ 30, 70];
                            else if(celve === 4) bili =[ 20, 80];
                            else if(celve === 5) bili = [ 10, 90];
                            break;
                        }else{
                            if(celve === 1) bili = [ 40, 60];
                            else if(celve === 2) bili = [ 30, 70];
                            else if(celve === 3) bili = [ 20, 80];
                            else if(celve === 4) bili =[ 10, 90];
                            else if(celve === 5) bili = [ 10, 90];
                            break;
                        }
                }

                // 存入全局变量（三个方向用相同分档）
                window["bili_weidu_"+dimension+"_left_"+item] =[...bili].reverse();
                window["bili_weidu_"+dimension+"_middle_"+item] = bili;
                window["bili_weidu_"+dimension+"_right_"+item] = bili;
            }
        }
    }

    function reverseIntArray(arr) {
        // 创建原始数组的副本
        var newArr = arr.slice();

        // 使用 reverse() 方法来反转新的整数列表
        newArr.reverse();

        return newArr;
    }
    function addIntArray(arr, num) {
        // 创建原始数组的副本
        var newArr = arr.slice();

        // 在列表的起始位置插入指定数量的0
        for (var i = 0; i < num; i++) {
            newArr.unshift(0);
        }

        return newArr;
    }
    function selectDuoXuanOptions(min_options, bili, ops) {
        let temp_flag = 0;

        while (temp_flag < min_options) {
            let temp_answer = [];
            for (let count = 0; count < bili.length; count++) {
                if (duoxuan(bili[count])) {
                    temp_answer.push(count);
                    temp_flag += 1;
                }
                if (count == bili.length - 1) {
                    if (temp_flag < min_options) {
                        temp_flag = 0;
                    } else {
                        for (let count = 0; count < temp_answer.length; count++) {
                            ops[temp_answer[count]].click();
                        }
                    }
                }
            }
        }
    }
    function generateRandomEmail() {
        // 常见邮箱域名及其生成规则
        const domains = [
            { domain: 'qq.com', type: 'numeric' },      // QQ邮箱通常为纯数字
            { domain: '163.com', type: 'alphanumeric' }, // 网易邮箱
            { domain: '126.com', type: 'alphanumeric' }, // 网易邮箱
            { domain: 'sina.com', type: 'alphanumeric' }, // 新浪邮箱
            { domain: 'sohu.com', type: 'alphanumeric' }, // 搜狐邮箱
        ];

        // 随机选择一个域名
        const selectedDomain = domains[Math.floor(Math.random() * domains.length)];

        // 根据域名类型生成用户名
        let username;
        if (selectedDomain.type === 'numeric') {
            // QQ邮箱：纯数字用户名
            const usernameLength = Math.floor(Math.random() * 5) + 8; // 8到12位数字
            username = Array.from({ length: usernameLength }, () => Math.floor(Math.random() * 10)).join('');
        } else {
            // 其他邮箱：字母和数字组合
            const chars = 'abcdefghijklmnopqrstuvwxyz';
            const alphanumericChars = 'abcdefghijklmnopqrstuvwxyz0123456789';
            const usernameLength = Math.floor(Math.random() * 5) + 6; // 6到10位

            username = '';
            for (let i = 0; i < usernameLength; i++) {
                username += alphanumericChars.charAt(Math.floor(Math.random() * alphanumericChars.length));
            }

            // 随机插入符号，增加真实感
            if (Math.random() > 0.5) {
                const symbols = ['.', '_'];
                const symbol = symbols[Math.floor(Math.random() * symbols.length)];
                username = username.slice(0, Math.floor(username.length / 2)) + symbol + username.slice(Math.floor(username.length / 2));
            }

            // 确保首字符大概率为字母
            if (Math.random() > 0.7) {
                username = chars.charAt(Math.floor(Math.random() * chars.length)) + username.slice(1);
            }
        }

        // 拼接邮箱地址
        return `${username}@${selectedDomain.domain}`;
    }


    function generateAnswer(id, value) {
        // 正则表达式
        const regex = /@@随机数\((\d+),(\d+)\)@@/;
        const regex_forDate = /@@范围内随机年月日\((\d{4}-\d{2}-\d{2}),(\d{4}-\d{2}-\d{2})\)@@/;

        // 处理字符串
        let dataList2 = value.toString()
        .replace(/"/g, '')
        .replace(/\（/g, '(')
        .replace(/\）/g, ')')
        .replace(/，/g, ',');

        // 匹配随机数
        const match = dataList2.match(regex);
        if (match) {
            const min = parseInt(match[1]);
            const max = parseInt(match[2]);
            if (min < max) {
                const randomValue = randomNum(min, max);
                return randomValue;
            } else {
                const randomValue = randomNum(max, min);
                return randomValue;
            }
        }

        // 匹配随机日期
        else if (regex_forDate.test(dataList2)) {
            const matchDate = dataList2.match(regex_forDate);
            const startDate = new Date(matchDate[1]);
            const endDate = new Date(matchDate[2]);
            if (startDate < endDate) {
                const randomDate = getRandomDate(matchDate[1], matchDate[2]);
                return randomDate;
            } else {
                const randomDate = getRandomDate(matchDate[2], matchDate[1]);
                return randomDate;
            }
        }

        // 匹配随机手机号
        else if (dataList2.includes("@@生成随机手机号@@")) {
            const mobile = getMoble();
            return mobile;
        }

        // 匹配随机邮箱
        else if (dataList2.includes("@@生成随机邮箱@@")) {
            const email = getEmail();
            return email;
        }

        // 匹配随机姓名
        else if (dataList2.includes("@@生成随机姓名@@")) {
            const name = getRandomName();
            return name;
        }

        // 默认处理
        else {
            const tiankong_list = dataList2.split(',').map(item => item.trim());
            const randomValue = tiankong_list[randomNum(0, tiankong_list.length - 1)];
            return randomValue
        }
    }

    function assignValuesDirectly(textareaMap) {
        textareaMap.forEach((value, id) => {
            const textarea = document.getElementById(id.replace('yifeng','tqq'));
            if (!textarea) return;

            const parent = textarea.parentElement;
            if (window.getComputedStyle(parent).display === 'block') {
                const answer = generateAnswer(id, value);
                textarea.value = answer; // 直接赋值
            }
        });
    }
    //end...
})();

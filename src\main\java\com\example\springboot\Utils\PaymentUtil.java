package com.example.springboot.Utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.security.MessageDigest;
import java.util.*;
import java.text.SimpleDateFormat;

public class PaymentUtil {

    private static final String API_URL = "https://zpayz.cn/mapi.php";
    private static final String SIGN_TYPE = "MD5";

    public static String generateOrderNumber() {
        Date currentDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String datePart = dateFormat.format(currentDate);
        Random random = new Random();
        int randomPart = random.nextInt(9000) + 1000;
        return datePart + randomPart;
    }

    public static String generateMd5Signature(Map<String, String> params, String secretKey) {
        List<Map.Entry<String, String>> sortedParams = new ArrayList<>(params.entrySet());
        sortedParams.sort(Map.Entry.comparingByKey());

        StringBuilder queryStringBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams) {
            String key = entry.getKey();
            String value = entry.getValue();
            queryStringBuilder.append(key).append("=").append(value).append("&");
        }
        String queryString = queryStringBuilder.toString();
        if (queryString.endsWith("&")) {
            queryString = queryString.substring(0, queryString.length() - 1);
        }

        String signString = queryString + secretKey;
        return md5(signString);
    }

    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                hexString.append(String.format("%02x", b & 0xFF));
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Map<String, String> initiatePayment(String pid, String type, String outTradeNo,
                                                      String notifyUrl, String name, String money,
                                                      String clientip, String param, String secretKey) {
        try {
            URL url = new URL(API_URL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);

            Map<String, String> params = new LinkedHashMap<>();
            params.put("pid", pid);
            params.put("type", type);
            params.put("out_trade_no", outTradeNo);
            params.put("notify_url", notifyUrl);
            params.put("name", name);
            params.put("money", money);
            params.put("clientip", clientip);
            params.put("param", param);
            params.put("sign_type", SIGN_TYPE);

            String signature = generateMd5Signature(params, secretKey);
            params.put("sign", signature);

            OutputStream os = conn.getOutputStream();
            os.write(getQuery(params).getBytes());
            os.flush();
            os.close();

            int responseCode = conn.getResponseCode();
            System.out.println("HTTP Response Code: " + responseCode);

            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            String responseString = response.toString();
            System.out.println("API Response: " + responseString);

            return parseResponse(responseString);

        } catch (Exception e) {
            throw new RuntimeException("Payment initiation failed", e);
        }
    }


    private static String getQuery(Map<String, String> params) {
        StringBuilder result = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (first) {
                first = false;
            } else {
                result.append("&");
            }
            result.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return result.toString();
    }


    // Assuming responseString is JSON
    private static Map<String, String> parseResponse(String response) {
        Map<String, String> result = new HashMap<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(response);

            // 检查响应的 code 字段是否为 1，表示成功
            if (rootNode.has("code") && rootNode.get("code").asText().equals("1")) {
                result.put("img", rootNode.path("img").asText()); // 返回二维码图片 URL
                result.put("price", rootNode.path("price").asText()); // 返回令牌价格
                result.put("qrcode", rootNode.path("qrcode").asText()); // 返回令牌价格
            } else {
                result.put("error", rootNode.path("msg").asText()); // 处理失败时的错误消息
            }
        } catch (Exception e) {
            result.put("error", "Response parsing failed: " + e.getMessage());
        }
        return result;
    }

    // 计算总费用的封装方法
    public static double calculateTotalCost(int numberOfQuestions, int targetCount, boolean changeIp) {
        // 计算 actual 值
        int actual = numberOfQuestions <= 15 ? 1 :
                numberOfQuestions <= 25 ? 2 :
                        numberOfQuestions <= 35 ? 3 :
                                numberOfQuestions <= 50 ? 4 : 5;

        // 根据是否换IP确定单价系数
        double unitCostFirst100 = changeIp ? 0.1 : 0.08;
        double unitCostAfter100 = changeIp ? 0.08 : 0.06;
        double unitCostAfter200 = changeIp ? 0.06 : 0.04; // 200 份后的单价

        // 计算前 100 份的费用
        double costForFirst100 = Math.min(targetCount, 100) * unitCostFirst100;

        // 计算 101 到 200 份的费用
        double costForAfter100 = Math.max(0, Math.min(targetCount - 100, 100)) * unitCostAfter100;

        // 计算超过 200 份的费用
        double costForAfter200 = Math.max(0, targetCount - 200) * unitCostAfter200;

        // 计算总费用并应用最低消费 3 元
        double totalCost = (actual - 1) * 2 + costForFirst100 + costForAfter100 + costForAfter200;
        return totalCost < 3 ? 3 : totalCost; // 应用最低消费 3 元
    }


    public static double calculateTotalCostForEXCEL(boolean changeIp, int fillRangeStart, int fillRangeEnd) {
        // 计算目标份数
        int targetCount = fillRangeEnd - fillRangeStart + 1;

        return calculateTotalCostForEXCEL(changeIp, targetCount);
    }
    public static double calculateTotalCostForEXCEL(boolean changeIp, int targetCount) {
        // 根据是否换IP确定单价
        double unitCostBefore200 = changeIp ? 0.08 : 0.06;  // 200 份之前的单价
        double unitCostAfter200 = changeIp ? 0.06 : 0.04;  // 200 份之后的单价

        // 计算 200 份之前和之后的费用
        double costBefore200 = Math.min(targetCount, 200) * unitCostBefore200;
        double costAfter200 = Math.max(0, targetCount - 200) * unitCostAfter200;

        // 计算总费用
        double totalCost = costBefore200 + costAfter200;

        // 应用最低消费 3 元
        if (totalCost < 3) {
            totalCost = 3; // 最低消费 3 元
        }

        return totalCost;
    }

    public static double calculateTotalCostForUpload(boolean changeIp, int targetCount) {
        // 根据是否换IP确定单价
        double unitCost = changeIp ? 0.06 : 0.04;

        // 计算总费用
        double totalCost = targetCount * unitCost;

        // 应用最低消费 3 元
        if (totalCost < 3) {
            totalCost = 3; // 最低消费 3 元
        }

        return totalCost;
    }

    public static double calculateTotalCostForDB(int numberOfQuestions, boolean changeIp, int targetCount) {
        // 计算 actual 值
        int actual = numberOfQuestions <= 15 ? 1 :
                numberOfQuestions <= 25 ? 2 :
                        numberOfQuestions <= 35 ? 3 :
                                numberOfQuestions <= 50 ? 4 : 5;

        // 根据是否换IP确定单价
        double unitCostBefore200 = changeIp ? 0.08 : 0.06;
        double unitCostAfter200 = changeIp ? 0.06 : 0.04;

        // 计算 200 份之前和之后的费用
        double costBefore200 = Math.min(targetCount, 200) * unitCostBefore200;
        double costAfter200 = Math.max(0, targetCount - 200) * unitCostAfter200;

        // 计算总费用
        double totalCost = (actual - 1) * 1 + costBefore200 + costAfter200;

        // 应用最低消费 3 元
        if (totalCost < 3) {
            totalCost = 3; // 最低消费 3 元
        }

        return totalCost;
    }


    public static double calculateSettlementPrice(int actual, boolean changeIP, int totalCount) {
        double unitCostBefore200 = changeIP ? 0.08 : 0.06;
        double unitCostAfter200 = changeIP ? 0.06 : 0.04;

        double costBefore200 = Math.min(totalCount, 200) * unitCostBefore200;
        double costAfter200 = Math.max(0, totalCount - 200) * unitCostAfter200;

        double total = (actual - 1) * 1 + costBefore200 + costAfter200;
        return total < 3 ? 3 : total;
    }

    // 根据价格反推 same questions
    public static int reverseCalculateActual(Double maxPrice, int targetCount,boolean changeIp) {
        for (int i = 1; i <= 5; i++) {
            double calculated = calculateSettlementPrice(i,changeIp,targetCount);
            if (Math.abs(maxPrice - calculated) <= 0.1) {
                return i;
            }
        }
        return 1;
    }



    // AES 解密方法
    public static String decryptAES(String encryptedText, String secretKey) throws Exception {
        byte[] decodedKey = secretKey.getBytes("UTF-8");
        SecretKeySpec key = new SecretKeySpec(decodedKey, "AES");
        IvParameterSpec iv = new IvParameterSpec("1234567890123456".getBytes("UTF-8")); // 与前端使用相同的 IV

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding"); // 使用 AES/CBC/PKCS5Padding 模式
        cipher.init(Cipher.DECRYPT_MODE, key, iv);

        byte[] decodedValue = Base64.getDecoder().decode(encryptedText);
        byte[] decryptedValue = cipher.doFinal(decodedValue);
        return new String(decryptedValue, "UTF-8");
    }


    /**
     * 根据 planId 获取对应的支付金额和实际到账代币数量
     *
     * @param planId 计划编号 (1~6)
     * @return 包含 "payAmount" 和 "actualCredit" 的 Map，如果 planId 不合法则返回 null
     */
    public static Map<String, Double> getPlanDetails(int planId) {
        Map<String, Double> planDetails = new HashMap<>();
        switch (planId) {
            case 1:
                planDetails.put("payAmount", 5.0);
                planDetails.put("actualCredit", 5.0);
                break;
            case 2:
                planDetails.put("payAmount", 10.0);
                planDetails.put("actualCredit", 10.0);
                break;
            case 3:
                planDetails.put("payAmount", 20.0);
                planDetails.put("actualCredit", 20.0);
                break;
            case 4:
                planDetails.put("payAmount", 30.0);
                planDetails.put("actualCredit", 30.0);
                break;
            case 5:
                planDetails.put("payAmount", 50.0);
                planDetails.put("actualCredit", 70.0);
                break;
            case 6:
                planDetails.put("payAmount", 100.0);
                planDetails.put("actualCredit", 150.0);
                break;
            case 7:
                planDetails.put("payAmount", 300.0);
                planDetails.put("actualCredit", 500.0);
                break;
            case 8:
                planDetails.put("payAmount", 500.0);
                planDetails.put("actualCredit", 1000.0);
                break;
            case 9:
                planDetails.put("payAmount", 1000.0);
                planDetails.put("actualCredit", 2200.0);
                break;
            default:
                // 不合法的 planId
                return null;
        }
        return planDetails;
    }




    public static double calculateRefund(int type, double originalPaid, int originalTargetCount, int actualTargetCount, boolean changeIp,boolean isExportExcel) {
        // 验证实际刷的份数不超过原订单
        if (actualTargetCount > originalTargetCount) {
            throw new IllegalArgumentException("实际刷的份数不能超过原订单份数");
        }

        // 根据类型计算实际应付金额
        double actualCost = 0.0;
//        switch (type) {
//            case 1:
//                actualCost = (1.0 * actualTargetCount)/originalTargetCount * originalPaid;
//                break;
//            case 2:
//                actualCost = calculateTotalCostForUpload(changeIp, actualTargetCount);
//                break;
//            case 3:
//                actualCost = calculateTotalCostForEXCEL(changeIp, actualTargetCount);
//                break;
//            default:
//                throw new IllegalArgumentException("无效的类型: " + type);
//        }
        actualCost = (1.0 * actualTargetCount)/originalTargetCount * originalPaid;
        // 四舍五入到小数点后两位
        BigDecimal actualCostRounded = BigDecimal.valueOf(actualCost)
                .setScale(2, RoundingMode.HALF_UP);
        actualCost = actualCostRounded.doubleValue();

        // 计算退款金额（确保非负）
//        System.out.println("actualCost:" + actualCost);
//        System.out.println("originalPaid:" + originalPaid);

        double refund = originalPaid - actualCost;
        BigDecimal tempRefund = BigDecimal.valueOf(refund)
                .setScale(2, RoundingMode.HALF_UP);
        refund = tempRefund.doubleValue();
        if (isExportExcel) {
            return Math.min(Math.max(refund, 0.0),originalPaid*0.8);
        }
        return Math.max(refund, 0.0);
    }

    public static void main(String[] args) {
        double v = calculateRefund(1, 60, 900, 1, true,true);
        System.out.println(v);
//        double v1 = calculateTotalCostForDB(1, false, 100);
//        System.out.println(v1);
    }
}

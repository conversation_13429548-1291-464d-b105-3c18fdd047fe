package com.example.springboot.mapper;

import com.example.springboot.entity.TokenVault;
import com.example.springboot.entity.WJXOrder;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface TokenVaultMapper {

    // 查询代币信息
    @Select("SELECT * FROM tokenvault WHERE activation_code = #{tokenCode}")
    TokenVault findByTokenCode(String tokenCode);

    // 支付代币操作
    @Update("UPDATE tokenvault SET token_balance = token_balance - #{amount} WHERE activation_code = #{tokenCode} AND token_balance >= #{amount}")
    int deductTokenBalance(@Param("tokenCode") String tokenCode, @Param("amount") double amount);

    @Insert("INSERT INTO tokenvault (token_balance, activation_code, remark, created_at, updated_at,invitation_code,rebate_received,invited_by_code) " +
            "VALUES (#{tokenBalance}, #{tokenCode}, #{remark}, NOW(), NOW(),#{invitationCode},#{rebateReceived},#{invitedByCode})")
    void insert(TokenVault tokenVault);

    // 查询消费记录（支持搜索和分页）
    @Select("<script>" +
            "SELECT * FROM wjx_order " +
            "WHERE activation_code = #{tokenCode} " +
            "<if test='search != null and search != \"\"'> " +
            "AND order_number LIKE CONCAT('%', #{search}, '%') " +
            "</if> " +
            "ORDER BY created_time DESC " +
            "LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<WJXOrder> findOrdersByActivationCodeAndSearch(
            @Param("tokenCode") String tokenCode,
            @Param("search") String search,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize);

    // 查询消费记录总数（支持搜索）
    @Select("<script>" +
            "SELECT COUNT(*) FROM wjx_order " +
            "WHERE activation_code = #{tokenCode} AND is_deleted = 0 AND is_pay = 1 " +
            "<if test='search != null and search != \"\"'> " +
            "   <choose> " +
            "       <when test='search.matches(\"\\\\d+\")'> " +
            "           AND order_number LIKE CONCAT('%', #{search}, '%') " +
            "       </when> " +
            "       <otherwise> " +
            "           AND survey_link LIKE CONCAT('%', #{search}, '%') " +
            "       </otherwise> " +
            "   </choose> " +
            "</if> " +
            "<if test='incomplete'> " +
            "   AND order_status != 6 AND order_status != -10 " +
            "   AND order_number NOT REGEXP '[一-龥]' " +
            "</if> " +
            "<if test='abnormal'> " +
            "   AND order_status != -9 AND order_status != -10 AND order_status &lt; 0 " +
            "   AND order_number NOT REGEXP '[一-龥]' " +  // 过滤中文订单号
            "</if> " +
            "</script>")
    int countOrdersByActivationCodeAndSearch(
            @Param("tokenCode") String tokenCode,
            @Param("search") String search,
            @Param("incomplete") boolean incomplete,
            @Param("abnormal") boolean abnormal);

    // 添加代币余额（充值）
    @Update("UPDATE tokenvault SET token_balance = token_balance + #{rechargeAmount}, updated_at = NOW() WHERE activation_code = #{tokenCode}")
    void addTokenBalance(@Param("tokenCode") String tokenCode, @Param("rechargeAmount") BigDecimal rechargeAmount);

    /**
     * 检查激活码是否存在
     *
     * @param tokenCode 要检查的激活码
     * @return 如果存在返回 true，否则返回 false
     */
    @Select("SELECT EXISTS(SELECT 1 FROM tokenvault WHERE activation_code = #{tokenCode})")
    boolean existsByTokenCode(@Param("tokenCode") String tokenCode);

    // 在Mapper接口中添加一个查询合并后的记录的方法
    @Select("<script>" +
            "SELECT created_time AS date, order_number, -price AS tokens_used " +
            "FROM wjx_order " +
            "WHERE activation_code = #{tokenCode} AND is_deleted = 0 AND is_pay = 1 " +
            "<if test='search != null and search != \"\"'> " +
            "   <choose> " +
            "       <when test='search.matches(\"\\\\d+\")'> " +
            "           AND order_number LIKE CONCAT('%', #{search}, '%') " +
            "       </when> " +
            "       <otherwise> " +
            "           AND survey_link LIKE CONCAT('%', #{search}, '%') " +
            "       </otherwise> " +
            "   </choose> " +
            "</if> " +
            "<if test='incomplete'> " +
            "   AND order_status != 6 AND order_status != -10" +
            "   AND order_number NOT REGEXP '[一-龥]' " +
            "</if> " +
            "<if test='abnormal'> " +
            "   AND order_status != -9 AND order_status != -10 AND order_status &lt; 0 " +
            "   AND order_number NOT REGEXP '[一-龥]' " +  // 过滤中文订单号
            "</if> " +
            "UNION ALL " +
            "SELECT created_at AS date, remark AS order_number, recharge_amount AS tokens_used " +
            "FROM tokenvault_recharge_log " +
            "WHERE activation_code = #{tokenCode} " +
            "<if test='search != null and search != \"\"'> " +
            "   AND remark LIKE CONCAT('%', #{search}, '%') " +
            "</if> " +
            "<if test='abnormal'> " +
            "   AND remark NOT REGEXP '[一-龥]' " +  // 过滤充值记录中的中文备注
            "</if> " +
            "<if test='incomplete'> " +
            "   AND remark NOT REGEXP '[一-龥]' " +  // 过滤充值记录中的中文备注
            "</if> " +
            "ORDER BY date DESC " +
            "LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<Map<String, Object>> findUsageRecords(
            @Param("tokenCode") String tokenCode,
            @Param("search") String search,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("incomplete") boolean incomplete,
            @Param("abnormal") boolean abnormal);

    @Select("<script>" +
            "SELECT * FROM tokenvault " +
            "<where>" +
            "  <if test='search != null'>" +
            "    activation_code LIKE CONCAT('%', #{search}, '%') OR " +
            "    remark LIKE CONCAT('%', #{search}, '%')" +
            "  </if>" +
            "</where>" +
            "ORDER BY created_at DESC" +
            "</script>")
    List<TokenVault> searchTokens(@Param("search") String search);

    @Select("SELECT * FROM tokenvault ORDER BY created_at DESC")
    List<TokenVault> findAllTokens();

    @Update("UPDATE tokenvault SET invitation_code = #{invitationCode}, rebate_received = #{rebateReceived} WHERE activation_code = #{tokenCode}")
    void updateInvitationInfo(TokenVault tokenVault);

    // 在TokenVaultMapper中添加
    @Select("SELECT * FROM tokenvault WHERE invitation_code = #{invitationCode}")
    TokenVault findByInvitationCode(@Param("invitationCode") String invitationCode);

    @Update("UPDATE tokenvault SET token_balance = token_balance + #{amount}, rebate_received = rebate_received + #{amount} WHERE activation_code = #{tokenCode}")
    void addRebate(@Param("tokenCode") String tokenCode, @Param("amount") BigDecimal amount);

    @Update("UPDATE tokenvault SET token_balance = 0 WHERE activation_code = #{tokenCode}")
    void clearTokenBalance(@Param("tokenCode") String tokenCode);

    @Update("UPDATE tokenvault SET token_balance = #{tokenCount} WHERE id = #{id}")
    void updateTokenCount(TokenVault tokenVault);
}

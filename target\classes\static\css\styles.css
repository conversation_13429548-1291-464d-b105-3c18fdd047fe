/* styles.css */

/* Reset default margin and padding */
body, h1, h2, h3, p, ul, li {
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.center-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.page-container {
    padding: 40px;
    text-align: center;
}

.login-box {
    background-color: #fff;
    border-radius: 20px;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 400px;
    margin: 0 auto;
    text-align: center;
}

h2 {
    color: #333;
    margin-bottom: 20px;
}

form {
    text-align: center;
}

.input-container {
    text-align: left;
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
    color: #666;
}

.input-field {
    width: 100%;
    padding: 15px;
    border: 1px solid #ccc;
    border-radius: 10px;
    background-color: #f9f9f9;
}

.captcha-input {
    display: flex;
    align-items: center;
}

.captcha-image {
    margin-left: 10px;
    max-height: 36px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.login-button {
    background-color: #007bff;
    color: #fff;
    padding: 15px 30px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.login-button:hover {
    background-color: #0056b3;
}

.purchase-note {
    font-size: 16px;
    color: #666;
    margin-top: 20px;
}

.purchase-link {
    color: #007bff;
    text-decoration: none;
}

.purchase-link:hover {
    text-decoration: underline;
}

.footer {
    text-align: center;
    margin-top: 30px;
    font-size: 14px;
    color: #666;
}

.qq-group-link {
    color: #007bff;
    text-decoration: none;
}

.qq-group-link:hover {
    text-decoration: underline;
}
/* styles.css */

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>易风问卷</title>
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"> -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(to right, #e0f7fa, #80deea); /* 渐变背景 */
            height: 100vh; /* 设置高度为视口高度 */
            display: flex; /* 使用 flexbox 布局 */
            align-items: center; /* 垂直居中 */
            justify-content: center; /* 水平居中 */
            margin: 0; /* 清除默认边距 */
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 15px; /* 添加圆角 */
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
            max-width: 700px;
            width: 100%; /* 确保容器宽度适应 */
        }

        h2 {
            color: #007bff; /* 标题颜色 */
            font-weight: 600; /* 标题字体加粗 */
        }

        .form-label {
            font-weight: bold;
            color: #555; /* 标签颜色 */
        }

        .input-group .btn {
            background-color: #007bff;
            color: white;
            border-radius: 5px; /* 按钮圆角 */
        }

        .input-group .btn:hover {
            background-color: #0056b3;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap; /* 允许换行 */
            gap: 10px; /* 按钮之间的间距 */
        }

        .button-group a {
            flex: 1 1 45%; /* 每个按钮占一行的45%，两行排布 */
            margin: 5px; /* 按钮之间的间距 */
            text-align: center;
            color: white;
            padding: 10px 0;
            border-radius: 5px;
            text-decoration: none;
        }
        .button-group a:nth-child(1) {
            background-color: #6c757d; /* 灰蓝色 */
        }

        .button-group a:nth-child(1):hover {
            background-color: #5a6268;
        }

        .button-group a:nth-child(2) {
            background-color: #d6336c; /* 深玫红色 */
        }

        .button-group a:nth-child(2):hover {
            background-color: #b02a56;
        }

        .button-group a:nth-child(3) {
            background-color: #17a2b8; /* 青绿色 */
        }

        .button-group a:nth-child(3):hover {
            background-color: #138496;
        }

        .button-group a:nth-child(4) {
            background-color: #fd7e14; /* 橙色 */
        }

        .button-group a:nth-child(4):hover {
            background-color: #e3640d;
        }

        .button-group a:nth-child(5) {
            background-color: #28a745; /* 绿色 */
        }

        .button-group a:nth-child(5):hover {
            background-color: #218838;
        }


        /* 缩小版提示样式 */
        .custom-script-notice {
            margin-top: 10px;
            padding: 8px; /* 缩小内边距 */
            background-color: #f8d7da; /* 红色背景提示 */
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            text-align: center;
            font-size: 14px; /* 缩小字体 */
        }

        .custom-script-notice a {
            color: #007bff;
            text-decoration: none;
            font-weight: normal; /* 去掉加粗 */
        }

        .custom-script-notice a:hover {
            text-decoration: underline;
        }

        .disclaimer {
            margin-top: 15px;
            margin-left: 5px;
            margin-right: 5px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 6px;
            color: #666;
            font-size: 12px;
            text-align: center;
            line-height: 1.5;
            border: 1px solid #dee2e6;
        }

        /* 原分割线样式微调 */
        hr {
            margin: 25px 0 15px; /* 下边距从25px减少到15px */
        }

        .input-group-append {
            margin-left: 5px;
            display: flex;
            gap: 5px;
        }
        #stepTestBtn {
            background-color: #28a745; /* 绿色 */
        }
        #stepTestBtn:hover {
            background-color: #218838;
        }
        /* 优化后的标签样式 */
        .discount-tag {
            position: absolute;
            top: -10px;
            right: -8px;
            background: linear-gradient(135deg, #6c5ce7 0%, #a363d9 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            animation:
                    soft-glow 3s infinite alternate,
                    gentle-bounce 2s infinite;
            box-shadow: 0 2px 6px rgba(108,92,231,0.2);
            z-index: 1;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        @keyframes soft-glow {
            0% { opacity: 0.9; }
            50% { opacity: 1; box-shadow: 0 3px 10px rgba(108,92,231,0.3); }
            100% { opacity: 0.9; }
        }

        @keyframes gentle-bounce {
            0% { transform: translateY(0); }
            50% { transform: translateY(-2px); }
            100% { transform: translateY(0); }
        }
        .btn-primary, .btn-secondary {
            border: none !important; /* 使用 !important 确保优先级 */
        }
    </style>
</head>
<body>

<div class="container">
    <div class="d-flex align-items-center justify-content-center mb-4">
        <a href="/">
            <img src="imgs/logo.png" alt="Image Alt Text" style="max-width: 60px; margin-right: 20px;">
        </a>
        <h2 class="text-center mb-0">易风一键填问卷</h2>
    </div>
    <form action="createOrder" method="post" id="surveyForm">
        <!-- 问卷链接输入 -->
        <div class="mb-3">
<!--            <label for="survey_link" class="form-label">问卷链接</label>-->
            <div class="input-group">
                <input type="text" id="survey_link" name="surveyUrl" class="form-control" placeholder="请输入问卷星平台的问卷链接">
                <div class="input-group-append">
                    <button type="submit" class="btn btn-primary">旧版解析</button>
                    <button type="button" class="btn btn-secondary ml-2" id="stepTestBtn">
                        新版解析
                        <span class="discount-tag">支持多维度</span>
                    </button>
                </div>
            </div>
        </div>
    </form>
    <!-- 添加分割线 -->
    <hr style="border: 1px solid #ddd; margin: 25px 0;">
    <!-- 新增按钮组 -->
    <div class="button-group">
        <a href="createOrderByJS">
            <img src="imgs/上传.png" alt="上传图标" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 3px;margin-bottom: 2px">
            上传脚本填问卷
        </a>
        <a href="createOrderByExcel">
            <img src="imgs/excel.png" alt="EXCEL图标" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 3px;margin-bottom: 3px">
            EXCEL回填问卷
        </a>
        <a href="queryOrder">
            <img src="imgs/搜索.png" alt="搜索图标" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 3px;margin-bottom: 2px">
            查询已付款订单
        </a>
        <a href="tokenvault">
            <img src="imgs/搜索.png" alt="搜索图标" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 3px;margin-bottom: 2px">
            查询代币码信息
        </a>
        <a href="https://www.yuque.com/yangyang-bnict/axszk1/sbrsk1lgn4eodeow" target="_blank">
            <img src="imgs/教程图标.png" alt="教程图标" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 3px;margin-bottom: 2px">
            必看教程
        </a>
    </div>
    <!-- 新增免责声明 -->
    <div class="disclaimer">
        本平台生成数据仅供个人学习测试使用，不得用于任何商业或非法用途。使用者应自行承担相关责任。
    </div>
</div>

<!-- 在body结束前添加 -->
<div class="beian-footer"
     style="
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        padding: 6px 0;
        border-top:  rgba(0,0,0,0.08);
        z-index: 999;
        backdrop-filter: blur(3px);
     ">
    <a href="https://beian.miit.gov.cn"
       target="_blank"
       style="
          color: #999;
          font-size: 12px;
          text-decoration: none;
          transition: color 0.3s;
       "
       onmouseover="this.style.color='#666'"
       onmouseout="this.style.color='#999'">
        苏ICP备2023029611号
    </a>
</div>
<script>
    // 提取验证函数
    function validateForm() {
        const surveyLinkInput = document.getElementById('survey_link');
        let surveyLink = surveyLinkInput.value.trim();

        if (surveyLink === '') {
            alert('请输入问卷链接');
            return false;
        }

        const urlPattern = /^(https?:\/\/(?:[\w-]+\.)?wjx\.(cn|com|top)\/(vm|vj|.+)\/.*)$/;
        if (!urlPattern.test(surveyLink)) {
            alert('请输入有效的问卷链接');
            return false;
        }

        if (surveyLink.includes('tp.wjx')) {
            alert('平台不支持投票类型问卷');
            return false;
        }

        surveyLink = surveyLink.replace(/\/vj\//, '/vm/');
        surveyLink = surveyLink.split('?')[0];
        surveyLink = surveyLink.split('#')[0];
        surveyLinkInput.value = surveyLink;

        return true;
    }

    // 原提交按钮处理
    document.getElementById('surveyForm').addEventListener('submit', function(event) {
        event.preventDefault();
        if (validateForm()) {
            this.action = 'createOrder';
            this.submit();
        }
    });

    // 分维度解析按钮处理
    document.getElementById('stepTestBtn').addEventListener('click', function() {
        if (validateForm()) {
            const form = document.getElementById('surveyForm');
            form.action = 'createOrderTest';
            form.submit();
        }
    });

    // 原有域名跳转逻辑保持不变
    (function() {
        const currentUrl = window.location.href;
        if (currentUrl.includes("***********:9090")) {
            window.location.href = currentUrl.replace("***********:9090", "www.yifengwenjuan.top");
        }
    })();
</script>

</body>
</html>

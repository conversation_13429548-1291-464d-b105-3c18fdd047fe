package com.example.springboot.entity;

import lombok.Data;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Data
public class TokenVaultRechargeLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // 充值记录 ID

    @Column(nullable = false, length = 64)
    private String activationCode; // 激活码（与 TokenVault 关联）

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal rechargeAmount; // 充值金额

    @Column(precision = 10, scale = 2)
    private BigDecimal actualPay; // 充值金额

    private String remark; // 备注信息

    @Column(length = 50)
    private String outTradeNo; // 订单号

    @Column(length = 50)
    private String tradeNo; // 支付宝订单号

    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt; // 创建时间

    @Column(unique = true, nullable = false, length = 64)
    private String invitationCode; // 邀请码

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
    }
}

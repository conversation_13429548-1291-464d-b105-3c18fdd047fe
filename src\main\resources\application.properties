# application.properties
# 视图前缀和后缀配置
spring.mvc.view.prefix=/templates/
spring.mvc.view.suffix=.html

app.jsVersion=${random.int}
spring.thymeleaf.enabled=true
# 设置 WebSocket 握手超时时间为 10 秒
spring.websocket.handshake-timeout=1000000

# 定义网站图标的位置
spring.mvc.favicon.location=/static/favicon.ico
#允许循环引用
spring.main.allow-circular-references=true

# 最大连接数
spring.datasource.hikari.maximum-pool-size=50
# 最小空闲连接数
spring.datasource.hikari.minimum-idle=30

# config.properties
# 支付平台ID
mypid=20230826000228
# 支付平台加密密钥
key=k5Nov9OuU2fKDsymfs2vVETzqJWtJ3Lm

# 服务IP地址
# myservice=127.0.0.1  # 如果要使用本地服务，请取消注释此行
#myservice=************
myservice=***********
myport=9090

server.servlet.context-path=/


spring.mail.host=smtp.qq.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=fojgofwaxsoabeff
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.protocol=smtp

server.port = 9090

# 配置 TypeHandler 扫描
mybatis.type-handlers-package=com.example.springboot.handler


#server.port = 443
#server.ssl.key-store = classpath:yifengwenjuan.top.jks
#server.ssl.key-store-password = osue1vn2
#server.ssl.keyStoreType = JKS

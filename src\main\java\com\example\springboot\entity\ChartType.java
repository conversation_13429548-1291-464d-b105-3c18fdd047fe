package com.example.springboot.entity;

/**
 * 统计图表类型枚举
 */
public enum ChartType {
    SCATTER_PLOT("散点图"),
    FACTOR_LOADING_MAP("因子载荷图"),
    REGRESSION_LINE("回归线图"),
    MEDIATION_PATH("中介路径图"),
    CORRELATION_MATRIX("相关矩阵热力图"),
    RELIABILITY_ITEM_ANALYSIS("信度项目分析图"),
    DESCRIPTIVE_BAR("描述性统计柱状图"),
    DESCRIPTIVE_BOX("描述性统计箱线图"),
    FREQUENCY_HISTOGRAM("频数直方图"),
    NORMAL_Q_Q("正态Q-Q图");

    private final String description;

    ChartType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
} 
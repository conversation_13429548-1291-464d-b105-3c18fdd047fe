package com.example.springboot.tool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.commons.math3.linear.LUDecomposition;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import com.example.springboot.entity.AiChatMessage;
import com.example.springboot.entity.DataQuery;
import com.example.springboot.entity.FactorAnalysisOutput;
import com.example.springboot.mapper.AIChatMessageMapper;
import com.example.springboot.service.impl.AIChatServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 逆向数据调整工具类
 * 根据用户需求调整数据以达到特定的统计指标要求
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReverseDataAdjustmentTools {

    private final AIChatServiceImpl aiChatService;
    private final AIChatMessageMapper messageMapper;
    private final ObjectMapper objectMapper;
    private final Random random = new Random();

    /**
     * 调整数据结果类
     */
    public static class AdjustmentResult {
        private List<List<Object>> changedCells;
        private String explanation;
        private Map<String, Object> achievedMetrics;
        private boolean success;

        // Getters and Setters
        public List<List<Object>> getChangedCells() { return changedCells; }
        public void setChangedCells(List<List<Object>> changedCells) { this.changedCells = changedCells; }
        public String getExplanation() { return explanation; }
        public void setExplanation(String explanation) { this.explanation = explanation; }
        public Map<String, Object> getAchievedMetrics() { return achievedMetrics; }
        public void setAchievedMetrics(Map<String, Object> achievedMetrics) { this.achievedMetrics = achievedMetrics; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
    }

    /**
     * 获取Excel数据
     */
    private List<List<String>> getExcelData(String sessionId) throws Exception {
        AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
        if (message == null || message.getCompleteExcelData() == null) {
            throw new RuntimeException("未找到对应的Excel数据");
        }
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(message.getCompleteExcelData(), new TypeReference<List<List<String>>>() {});
    }

    /**
     * 获取数值型列数据
     */
    public List<Double> getNumericColumnData(String sessionId, int columnIndex) {
        try {
            List<List<String>> excelData = getExcelData(sessionId);
            return excelData.stream()
                    .skip(1) // 跳过表头
                    .map(row -> {
                        if (columnIndex >= 0 && columnIndex < row.size()) {
                            try {
                                return Double.parseDouble(row.get(columnIndex));
                            } catch (NumberFormatException e) {
                                return null;
                            }
                        }
                        return null;
                    })
                    .filter(value -> value != null)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("获取数值数据失败: " + e.getMessage());
        }
    }

  
    /**
     * 内部分析方法 - 计算Cronbach's Alpha系数（不保存到数据库）
     */
    public double calculateCronbachAlphaInternal(String sessionId, List<Integer> columns) {
        try {
            List<List<Double>> columnData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                columnData.add(data);
            }
            return calculateCronbachAlpha(columnData);
        } catch (Exception e) {
            log.error("[内部信度计算] 计算失败", e);
            return 0.0;
        }
    }

    /**
     * 内部分析方法 - 计算相关系数（不保存到数据库）
     */
    public double calculateCorrelationInternal(String sessionId, Integer col1, Integer col2) {
        try {
            List<Double> data1 = getNumericColumnData(sessionId, col1 - 1);
            List<Double> data2 = getNumericColumnData(sessionId, col2 - 1);
            if (data1.isEmpty() || data2.isEmpty()) {
                return 0.0;
            }
            return calculateCorrelation(data1, data2);
        } catch (Exception e) {
            log.error("[内部相关计算] 计算失败", e);
            return 0.0;
        }
    }

    /**
     * 内部分析方法 - 计算回归统计（不保存到数据库）
     */
    public Map<String, Object> calculateRegressionInternal(String sessionId, Integer dependentVar, List<Integer> independentVars) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Double> yData = getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xData = new ArrayList<>();
            for (Integer col : independentVars) {
                xData.add(getNumericColumnData(sessionId, col - 1));
            }

            if (yData.isEmpty() || xData.isEmpty()) {
                result.put("rSquared", 0.0);
                result.put("coefficients", new double[0]);
                return result;
            }

            int sampleSize = yData.size();
            int numPredictors = independentVars.size();

            // 使用Apache Commons Math进行回归分析
            org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regression =
                new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();

            double[][] xMatrix = new double[sampleSize][numPredictors];
            for (int i = 0; i < sampleSize; i++) {
                for (int j = 0; j < numPredictors; j++) {
                    xMatrix[i][j] = xData.get(j).get(i);
                }
            }
            double[] yArray = yData.stream().mapToDouble(Double::doubleValue).toArray();

            regression.newSampleData(yArray, xMatrix);

            result.put("rSquared", regression.calculateRSquared());
            result.put("coefficients", regression.estimateRegressionParameters());
            result.put("residualSumSquares", regression.calculateResidualSumOfSquares());

        } catch (Exception e) {
            log.error("[内部回归计算] 计算失败", e);
            result.put("rSquared", 0.0);
            result.put("coefficients", new double[0]);
        }
        return result;
    }

    /**
     * 内部分析方法 - 计算方差分析统计（不保存到数据库）
     */
    public Map<String, Object> calculateAnovaInternal(String sessionId, Integer groupCol, Integer valueCol) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<String> groupData = getColumnData(sessionId, groupCol - 1);
            List<Double> valueData = getNumericColumnData(sessionId, valueCol - 1);

            if (groupData.isEmpty() || valueData.isEmpty()) {
                result.put("fStatistic", 0.0);
                result.put("pValue", 1.0);
                return result;
            }

            // 按组分类数据
            Map<String, List<Double>> groupedData = new HashMap<>();
            for (int i = 0; i < groupData.size(); i++) {
                String group = groupData.get(i);
                groupedData.computeIfAbsent(group, k -> new ArrayList<>()).add(valueData.get(i));
            }

            // 计算F统计量（简化版本）
            double grandMean = valueData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double ssBetween = 0.0;
            double ssWithin = 0.0;
            int totalN = valueData.size();
            int numGroups = groupedData.size();

            for (List<Double> groupValues : groupedData.values()) {
                double groupMean = groupValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                ssBetween += groupValues.size() * Math.pow(groupMean - grandMean, 2);

                for (Double value : groupValues) {
                    ssWithin += Math.pow(value - groupMean, 2);
                }
            }

            double msBetween = ssBetween / (numGroups - 1);
            double msWithin = ssWithin / (totalN - numGroups);
            double fStatistic = msBetween / msWithin;

            result.put("fStatistic", fStatistic);
            result.put("ssBetween", ssBetween);
            result.put("ssWithin", ssWithin);
            result.put("dfBetween", numGroups - 1);
            result.put("dfWithin", totalN - numGroups);

        } catch (Exception e) {
            log.error("[内部方差分析计算] 计算失败", e);
            result.put("fStatistic", 0.0);
            result.put("pValue", 1.0);
        }
        return result;
    }

    /**
     * 内部分析方法 - 计算T检验统计（不保存到数据库）
     */
    public Map<String, Object> calculateTTestInternal(String sessionId, Integer groupCol, Integer testCol, String testType, Double testValue) {
        Map<String, Object> result = new HashMap<>();
        try {
            if ("independent".equals(testType)) {
                // 独立样本T检验
                List<String> groupData = getColumnData(sessionId, groupCol - 1);
                List<Double> testData = getNumericColumnData(sessionId, testCol - 1);

                if (groupData.isEmpty() || testData.isEmpty()) {
                    result.put("tStatistic", 0.0);
                    result.put("pValue", 1.0);
                    return result;
                }

                // 按组分类数据
                Map<String, List<Double>> groupedData = new HashMap<>();
                for (int i = 0; i < groupData.size(); i++) {
                    String group = groupData.get(i);
                    groupedData.computeIfAbsent(group, k -> new ArrayList<>()).add(testData.get(i));
                }

                if (groupedData.size() == 2) {
                    List<String> groups = new ArrayList<>(groupedData.keySet());
                    List<Double> group1 = groupedData.get(groups.get(0));
                    List<Double> group2 = groupedData.get(groups.get(1));

                    // 计算独立样本t统计量
                    DescriptiveStatistics stats1 = new DescriptiveStatistics();
                    group1.forEach(stats1::addValue);
                    DescriptiveStatistics stats2 = new DescriptiveStatistics();
                    group2.forEach(stats2::addValue);

                    double mean1 = stats1.getMean();
                    double mean2 = stats2.getMean();
                    double var1 = stats1.getVariance();
                    double var2 = stats2.getVariance();
                    int n1 = group1.size();
                    int n2 = group2.size();

                    double pooledVar = ((n1 - 1) * var1 + (n2 - 1) * var2) / (n1 + n2 - 2);
                    double tStatistic = (mean1 - mean2) / Math.sqrt(pooledVar * (1.0/n1 + 1.0/n2));

                    result.put("tStatistic", tStatistic);
                    result.put("mean1", mean1);
                    result.put("mean2", mean2);
                    result.put("n1", n1);
                    result.put("n2", n2);
                }

            } else if ("onesample".equals(testType) && testValue != null) {
                // 单样本T检验
                List<Double> testData = getNumericColumnData(sessionId, testCol - 1);
                if (!testData.isEmpty()) {
                    DescriptiveStatistics stats = new DescriptiveStatistics();
                    testData.forEach(stats::addValue);

                    double mean = stats.getMean();
                    double std = stats.getStandardDeviation();
                    int n = testData.size();

                    double tStatistic = (mean - testValue) / (std / Math.sqrt(n));

                    result.put("tStatistic", tStatistic);
                    result.put("sampleMean", mean);
                    result.put("testValue", testValue);
                    result.put("sampleSize", n);
                }
            }

        } catch (Exception e) {
            log.error("[内部T检验计算] 计算失败", e);
            result.put("tStatistic", 0.0);
            result.put("pValue", 1.0);
        }
        return result;
    }

    /**
     * 内部分析方法 - 计算因子分析指标（不保存到数据库）
     */
    public Map<String, Object> calculateFactorAnalysisInternal(String sessionId, List<Integer> columns) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<List<Double>> data = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> colData = getNumericColumnData(sessionId, col - 1);
                if (colData.isEmpty()) {
                    result.put("kmo", 0.0);
                    result.put("bartlettP", 1.0);
                    return result;
                }
                data.add(colData);
            }

            // 计算相关矩阵
            int numVars = data.size();
            double[][] correlationMatrix = new double[numVars][numVars];

            for (int i = 0; i < numVars; i++) {
                for (int j = 0; j < numVars; j++) {
                    if (i == j) {
                        correlationMatrix[i][j] = 1.0;
                    } else {
                        correlationMatrix[i][j] = calculateCorrelation(data.get(i), data.get(j));
                    }
                }
            }

            // 简化的KMO计算
            double kmo = calculateSimpleKMO(correlationMatrix);

            result.put("kmo", kmo);
            result.put("correlationMatrix", correlationMatrix);
            result.put("numVariables", numVars);

        } catch (Exception e) {
            log.error("[内部因子分析计算] 计算失败", e);
            result.put("kmo", 0.0);
            result.put("bartlettP", 1.0);
        }
        return result;
    }

    /**
     * 简化的KMO计算
     */
    private double calculateSimpleKMO(double[][] correlationMatrix) {
        int n = correlationMatrix.length;
        double sumR = 0.0;
        double sumA = 0.0;

        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i != j) {
                    double r = correlationMatrix[i][j];
                    sumR += r * r;

                    // 简化的反相关矩阵计算
                    double a = r / (1 + Math.abs(r));
                    sumA += a * a;
                }
            }
        }

        return sumR / (sumR + sumA);
    }

    /**
     * 计算Cronbach's Alpha系数
     */
    private double calculateCronbachAlpha(List<List<Double>> columnData) {
        int itemCount = columnData.size();
        int sampleSize = columnData.get(0).size();

        // 计算总分
        List<Double> totalScores = new ArrayList<>();
        for (int i = 0; i < sampleSize; i++) {
            double total = 0.0;
            for (List<Double> col : columnData) {
                total += col.get(i);
            }
            totalScores.add(total);
        }

        // 计算总方差
        DescriptiveStatistics totalStats = new DescriptiveStatistics();
        totalScores.forEach(totalStats::addValue);
        double totalVariance = totalStats.getVariance();

        // 计算各项目方差和
        double itemVarianceSum = 0.0;
        for (List<Double> col : columnData) {
            DescriptiveStatistics itemStats = new DescriptiveStatistics();
            col.forEach(itemStats::addValue);
            itemVarianceSum += itemStats.getVariance();
        }

        // 计算Cronbach's Alpha
        return (itemCount / (itemCount - 1.0)) * (1 - itemVarianceSum / totalVariance);
    }

    /**
     * 计算两个变量的相关系数
     */
    private double calculateCorrelation(List<Double> x, List<Double> y) {
        PearsonsCorrelation correlation = new PearsonsCorrelation();
        return correlation.correlation(
                x.stream().mapToDouble(Double::doubleValue).toArray(),
                y.stream().mapToDouble(Double::doubleValue).toArray());
    }

    /**
     * 生成具有指定相关性的数据
     */
    private List<Double> generateCorrelatedData(List<Double> baseData, double targetCorrelation) {
        int n = baseData.size();
        List<Double> result = new ArrayList<>();
        
        // 标准化基础数据
        DescriptiveStatistics baseStats = new DescriptiveStatistics();
        baseData.forEach(baseStats::addValue);
        double baseMean = baseStats.getMean();
        double baseStd = baseStats.getStandardDeviation();
        
        // 生成独立的随机数据
        NormalDistribution normalDist = new NormalDistribution();
        List<Double> independentData = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            independentData.add(normalDist.sample());
        }
        
        // 使用Cholesky分解方法生成相关数据
        for (int i = 0; i < n; i++) {
            double baseStandardized = (baseData.get(i) - baseMean) / baseStd;
            double correlatedValue = targetCorrelation * baseStandardized + 
                                   Math.sqrt(1 - targetCorrelation * targetCorrelation) * independentData.get(i);
            result.add(correlatedValue);
        }
        
        return result;
    }

    /**
     * 添加噪声到数据中
     */
    private List<Double> addNoise(List<Double> data, double noiseLevel) {
        return data.stream()
                .map(value -> value + random.nextGaussian() * noiseLevel)
                .collect(Collectors.toList());
    }

    /**
     * 标准化数据到指定范围
     */
    private List<Double> normalizeToRange(List<Double> data, double min, double max) {
        DescriptiveStatistics stats = new DescriptiveStatistics();
        data.forEach(stats::addValue);
        double dataMin = stats.getMin();
        double dataMax = stats.getMax();
        double range = dataMax - dataMin;
        
        if (range == 0) return data; // 避免除零
        
        return data.stream()
                .map(value -> min + (value - dataMin) / range * (max - min))
                .collect(Collectors.toList());
    }

    /**
     * 将连续数据转换为离散选项值
     */
    private List<String> convertToDiscreteOptions(List<Double> data, int numOptions) {
        DescriptiveStatistics stats = new DescriptiveStatistics();
        data.forEach(stats::addValue);
        double min = stats.getMin();
        double max = stats.getMax();
        double range = max - min;
        
        return data.stream()
                .map(value -> {
                    int option = (int) Math.floor((value - min) / range * numOptions) + 1;
                    return String.valueOf(Math.min(option, numOptions));
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取字符串列数据
     */
    public List<String> getColumnData(String sessionId, int columnIndex) {
        try {
            List<List<String>> excelData = getExcelData(sessionId);
            return excelData.stream()
                    .skip(1) // 跳过表头
                    .map(row -> (columnIndex >= 0 && columnIndex < row.size()) ? row.get(columnIndex) : "")
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("获取列数据失败: " + e.getMessage());
        }
    }

    /**
     * 创建调整结果
     */
    public AdjustmentResult createAdjustmentResult(List<List<Object>> changedCells,
                                                   String explanation,
                                                   Map<String, Object> metrics,
                                                   boolean success) {
        AdjustmentResult result = new AdjustmentResult();
        result.setChangedCells(changedCells);
        result.setExplanation(explanation);
        result.setAchievedMetrics(metrics);
        result.setSuccess(success);
        return result;
    }

    /**
     * 调整数据以达到目标信度系数
     */
    @Tool(description = "根据目标Cronbach's Alpha值调整量表数据，提高或降低信度系数到指定范围")
    public String adjustDataForReliability(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "需要调整的列索引列表（从1开始）") List<Integer> columns,
            @ToolParam(description = "目标Cronbach's Alpha值（0.0-1.0）") Double targetAlpha,
            @ToolParam(description = "允许的误差范围，默认0.02") Double tolerance) {

        log.info("[信度调整] 开始调整数据信度，sessionId={}, columns={}, targetAlpha={}",
                sessionId, columns, targetAlpha);

        try {
            // 参数验证
            if (targetAlpha == null || targetAlpha <= 0 || targetAlpha > 1) {
                throw new IllegalArgumentException("目标信度系数必须在0到1之间，当前值：" + targetAlpha);
            }
            if (targetAlpha < 0.5) {
                throw new IllegalArgumentException("目标信度系数过低（" + targetAlpha + "），建议设置在0.6以上");
            }

            if (tolerance == null) tolerance = 0.02;
            if (tolerance <= 0 || tolerance > 0.5) {
                throw new IllegalArgumentException("容差必须在0到0.5之间，当前值：" + tolerance);
            }

            if (columns == null || columns.isEmpty()) {
                throw new IllegalArgumentException("列索引列表不能为空");
            }

            // 验证列索引
            for (Integer col : columns) {
                if (col == null || col < 1) {
                    throw new IllegalArgumentException("列索引必须大于0，当前值：" + col);
                }
            }

            // 获取原始数据
            List<List<Double>> originalData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                originalData.add(new ArrayList<>(data));
            }

            // 计算当前信度 - 使用内部方法避免数据库保存
            double currentAlpha = calculateCronbachAlphaInternal(sessionId, columns);
            log.info("[信度调整] 当前Alpha值: {}, 目标Alpha值: {}", currentAlpha, targetAlpha);

            // 如果已经在目标范围内，无需调整
            if (Math.abs(currentAlpha - targetAlpha) <= tolerance) {
                return String.format("当前信度系数%.3f已在目标范围内，无需调整", currentAlpha);
            }

            List<List<Object>> changedCells = new ArrayList<>();
            List<List<Double>> adjustedData = new ArrayList<>(originalData);

            int maxIterations = 100;
            int iteration = 0;
            double bestAlpha = currentAlpha;
            List<List<Double>> bestData = new ArrayList<>(originalData);

            while (iteration < maxIterations && Math.abs(bestAlpha - targetAlpha) > tolerance) {
                iteration++;

                if (targetAlpha > currentAlpha) {
                    // 需要提高信度 - 增加项目间一致性
                    adjustedData = increaseReliability(adjustedData, targetAlpha);
                } else {
                    // 需要降低信度 - 增加随机噪声
                    adjustedData = decreaseReliability(adjustedData, targetAlpha);
                }

                double newAlpha = calculateCronbachAlpha(adjustedData);

                // 如果更接近目标值，保存这个结果
                if (Math.abs(newAlpha - targetAlpha) < Math.abs(bestAlpha - targetAlpha)) {
                    bestAlpha = newAlpha;
                    bestData = new ArrayList<>();
                    for (List<Double> col : adjustedData) {
                        bestData.add(new ArrayList<>(col));
                    }
                }

                log.debug("[信度调整] 迭代{}: Alpha={}", iteration, newAlpha);
            }

            // 自动检测量表范围
            double minOption = originalData.stream()
                .flatMap(List::stream)
                .mapToDouble(Double::doubleValue)
                .min().orElse(1.0);
            double maxOption = originalData.stream()
                .flatMap(List::stream)
                .mapToDouble(Double::doubleValue)
                .max().orElse(5.0);

            // 生成变更记录
            for (int colIdx = 0; colIdx < columns.size(); colIdx++) {
                List<Double> originalCol = originalData.get(colIdx);
                List<Double> adjustedCol = bestData.get(colIdx);

                for (int rowIdx = 0; rowIdx < originalCol.size(); rowIdx++) {
                    if (!originalCol.get(rowIdx).equals(adjustedCol.get(rowIdx))) {
                        // 转换为适当的选项值（自动检测量表范围）
                        double adjustedValue = adjustedCol.get(rowIdx);
                        int optionValue = Math.max((int)minOption, Math.min((int)maxOption, (int) Math.round(adjustedValue)));

                        changedCells.add(Arrays.asList(
                            rowIdx + 1, // 行号（跳过表头）
                            columns.get(colIdx) - 1, // 列号（0基索引）
                            String.valueOf(optionValue)
                        ));
                    }
                }
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("originalAlpha", currentAlpha);
            metrics.put("achievedAlpha", bestAlpha);
            metrics.put("targetAlpha", targetAlpha);
            metrics.put("iterations", iteration);
            metrics.put("cellsChanged", changedCells.size());
            metrics.put("columns", columns); // 传递columns参数用于重新计算

            // 生成调整说明
            String explanation = String.format(
                "成功调整数据信度系数从%.3f到%.3f（目标%.3f），共调整%d个单元格。调整后的数据更好地满足了信度要求。",
                currentAlpha, bestAlpha, targetAlpha, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[信度调整] 调整失败", e);
            return "信度调整失败: " + e.getMessage();
        }
    }

    /**
     * 提高数据信度的方法
     */
    private List<List<Double>> increaseReliability(List<List<Double>> data, double targetAlpha) {
        List<List<Double>> result = new ArrayList<>();
        int itemCount = data.size();
        int sampleSize = data.get(0).size();

        // 计算项目间平均相关性
        double totalCorr = 0.0;
        int corrCount = 0;
        for (int i = 0; i < itemCount; i++) {
            for (int j = i + 1; j < itemCount; j++) {
                totalCorr += calculateCorrelation(data.get(i), data.get(j));
                corrCount++;
            }
        }
        double avgCorr = totalCorr / corrCount;

        // 增加项目间一致性
        for (int i = 0; i < itemCount; i++) {
            List<Double> adjustedCol = new ArrayList<>();
            List<Double> originalCol = data.get(i);

            for (int j = 0; j < sampleSize; j++) {
                double originalValue = originalCol.get(j);
                double adjustment = 0.0;

                // 基于其他项目的平均值进行调整
                for (int k = 0; k < itemCount; k++) {
                    if (k != i) {
                        adjustment += data.get(k).get(j);
                    }
                }
                adjustment /= (itemCount - 1);

                // 向平均值方向调整，增加一致性
                double adjustedValue = originalValue + 0.1 * (adjustment - originalValue);
                adjustedCol.add(adjustedValue);
            }
            result.add(adjustedCol);
        }

        return result;
    }

    /**
     * 降低数据信度的方法
     */
    private List<List<Double>> decreaseReliability(List<List<Double>> data, double targetAlpha) {
        List<List<Double>> result = new ArrayList<>();

        // 添加随机噪声来降低一致性
        for (List<Double> col : data) {
            List<Double> noisyCol = addNoise(col, 0.3);
            result.add(noisyCol);
        }

        return result;
    }

    /**
     * 调整两个变量之间的相关性
     */
    @Tool(description = "调整两个变量之间的相关系数到指定值，支持正相关、负相关和无相关")
    public String adjustDataForCorrelation(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "第一个变量的列索引（从1开始）") Integer column1,
            @ToolParam(description = "第二个变量的列索引（从1开始）") Integer column2,
            @ToolParam(description = "目标相关系数（-1.0到1.0）") Double targetCorrelation,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[相关性调整] 开始调整变量相关性，sessionId={}, col1={}, col2={}, target={}",
                sessionId, column1, column2, targetCorrelation);

        try {
            // 参数验证
            if (targetCorrelation == null || targetCorrelation < -1.0 || targetCorrelation > 1.0) {
                throw new IllegalArgumentException("目标相关系数必须在-1.0到1.0之间，当前值：" + targetCorrelation);
            }

            if (tolerance == null) tolerance = 0.05;
            if (tolerance <= 0 || tolerance > 1) {
                throw new IllegalArgumentException("容差必须在0到1之间，当前值：" + tolerance);
            }

            if (column1 == null || column1 < 1) {
                throw new IllegalArgumentException("第一个变量的列索引必须大于0，当前值：" + column1);
            }
            if (column2 == null || column2 < 1) {
                throw new IllegalArgumentException("第二个变量的列索引必须大于0，当前值：" + column2);
            }
            if (column1.equals(column2)) {
                throw new IllegalArgumentException("两个变量不能是同一列");
            }

            // 获取原始数据
            List<Double> data1 = getNumericColumnData(sessionId, column1 - 1);
            List<Double> data2 = getNumericColumnData(sessionId, column2 - 1);

            if (data1.isEmpty() || data2.isEmpty()) {
                throw new RuntimeException("列数据为空或无有效数值");
            }

            if (data1.size() != data2.size()) {
                throw new RuntimeException("两列数据长度不一致");
            }

            // 计算当前相关系数 - 使用内部方法避免数据库保存
            double currentCorrelation = calculateCorrelationInternal(sessionId, column1, column2);
            log.info("[相关性调整] 当前相关系数: {}, 目标相关系数: {}", currentCorrelation, targetCorrelation);

            // 如果已经在目标范围内，无需调整
            if (Math.abs(currentCorrelation - targetCorrelation) <= tolerance) {
                return String.format("当前相关系数%.3f已在目标范围内，无需调整", currentCorrelation);
            }

            // 生成具有目标相关性的新数据
            List<Double> adjustedData2 = generateCorrelatedData(data1, targetCorrelation);

            // 标准化到原始数据的范围
            DescriptiveStatistics originalStats = new DescriptiveStatistics();
            data2.forEach(originalStats::addValue);
            double originalMean = originalStats.getMean();
            double originalStd = originalStats.getStandardDeviation();

            DescriptiveStatistics adjustedStats = new DescriptiveStatistics();
            adjustedData2.forEach(adjustedStats::addValue);
            double adjustedMean = adjustedStats.getMean();
            double adjustedStd = adjustedStats.getStandardDeviation();

            // 重新缩放到原始分布
            List<Double> finalData2 = adjustedData2.stream()
                    .map(value -> originalMean + (value - adjustedMean) * originalStd / adjustedStd)
                    .collect(Collectors.toList());

            // 验证调整后的相关系数
            double achievedCorrelation = calculateCorrelation(data1, finalData2);

            // 自动检测量表范围
            double minOption = data2.stream().mapToDouble(Double::doubleValue).min().orElse(1.0);
            double maxOption = data2.stream().mapToDouble(Double::doubleValue).max().orElse(5.0);

            // 生成变更记录
            List<List<Object>> changedCells = new ArrayList<>();
            for (int i = 0; i < data2.size(); i++) {
                if (!data2.get(i).equals(finalData2.get(i))) {
                    // 转换为适当的选项值（自动检测量表范围）
                    double adjustedValue = finalData2.get(i);
                    int optionValue = Math.max((int)minOption, Math.min((int)maxOption, (int) Math.round(adjustedValue)));

                    changedCells.add(Arrays.asList(
                        i + 1, // 行号（跳过表头）
                        column2 - 1, // 列号（0基索引）
                        String.valueOf(optionValue)
                    ));
                }
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("originalCorrelation", currentCorrelation);
            metrics.put("achievedCorrelation", achievedCorrelation);
            metrics.put("targetCorrelation", targetCorrelation);
            metrics.put("cellsChanged", changedCells.size());
            metrics.put("column1", column1); // 传递参数用于重新计算
            metrics.put("column2", column2);

            // 生成调整说明
            String explanation = String.format(
                "成功调整两个变量的相关系数从%.3f到%.3f（目标%.3f），共调整%d个单元格。",
                currentCorrelation, achievedCorrelation, targetCorrelation, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[相关性调整] 调整失败", e);
            return "相关性调整失败: " + e.getMessage();
        }
    }

    /**
     * 批量调整多个变量之间的相关性矩阵
     */
    @Tool(description = "批量调整多个变量之间的相关性，使其符合指定的相关性矩阵模式")
    public String adjustDataForCorrelationMatrix(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "需要调整的列索引列表（从1开始）") List<Integer> columns,
            @ToolParam(description = "目标相关性矩阵（上三角矩阵，按行优先顺序）") List<Double> targetCorrelations,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[相关性矩阵调整] 开始调整相关性矩阵，sessionId={}, columns={}", sessionId, columns);

        try {
            // 参数验证
            if (columns == null || columns.isEmpty()) {
                throw new IllegalArgumentException("列索引列表不能为空");
            }
            if (columns.size() < 2) {
                throw new IllegalArgumentException("至少需要2个变量才能计算相关性");
            }

            // 验证列索引
            for (Integer col : columns) {
                if (col == null || col < 1) {
                    throw new IllegalArgumentException("列索引必须大于0，当前值：" + col);
                }
            }

            if (targetCorrelations == null || targetCorrelations.isEmpty()) {
                throw new IllegalArgumentException("目标相关性列表不能为空");
            }

            // 验证相关系数范围
            for (Double corr : targetCorrelations) {
                if (corr == null || corr < -1.0 || corr > 1.0) {
                    throw new IllegalArgumentException("相关系数必须在-1.0到1.0之间，当前值：" + corr);
                }
            }

            if (tolerance == null) tolerance = 0.05;
            if (tolerance <= 0 || tolerance > 1) {
                throw new IllegalArgumentException("容差必须在0到1之间，当前值：" + tolerance);
            }

            int numVars = columns.size();
            int expectedCorrelations = numVars * (numVars - 1) / 2;

            if (targetCorrelations.size() != expectedCorrelations) {
                throw new IllegalArgumentException(
                    String.format("目标相关性数量不匹配，期望%d个，实际%d个", expectedCorrelations, targetCorrelations.size()));
            }

            // 获取原始数据
            List<List<Double>> originalData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                originalData.add(new ArrayList<>(data));
            }

            // 构建目标相关性矩阵
            double[][] targetMatrix = new double[numVars][numVars];
            int corrIndex = 0;
            for (int i = 0; i < numVars; i++) {
                targetMatrix[i][i] = 1.0; // 对角线为1
                for (int j = i + 1; j < numVars; j++) {
                    targetMatrix[i][j] = targetCorrelations.get(corrIndex);
                    targetMatrix[j][i] = targetCorrelations.get(corrIndex);
                    corrIndex++;
                }
            }

            // 使用Cholesky分解生成具有目标相关性的数据
            List<List<Double>> adjustedData = generateMultivariateCorrelatedData(originalData, targetMatrix);

            // 自动检测量表范围
            double minOption = originalData.stream()
                .flatMap(List::stream)
                .mapToDouble(Double::doubleValue)
                .min().orElse(1.0);
            double maxOption = originalData.stream()
                .flatMap(List::stream)
                .mapToDouble(Double::doubleValue)
                .max().orElse(5.0);

            // 生成变更记录
            List<List<Object>> changedCells = new ArrayList<>();
            for (int colIdx = 0; colIdx < columns.size(); colIdx++) {
                List<Double> originalCol = originalData.get(colIdx);
                List<Double> adjustedCol = adjustedData.get(colIdx);

                for (int rowIdx = 0; rowIdx < originalCol.size(); rowIdx++) {
                    if (!originalCol.get(rowIdx).equals(adjustedCol.get(rowIdx))) {
                        double adjustedValue = adjustedCol.get(rowIdx);
                        int optionValue = Math.max((int)minOption, Math.min((int)maxOption, (int) Math.round(adjustedValue)));

                        changedCells.add(Arrays.asList(
                            rowIdx + 1,
                            columns.get(colIdx) - 1,
                            String.valueOf(optionValue)
                        ));
                    }
                }
            }

            // 计算实际达到的相关性
            List<Double> achievedCorrelations = new ArrayList<>();
            corrIndex = 0;
            for (int i = 0; i < numVars; i++) {
                for (int j = i + 1; j < numVars; j++) {
                    double corr = calculateCorrelation(adjustedData.get(i), adjustedData.get(j));
                    achievedCorrelations.add(corr);
                    corrIndex++;
                }
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("targetCorrelations", targetCorrelations);
            metrics.put("achievedCorrelations", achievedCorrelations);
            metrics.put("cellsChanged", changedCells.size());

            // 生成调整说明
            String explanation = String.format(
                "成功调整%d个变量的相关性矩阵，共调整%d个单元格。调整后的数据更好地符合了预期的相关性模式。",
                numVars, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[相关性矩阵调整] 调整失败", e);
            return "相关性矩阵调整失败: " + e.getMessage();
        }
    }

    /**
     * 生成具有指定相关性矩阵的多元数据
     */
    private List<List<Double>> generateMultivariateCorrelatedData(List<List<Double>> originalData, double[][] correlationMatrix) {
        int numVars = originalData.size();
        int sampleSize = originalData.get(0).size();

        // 生成独立的标准正态分布数据
        List<List<Double>> independentData = new ArrayList<>();
        NormalDistribution normalDist = new NormalDistribution();

        for (int i = 0; i < numVars; i++) {
            List<Double> varData = new ArrayList<>();
            for (int j = 0; j < sampleSize; j++) {
                varData.add(normalDist.sample());
            }
            independentData.add(varData);
        }

        // 使用简化的Cholesky分解方法
        double[][] L = choleskyDecomposition(correlationMatrix);

        // 应用变换生成相关数据
        List<List<Double>> correlatedData = new ArrayList<>();
        for (int i = 0; i < numVars; i++) {
            List<Double> varData = new ArrayList<>();
            for (int j = 0; j < sampleSize; j++) {
                double value = 0.0;
                for (int k = 0; k <= i; k++) {
                    value += L[i][k] * independentData.get(k).get(j);
                }
                varData.add(value);
            }
            correlatedData.add(varData);
        }

        // 标准化到原始数据的分布
        List<List<Double>> result = new ArrayList<>();
        for (int i = 0; i < numVars; i++) {
            List<Double> originalCol = originalData.get(i);
            List<Double> correlatedCol = correlatedData.get(i);

            DescriptiveStatistics originalStats = new DescriptiveStatistics();
            originalCol.forEach(originalStats::addValue);

            DescriptiveStatistics correlatedStats = new DescriptiveStatistics();
            correlatedCol.forEach(correlatedStats::addValue);

            List<Double> scaledCol = correlatedCol.stream()
                    .map(value -> originalStats.getMean() +
                                 (value - correlatedStats.getMean()) *
                                 originalStats.getStandardDeviation() / correlatedStats.getStandardDeviation())
                    .collect(Collectors.toList());

            result.add(scaledCol);
        }

        return result;
    }

    /**
     * 简化的Cholesky分解
     */
    private double[][] choleskyDecomposition(double[][] matrix) {
        int n = matrix.length;
        double[][] L = new double[n][n];

        for (int i = 0; i < n; i++) {
            for (int j = 0; j <= i; j++) {
                if (i == j) {
                    double sum = 0.0;
                    for (int k = 0; k < j; k++) {
                        sum += L[j][k] * L[j][k];
                    }
                    L[j][j] = Math.sqrt(Math.max(matrix[j][j] - sum, 0.001)); // 避免负数
                } else {
                    double sum = 0.0;
                    for (int k = 0; k < j; k++) {
                        sum += L[i][k] * L[j][k];
                    }
                    L[i][j] = (matrix[i][j] - sum) / L[j][j];
                }
            }
        }

        return L;
    }

    /**
     * 保存调整后的Excel数据到数据库（专门用于逆向数据调整）
     * 修改逻辑：先创建等待AI填充的消息，然后返回分析结果给AI处理
     */
    public String saveAdjustedDataToMessage(String sessionId, List<List<Object>> changedCells, String explanation, Map<String, Object> metrics) {
        try {
            // 获取当前最新的Excel数据
            List<List<String>> currentExcelData = getExcelData(sessionId);
            if (currentExcelData == null || currentExcelData.isEmpty()) {
                log.error("[数据调整保存] 未找到当前Excel数据，sessionId={}", sessionId);
                return "未找到Excel数据";
            }

            // 应用所有变更到Excel数据
            List<List<String>> newExcelData = applyChangesToExcelData(currentExcelData, changedCells);

            // 构建数据修改记录 - 使用简单的数组格式 [[row, col, value], ...]
            // 根据AIConfig.java的说明，data_modifications应该是简单的数组格式
            // 同时只记录实际发生变化的单元格
            List<List<Object>> modifications = new ArrayList<>();
            int actualChangedCount = 0;

            for (List<Object> change : changedCells) {
                if (change.size() >= 3) {
                    int row = (Integer) change.get(0);
                    int col = (Integer) change.get(1);
                    String newValue = String.valueOf(change.get(2));

                    // 检查是否真的发生了变化
                    boolean actuallyChanged = false;
                    if (row >= 0 && row < currentExcelData.size() &&
                        col >= 0 && col < currentExcelData.get(row).size()) {
                        String oldValue = currentExcelData.get(row).get(col);
                        actuallyChanged = !newValue.equals(oldValue);
                        log.debug("[数据调整保存] 检查变更 row={}, col={}, oldValue='{}', newValue='{}', changed={}",
                                row, col, oldValue, newValue, actuallyChanged);
                    } else {
                        actuallyChanged = true; // 新增的单元格
                        log.debug("[数据调整保存] 新增单元格 row={}, col={}, newValue='{}'", row, col, newValue);
                    }

                    if (actuallyChanged) {
                        List<Object> mod = Arrays.asList(
                            change.get(0),  // row
                            change.get(1),  // col
                            change.get(2)   // value
                        );
                        modifications.add(mod);
                        actualChangedCount++;
                    }
                }
            }

            log.info("[数据调整保存] changedCells总数={}, 实际变更数={}", changedCells.size(), actualChangedCount);

            // 生成表格数据（统计摘要），使用实际变更数量
            List<FactorAnalysisOutput.TableData> tableDataList = generateAdjustmentSummaryTable(metrics, actualChangedCount);

            // 1. 查找已存在的等待AI填充的消息（AI聊天服务预先创建的）
            AiChatMessage waitingMsg = messageMapper.findLatestWaitingFillBySessionId(sessionId);

            if (waitingMsg != null) {
                // 更新已存在的等待填充消息，设置好所有数据字段
                String modificationsJson = objectMapper.writeValueAsString(modifications);
                log.info("[数据调整保存] 序列化modifications JSON长度={}, 前100字符={}",
                        modificationsJson.length(),
                        modificationsJson.length() > 100 ? modificationsJson.substring(0, 100) : modificationsJson);

                waitingMsg.setDataModifications(modificationsJson);
                waitingMsg.setCompleteExcelData(objectMapper.writeValueAsString(newExcelData));
                waitingMsg.setTableData(objectMapper.writeValueAsString(tableDataList));

                // 如果metrics中包含配置文本，则保存到数据库
                if (metrics.containsKey("configText")) {
                    String configText = (String) metrics.get("configText");
                    waitingMsg.setConfigText(configText);
                    log.info("[数据调整保存] 保存配置文本到等待消息，长度={}", configText != null ? configText.length() : 0);
                }

                log.info("[数据调整保存] 更新前 - 消息ID={}, dataModifications长度={}",
                        waitingMsg.getId(),
                        waitingMsg.getDataModifications() != null ? waitingMsg.getDataModifications().length() : 0);

                messageMapper.update(waitingMsg);

                // 验证更新后的数据
                AiChatMessage updatedMsg = messageMapper.findMessageById(waitingMsg.getId());
                log.info("[数据调整保存] 更新后验证 - 消息ID={}, dataModifications是否为空={}, 长度={}",
                        updatedMsg.getId(),
                        updatedMsg.getDataModifications() == null || updatedMsg.getDataModifications().trim().isEmpty(),
                        updatedMsg.getDataModifications() != null ? updatedMsg.getDataModifications().length() : 0);

                log.info("[数据调整保存] 更新等待AI填充的消息ID={}, 实际调整了{}个单元格", waitingMsg.getId(), actualChangedCount);

                // 数据已保存到数据库，现在基于数据库中的数据重新计算实际指标并更新消息
                explanation = recalculateMetricsAfterSave(sessionId, metrics, actualChangedCount, explanation);

                // 重新生成表格数据并更新消息
                List<FactorAnalysisOutput.TableData> updatedTableDataList = generateAdjustmentSummaryTable(metrics, actualChangedCount);
                waitingMsg.setTableData(objectMapper.writeValueAsString(updatedTableDataList));
                messageMapper.update(waitingMsg);

                log.info("[数据调整保存] 已更新表格数据，使用重新计算的实际指标");

            } else {
                // 如果没有找到等待填充的消息，创建新的（备用逻辑）
                String modificationsJson = objectMapper.writeValueAsString(modifications);
                log.info("[数据调整保存] 创建新消息 - 序列化modifications JSON长度={}", modificationsJson.length());

                waitingMsg = new AiChatMessage();
                waitingMsg.setSessionId(sessionId);
                waitingMsg.setRole("assistant");
                waitingMsg.setContent("等待AI填充");
                waitingMsg.setMessageOrder(messageMapper.getMaxMessageOrder(sessionId) + 1);
                waitingMsg.setDataModifications(modificationsJson);
                waitingMsg.setCompleteExcelData(objectMapper.writeValueAsString(newExcelData));
                waitingMsg.setTableData(objectMapper.writeValueAsString(tableDataList));

                // 如果metrics中包含配置文本，则保存到数据库
                if (metrics.containsKey("configText")) {
                    String configText = (String) metrics.get("configText");
                    waitingMsg.setConfigText(configText);
                    log.info("[数据调整保存] 保存配置文本到新消息，长度={}", configText != null ? configText.length() : 0);
                }

                waitingMsg.onCreate();
                messageMapper.insert(waitingMsg);

                // 验证插入后的数据
                AiChatMessage insertedMsg = messageMapper.findMessageById(waitingMsg.getId());
                log.info("[数据调整保存] 插入后验证 - 消息ID={}, dataModifications是否为空={}, 长度={}",
                        insertedMsg.getId(),
                        insertedMsg.getDataModifications() == null || insertedMsg.getDataModifications().trim().isEmpty(),
                        insertedMsg.getDataModifications() != null ? insertedMsg.getDataModifications().length() : 0);

                log.info("[数据调整保存] 创建新的等待AI填充消息ID={}, 实际调整了{}个单元格", waitingMsg.getId(), actualChangedCount);

                // 数据已保存到数据库，现在基于数据库中的数据重新计算实际指标并更新消息
                explanation = recalculateMetricsAfterSave(sessionId, metrics, actualChangedCount, explanation);

                // 重新生成表格数据并更新消息
                List<FactorAnalysisOutput.TableData> updatedTableDataList = generateAdjustmentSummaryTable(metrics, actualChangedCount);
                waitingMsg.setTableData(objectMapper.writeValueAsString(updatedTableDataList));
                messageMapper.update(waitingMsg);

                log.info("[数据调整保存] 已更新表格数据，使用重新计算的实际指标");
            }

            // 2. 返回分析结果给AI，让AI来生成最终的content并更新消息
            return explanation;

        } catch (Exception e) {
            log.error("[数据调整保存] 保存失败，sessionId={}", sessionId, e);
            return "保存调整后的数据失败: " + e.getMessage();
        }
    }

    /**
     * 兼容旧的方法签名
     */
    public String saveAdjustedDataToMessage(String sessionId, List<List<Object>> changedCells, String explanation) {
        Map<String, Object> emptyMetrics = new HashMap<>();
        emptyMetrics.put("cellsChanged", changedCells.size());
        return saveAdjustedDataToMessage(sessionId, changedCells, explanation, emptyMetrics);
    }

    /**
     * 应用变更到Excel数据
     */
    private List<List<String>> applyChangesToExcelData(List<List<String>> originalData, List<List<Object>> changes) {
        // 深拷贝原始数据
        List<List<String>> newData = new ArrayList<>();
        for (List<String> row : originalData) {
            newData.add(new ArrayList<>(row));
        }

        // 应用所有变更
        for (List<Object> change : changes) {
            if (change.size() >= 3) {
                int row = (Integer) change.get(0);
                int col = (Integer) change.get(1);
                String newValue = String.valueOf(change.get(2));

                // 确保行存在
                while (newData.size() <= row) {
                    newData.add(new ArrayList<>());
                }

                // 确保列存在
                List<String> targetRow = newData.get(row);
                while (targetRow.size() <= col) {
                    targetRow.add("");
                }

                // 应用变更
                targetRow.set(col, newValue);
            }
        }

        return newData;
    }

    /**
     * 基于给定的Excel数据计算Cronbach's Alpha系数
     */
    private double calculateCronbachAlphaFromData(List<List<String>> excelData, List<Integer> columns) {
        try {
            List<List<Double>> columnData = new ArrayList<>();

            // 跳过标题行，从第二行开始
            for (Integer col : columns) {
                List<Double> data = new ArrayList<>();
                for (int row = 1; row < excelData.size(); row++) {
                    if (col - 1 < excelData.get(row).size()) {
                        String cellValue = excelData.get(row).get(col - 1);
                        try {
                            double value = Double.parseDouble(cellValue);
                            data.add(value);
                        } catch (NumberFormatException e) {
                            // 跳过非数值数据
                        }
                    }
                }
                if (!data.isEmpty()) {
                    columnData.add(data);
                }
            }

            if (columnData.isEmpty() || columnData.size() < 2) {
                return 0.0;
            }

            return calculateCronbachAlpha(columnData);
        } catch (Exception e) {
            log.error("[基于数据计算信度] 计算失败", e);
            return 0.0;
        }
    }

    /**
     * 基于给定的Excel数据计算Cronbach's Alpha系数（考虑得分方向）
     * 用于多维度量表调整中的统计信息计算
     */
    private double calculateCronbachAlphaFromDataWithScoring(List<List<String>> excelData, List<Integer> columns,
                                                           List<String> scoringDirections) {
        try {
            List<List<Double>> columnData = new ArrayList<>();

            // 跳过标题行，从第二行开始
            for (int colIdx = 0; colIdx < columns.size(); colIdx++) {
                Integer col = columns.get(colIdx);
                String direction = scoringDirections != null && colIdx < scoringDirections.size() ?
                                 scoringDirections.get(colIdx) : "positive";

                List<Double> data = new ArrayList<>();
                for (int row = 1; row < excelData.size(); row++) {
                    if (col - 1 < excelData.get(row).size()) {
                        String cellValue = excelData.get(row).get(col - 1);
                        try {
                            double optionValue = Double.parseDouble(cellValue);

                            // 根据得分方向转换为得分值
                            double scoreValue;
                            if ("negative".equals(direction)) {
                                // 反向计分：1→5, 2→4, 3→3, 4→2, 5→1
                                scoreValue = 6.0 - optionValue;
                            } else {
                                // 正向计分：选项值就是得分值
                                scoreValue = optionValue;
                            }
                            data.add(scoreValue);
                        } catch (NumberFormatException e) {
                            // 跳过非数值数据
                        }
                    }
                }
                if (!data.isEmpty()) {
                    columnData.add(data);
                }
            }

            if (columnData.isEmpty() || columnData.size() < 2) {
                return 0.0;
            }

            return calculateCronbachAlpha(columnData);
        } catch (Exception e) {
            log.error("[基于数据计算信度（考虑得分方向）] 计算失败", e);
            return 0.0;
        }
    }

    /**
     * 基于给定的Excel数据计算两列的相关系数
     */
    private double calculateCorrelationFromData(List<List<String>> excelData, Integer col1, Integer col2) {
        try {
            List<Double> data1 = new ArrayList<>();
            List<Double> data2 = new ArrayList<>();

            // 跳过标题行，从第二行开始
            for (int row = 1; row < excelData.size(); row++) {
                if (col1 - 1 < excelData.get(row).size() && col2 - 1 < excelData.get(row).size()) {
                    try {
                        String value1 = excelData.get(row).get(col1 - 1);
                        String value2 = excelData.get(row).get(col2 - 1);

                        double d1 = Double.parseDouble(value1);
                        double d2 = Double.parseDouble(value2);

                        data1.add(d1);
                        data2.add(d2);
                    } catch (NumberFormatException e) {
                        // 跳过非数值数据
                    }
                }
            }

            if (data1.size() < 2 || data2.size() < 2 || data1.size() != data2.size()) {
                return 0.0;
            }

            return calculateCorrelation(data1, data2);
        } catch (Exception e) {
            log.error("[基于数据计算相关性] 计算失败", e);
            return 0.0;
        }
    }

    /**
     * 生成调整摘要表格数据
     */
    private List<FactorAnalysisOutput.TableData> generateAdjustmentSummaryTable(Map<String, Object> metrics, int changedCellsCount) {
        List<FactorAnalysisOutput.TableData> tableDataList = new ArrayList<>();

        try {
            // 检查是否是分维度量表调整，如果是则使用详细表格数据
            if (metrics.containsKey("isMultiDimensionalAdjustment") &&
                Boolean.TRUE.equals(metrics.get("isMultiDimensionalAdjustment"))) {

                FactorAnalysisOutput.TableData detailedTable = new FactorAnalysisOutput.TableData();
                detailedTable.setType("data_adjustment_summary");
                detailedTable.setTitle("数据调整摘要");

                // 使用详细的表格头部和数据
                @SuppressWarnings("unchecked")
                List<String> detailedHeaders = (List<String>) metrics.get("detailedTableHeaders");
                @SuppressWarnings("unchecked")
                List<List<Object>> detailedRows = (List<List<Object>>) metrics.get("detailedTableData");

                if (detailedHeaders != null && detailedRows != null) {
                    detailedTable.setHeaders(detailedHeaders);
                    detailedTable.setRows(detailedRows);
                    tableDataList.add(detailedTable);

                    log.info("[表格数据生成] 使用详细的分维度表格数据，行数={}", detailedRows.size());
                    return tableDataList;
                }
            }

            // 原有的简单表格逻辑
            FactorAnalysisOutput.TableData summaryTable = new FactorAnalysisOutput.TableData();
            summaryTable.setType("data_adjustment_summary");
            summaryTable.setTitle("数据调整摘要");

            // 创建表格头部
            List<String> headers = Arrays.asList("指标", "调整前", "调整后", "目标值", "变更数量");
            summaryTable.setHeaders(headers);

            // 创建表格数据行
            List<List<Object>> rows = new ArrayList<>();

            // 根据metrics中的数据生成行
            if (metrics.containsKey("originalAlpha") && metrics.containsKey("achievedAlpha")) {
                // 信度调整摘要
                List<Object> alphaRow = Arrays.asList(
                    "Cronbach's Alpha",
                    String.format("%.3f", (Double) metrics.get("originalAlpha")),
                    String.format("%.3f", (Double) metrics.get("achievedAlpha")),
                    String.format("%.3f", (Double) metrics.get("targetAlpha")),
                    String.valueOf(changedCellsCount)
                );
                rows.add(alphaRow);
            } else if (metrics.containsKey("originalCorrelation") && metrics.containsKey("achievedCorrelation")) {
                // 相关性调整摘要
                List<Object> corrRow = Arrays.asList(
                    "相关系数",
                    String.format("%.3f", (Double) metrics.get("originalCorrelation")),
                    String.format("%.3f", (Double) metrics.get("achievedCorrelation")),
                    String.format("%.3f", (Double) metrics.get("targetCorrelation")),
                    String.valueOf(changedCellsCount)
                );
                rows.add(corrRow);
            } else if (metrics.containsKey("originalRSquared") && metrics.containsKey("achievedRSquared")) {
                // 回归调整摘要
                List<Object> rSquaredRow = Arrays.asList(
                    "R²值",
                    String.format("%.3f", (Double) metrics.get("originalRSquared")),
                    String.format("%.3f", (Double) metrics.get("achievedRSquared")),
                    String.format("%.3f", (Double) metrics.get("targetRSquared")),
                    String.valueOf(changedCellsCount)
                );
                rows.add(rSquaredRow);
            } else {
                // 通用调整摘要
                List<Object> generalRow = Arrays.asList(
                    "数据调整",
                    "已调整",
                    "完成",
                    "达到目标",
                    String.valueOf(changedCellsCount)
                );
                rows.add(generalRow);
            }

            summaryTable.setRows(rows);
            tableDataList.add(summaryTable);

        } catch (Exception e) {
            log.error("[生成调整摘要表格] 失败", e);
            // 如果生成失败，创建一个简单的摘要表格
            FactorAnalysisOutput.TableData simpleTable = new FactorAnalysisOutput.TableData();
            simpleTable.setType("data_adjustment_simple");
            simpleTable.setTitle("数据调整完成");
            simpleTable.setHeaders(Arrays.asList("项目", "值"));
            List<List<Object>> simpleRows = Arrays.asList(
                Arrays.asList("调整单元格数量", String.valueOf(changedCellsCount)),
                Arrays.asList("调整状态", "完成")
            );
            simpleTable.setRows(simpleRows);
            tableDataList.add(simpleTable);
        }

        return tableDataList;
    }

    /**
     * 基于Excel数据计算KMO值
     */
    private double calculateKMOFromExcelData(List<List<String>> excelData, List<Integer> columns) {
        try {
            // 提取数值数据
            List<List<Double>> columnData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = new ArrayList<>();
                // 跳过标题行，从第二行开始
                for (int row = 1; row < excelData.size(); row++) {
                    if (col - 1 < excelData.get(row).size()) {
                        String cellValue = excelData.get(row).get(col - 1);
                        try {
                            double value = Double.parseDouble(cellValue);
                            data.add(value);
                        } catch (NumberFormatException e) {
                            // 跳过非数值数据
                        }
                    }
                }
                if (!data.isEmpty()) {
                    columnData.add(data);
                }
            }

            if (columnData.size() < 2) {
                log.warn("[KMO计算] 需要至少2个变量才能计算KMO");
                return 0.0;
            }

            // 直接基于提取的数据计算KMO
            return calculateKMOFromColumnData(columnData);

        } catch (Exception e) {
            log.error("[KMO计算] 基于Excel数据计算失败", e);
            return 0.0;
        }
    }

    /**
     * 基于Excel数据计算KMO值（考虑得分方向）
     * 用于多维度量表调整中的统计信息计算
     */
    private double calculateKMOFromExcelDataWithScoring(List<List<String>> excelData, List<Integer> allColumns,
                                                       List<List<String>> scoringDirections, List<List<Integer>> dimensions) {
        try {
            // 提取数值数据，考虑得分方向
            List<List<Double>> columnData = new ArrayList<>();

            // 构建列号到得分方向的映射
            Map<Integer, String> columnScoringMap = new HashMap<>();
            if (scoringDirections != null && dimensions != null) {
                for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                    List<Integer> dimension = dimensions.get(dimIdx);
                    List<String> dimDirections = dimIdx < scoringDirections.size() ? scoringDirections.get(dimIdx) : null;

                    for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                        Integer col = dimension.get(itemIdx);
                        String direction = dimDirections != null && itemIdx < dimDirections.size() ?
                                         dimDirections.get(itemIdx) : "positive";
                        columnScoringMap.put(col, direction);
                    }
                }
            }

            // 跳过标题行，从第二行开始
            for (Integer col : allColumns) {
                String direction = columnScoringMap.getOrDefault(col, "positive");
                List<Double> data = new ArrayList<>();

                for (int row = 1; row < excelData.size(); row++) {
                    if (col - 1 < excelData.get(row).size()) {
                        String cellValue = excelData.get(row).get(col - 1);
                        try {
                            double optionValue = Double.parseDouble(cellValue);

                            // 根据得分方向转换为得分值
                            double scoreValue;
                            if ("negative".equals(direction)) {
                                // 反向计分：1→5, 2→4, 3→3, 4→2, 5→1
                                scoreValue = 6.0 - optionValue;
                            } else {
                                // 正向计分：选项值就是得分值
                                scoreValue = optionValue;
                            }
                            data.add(scoreValue);
                        } catch (NumberFormatException e) {
                            // 跳过非数值数据
                        }
                    }
                }
                if (!data.isEmpty()) {
                    columnData.add(data);
                }
            }

            if (columnData.size() < 2) {
                log.warn("[KMO计算（考虑得分方向）] 需要至少2个变量才能计算KMO");
                return 0.0;
            }

            // 直接基于提取的数据计算KMO
            return calculateKMOFromColumnData(columnData);

        } catch (Exception e) {
            log.error("[KMO计算（考虑得分方向）] 基于Excel数据计算失败", e);
            return 0.0;
        }
    }

    /**
     * 基于列数据直接计算KMO值 - 使用与AdjustDataTools.java相同的标准算法
     */
    private double calculateKMOFromColumnData(List<List<Double>> columnData) {
        try {
            int numVars = columnData.size();
            if (numVars < 2) return 0.0;

            // 标准化数据
            List<List<Double>> standardizedData = standardizeDataForKMO(columnData);

            // 计算相关矩阵 - 使用Apache Commons Math
            RealMatrix correlationMatrix = calculateCorrelationMatrixForKMO(standardizedData);

            // 使用标准KMO算法计算
            double kmoValue = calculateStandardKMO(correlationMatrix);
            log.info("[KMO计算] 使用标准算法计算KMO值: {}", kmoValue);
            return kmoValue;

        } catch (Exception e) {
            log.error("[KMO计算] 基于列数据计算失败", e);
            return 0.0;
        }
    }

    /**
     * 数据保存后重新计算实际指标
     */
    private String recalculateMetricsAfterSave(String sessionId, Map<String, Object> metrics, int actualChangedCount, String originalExplanation) {
        try {
            // 从数据库中获取最新的Excel数据
            List<List<String>> latestExcelData = getExcelData(sessionId);
            if (latestExcelData == null || latestExcelData.isEmpty()) {
                log.warn("[指标重新计算] 无法获取最新的Excel数据");
                return originalExplanation;
            }

            String updatedExplanation = originalExplanation;

            // 处理不同类型的调整
            if (metrics.containsKey("originalAlpha") && metrics.containsKey("targetAlpha")) {
                // 这是信度调整，基于数据库数据重新计算实际的Alpha值
                @SuppressWarnings("unchecked")
                List<Integer> columns = (List<Integer>) metrics.get("columns");
                if (columns != null) {
                    double actualAlpha = calculateCronbachAlphaFromData(latestExcelData, columns);
                    metrics.put("achievedAlpha", actualAlpha);

                    // 更新说明文本
                    double originalAlpha = (Double) metrics.get("originalAlpha");
                    double targetAlpha = (Double) metrics.get("targetAlpha");
                    updatedExplanation = String.format(
                        "成功调整数据信度系数从%.3f到%.3f（目标%.3f），共调整%d个单元格。调整后的数据更好地满足了信度要求。",
                        originalAlpha, actualAlpha, targetAlpha, actualChangedCount
                    );
                }
            } else if (metrics.containsKey("originalCorrelation") && metrics.containsKey("targetCorrelation")) {
                // 这是相关性调整，基于数据库数据重新计算实际的相关系数
                Integer column1 = (Integer) metrics.get("column1");
                Integer column2 = (Integer) metrics.get("column2");
                if (column1 != null && column2 != null) {
                    double actualCorrelation = calculateCorrelationFromData(latestExcelData, column1, column2);
                    metrics.put("achievedCorrelation", actualCorrelation);

                    // 更新说明文本
                    double originalCorrelation = (Double) metrics.get("originalCorrelation");
                    double targetCorrelation = (Double) metrics.get("targetCorrelation");
                    updatedExplanation = String.format(
                        "成功调整两个变量的相关系数从%.3f到%.3f（目标%.3f），共调整%d个单元格。",
                        originalCorrelation, actualCorrelation, targetCorrelation, actualChangedCount
                    );
                }
            } else if (metrics.containsKey("isMultiDimensionalAdjustment") && (Boolean) metrics.get("isMultiDimensionalAdjustment")) {
                // 这是多维度量表调整，需要重新计算各维度信度、总量表信度和KMO值
                updatedExplanation = recalculateMultiDimensionalMetrics(sessionId, latestExcelData, metrics, actualChangedCount);
            }

            return updatedExplanation;

        } catch (Exception e) {
            log.error("[指标重新计算] 重新计算失败", e);
            return originalExplanation;
        }
    }

    /**
     * 重新计算多维度量表的指标
     */
    private String recalculateMultiDimensionalMetrics(String sessionId, List<List<String>> latestExcelData,
                                                     Map<String, Object> metrics, int actualChangedCount) {
        try {
            @SuppressWarnings("unchecked")
            List<List<Integer>> dimensions = (List<List<Integer>>) metrics.get("dimensions");
            @SuppressWarnings("unchecked")
            List<Integer> allColumns = (List<Integer>) metrics.get("allColumns");
            @SuppressWarnings("unchecked")
            List<Double> targetDimensionAlphas = (List<Double>) metrics.get("targetDimensionAlphas");
            Double targetTotalAlpha = (Double) metrics.get("targetTotalAlpha");
            Double targetKMO = (Double) metrics.get("targetKMO");
            @SuppressWarnings("unchecked")
            List<Double> originalDimensionAlphas = (List<Double>) metrics.get("originalDimensionAlphas");
            Double originalTotalAlpha = (Double) metrics.get("originalTotalAlpha");
            Double originalKMO = (Double) metrics.get("originalKMO");
            @SuppressWarnings("unchecked")
            List<List<String>> scoringDirections = (List<List<String>>) metrics.get("scoringDirections");

            if (dimensions == null || allColumns == null) {
                return "多维度量表调整完成，共调整" + actualChangedCount + "个单元格。";
            }

            // 重新计算各维度的实际信度（基于数据库中的最新数据，考虑得分方向）
            List<Double> actualDimensionAlphas = new ArrayList<>();
            for (int i = 0; i < dimensions.size(); i++) {
                List<Integer> dimension = dimensions.get(i);
                List<String> dimScoringDirections = scoringDirections != null && i < scoringDirections.size() ?
                                                   scoringDirections.get(i) : null;
                double actualAlpha = calculateCronbachAlphaFromDataWithScoring(latestExcelData, dimension, dimScoringDirections);
                actualDimensionAlphas.add(actualAlpha);
                log.info("[多维度指标重新计算] 维度{}实际信度: {}", i + 1, actualAlpha);
            }
            metrics.put("adjustedDimensionAlphas", actualDimensionAlphas);

            // 重新计算总量表的实际信度（考虑得分方向）
            Double actualTotalAlpha = null;
            if (targetTotalAlpha != null) {
                // 构建所有题目的得分方向列表
                List<String> allScoringDirections = new ArrayList<>();
                if (scoringDirections != null) {
                    for (List<String> dimDirections : scoringDirections) {
                        allScoringDirections.addAll(dimDirections);
                    }
                }
                actualTotalAlpha = calculateCronbachAlphaFromDataWithScoring(latestExcelData, allColumns, allScoringDirections);
                metrics.put("adjustedTotalAlpha", actualTotalAlpha);
                log.info("[多维度指标重新计算] 总量表实际信度: {}", actualTotalAlpha);
            }

            // 重新计算实际的KMO值（考虑得分方向）
            Double actualKMO = null;
            if (targetKMO != null) {
                actualKMO = calculateKMOFromExcelDataWithScoring(latestExcelData, allColumns, scoringDirections, dimensions);
                metrics.put("adjustedKMO", actualKMO);
                log.info("[多维度指标重新计算] 实际KMO值: {}", actualKMO);
            }

            // 生成更新后的调整说明 - 只包含表格中存在的数据
            StringBuilder sb = new StringBuilder();
            sb.append("成功完成分维度量表的信度效度调整：\n\n");

            // 各维度信度调整结果 - 只显示调整后的值、目标值、距离目标值和状态
            sb.append("【维度信度调整结果】\n");
            for (int i = 0; i < dimensions.size(); i++) {
                List<Integer> dimension = dimensions.get(i);
                double actualAlpha = actualDimensionAlphas.get(i);
                double targetAlpha = targetDimensionAlphas.get(i);
                double distanceToTarget = actualAlpha - targetAlpha;
                String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
                String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

                sb.append(String.format("维度%d（题目%s）：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                    i + 1, dimension.toString(), actualAlpha, targetAlpha, distanceStr, status));
            }

            // 总量表信度调整结果
            if (targetTotalAlpha != null && actualTotalAlpha != null) {
                double distanceToTarget = actualTotalAlpha - targetTotalAlpha;
                String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
                String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";
                sb.append(String.format("\n【总量表信度】：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                    actualTotalAlpha, targetTotalAlpha, distanceStr, status));
            }

            // KMO值调整结果（效度指标）
            if (targetKMO != null && actualKMO != null) {
                double distanceToTarget = actualKMO - targetKMO;
                String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
                String status = Math.abs(distanceToTarget) <= 0.05 ? "✓达标" : "需优化";
                sb.append(String.format("\n【效度指标KMO】：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                    actualKMO, targetKMO, distanceStr, status));
            }

            // 调整统计信息 - 只显示表格中存在的统计数据
            sb.append(String.format("\n【调整统计】\n"));
            sb.append(String.format("- 调整单元格数量：%d个\n", actualChangedCount));
            sb.append(String.format("- 涉及题目数量：%d个\n", allColumns.size()));
            sb.append(String.format("- 维度数量：%d个\n", dimensions.size()));

            // 质量评估 - 只显示表格中存在的达标比例
            sb.append("\n【质量评估】\n");
            int passedDimensions = 0;
            for (int i = 0; i < actualDimensionAlphas.size(); i++) {
                if (Math.abs(actualDimensionAlphas.get(i) - targetDimensionAlphas.get(i)) <= 0.02) {
                    passedDimensions++;
                }
            }
            sb.append(String.format("- 达标维度比例：%d/%d\n", passedDimensions, dimensions.size()));

            sb.append("\n调整完成，数据已按照目标要求进行优化。");

            // 更新detailedTableData中的实际指标值
            updateDetailedTableDataWithActualMetrics(metrics, dimensions, originalDimensionAlphas, actualDimensionAlphas,
                                                    targetDimensionAlphas, originalTotalAlpha, actualTotalAlpha,
                                                    targetTotalAlpha, originalKMO, actualKMO, targetKMO, actualChangedCount);

            return sb.toString();

        } catch (Exception e) {
            log.error("[多维度指标重新计算] 计算失败", e);
            return "多维度量表调整完成，共调整" + actualChangedCount + "个单元格。";
        }
    }

    /**
     * 更新detailedTableData中的实际指标值
     */
    private void updateDetailedTableDataWithActualMetrics(Map<String, Object> metrics,
                                                         List<List<Integer>> dimensions,
                                                         List<Double> originalDimensionAlphas,
                                                         List<Double> actualDimensionAlphas,
                                                         List<Double> targetDimensionAlphas,
                                                         Double originalTotalAlpha,
                                                         Double actualTotalAlpha,
                                                         Double targetTotalAlpha,
                                                         Double originalKMO,
                                                         Double actualKMO,
                                                         Double targetKMO,
                                                         int actualChangedCount) {
        try {
            @SuppressWarnings("unchecked")
            List<List<Object>> detailedTableData = (List<List<Object>>) metrics.get("detailedTableData");

            if (detailedTableData == null) {
                log.warn("[表格数据更新] detailedTableData为空，无法更新实际指标");
                return;
            }

            // 更新维度信度行的实际值
            for (int i = 0; i < detailedTableData.size(); i++) {
                List<Object> row = detailedTableData.get(i);
                if (row.size() >= 5) {
                    String indicator = (String) row.get(0);

                    // 更新维度信度行
                    for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                        List<Integer> dimension = dimensions.get(dimIdx);
                        String expectedIndicator = String.format("维度%d信度（题目%s）", dimIdx + 1, dimension.toString());

                        if (indicator.equals(expectedIndicator)) {
                            double actualAlpha = actualDimensionAlphas.get(dimIdx);
                            double targetAlpha = targetDimensionAlphas.get(dimIdx);
                            double distanceToTarget = actualAlpha - targetAlpha;
                            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
                            String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

                            row.set(1, String.format("%.3f", actualAlpha));
                            row.set(2, String.format("%.3f", targetAlpha));
                            row.set(3, distanceStr);
                            row.set(4, status);
                            break;
                        }
                    }

                    // 更新总量表信度行
                    if (indicator.equals("总量表信度（全部题目）") && actualTotalAlpha != null && targetTotalAlpha != null) {
                        double distanceToTarget = actualTotalAlpha - targetTotalAlpha;
                        String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
                        String totalStatus = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

                        row.set(1, String.format("%.3f", actualTotalAlpha));
                        row.set(2, String.format("%.3f", targetTotalAlpha));
                        row.set(3, distanceStr);
                        row.set(4, totalStatus);
                    }

                    // 更新KMO行
                    if (indicator.equals("KMO效度指标") && actualKMO != null && targetKMO != null) {
                        double distanceToTarget = actualKMO - targetKMO;
                        String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
                        String kmoStatus = Math.abs(distanceToTarget) <= 0.05 ? "✓达标" : "需优化";

                        row.set(1, String.format("%.3f", actualKMO));
                        row.set(2, String.format("%.3f", targetKMO));
                        row.set(3, distanceStr);
                        row.set(4, kmoStatus);
                    }

                    // 更新调整单元格数量行
                    if (indicator.equals("调整单元格数量")) {
                        row.set(4, actualChangedCount + "个");
                    }
                }
            }

            log.info("[表格数据更新] 已使用实际计算的指标更新detailedTableData，actualChangedCount={}", actualChangedCount);

        } catch (Exception e) {
            log.error("[表格数据更新] 更新detailedTableData失败", e);
        }
    }

    /**
     * 更新多维度表格数据，使用实际计算的指标
     */
    private void updateMultiDimensionalTableData(String sessionId, Map<String, Object> metrics,
                                                List<List<Integer>> dimensions,
                                                List<Double> originalDimensionAlphas,
                                                List<Double> actualDimensionAlphas,
                                                List<Double> targetDimensionAlphas,
                                                Double originalTotalAlpha,
                                                Double actualTotalAlpha,
                                                Double targetTotalAlpha,
                                                Double originalKMO,
                                                Double actualKMO,
                                                Double targetKMO,
                                                int actualChangedCount) {
        try {
            // 构建详细的表格数据 - 使用实际计算的指标
            List<List<Object>> summaryTableData = new ArrayList<>();

            // 添加各维度信度行
            for (int i = 0; i < dimensions.size(); i++) {
                List<Integer> dimension = dimensions.get(i);
                double originalAlpha = originalDimensionAlphas.get(i);
                double actualAlpha = actualDimensionAlphas.get(i);
                double targetAlpha = targetDimensionAlphas.get(i);
                double improvement = actualAlpha - originalAlpha;
                String improvementStr = improvement >= 0 ? String.format("+%.3f", improvement) : String.format("%.3f", improvement);
                String status = Math.abs(actualAlpha - targetAlpha) <= 0.02 ? "✓达标" : "接近目标";

                summaryTableData.add(Arrays.asList(
                    String.format("维度%d信度（题目%s）", i + 1, dimension.toString()),
                    String.format("%.3f", originalAlpha),
                    String.format("%.3f", actualAlpha),
                    String.format("%.3f", targetAlpha),
                    improvementStr,
                    status
                ));
            }

            // 获取得分方向和目标均值信息
            @SuppressWarnings("unchecked")
            List<List<String>> scoringDirections = (List<List<String>>) metrics.get("scoringDirections");
            @SuppressWarnings("unchecked")
            List<List<Double>> targetItemMeans = (List<List<Double>>) metrics.get("targetItemMeans");

            // 添加得分方向设置信息（如果有的话）
            if (scoringDirections != null && !scoringDirections.isEmpty()) {
                for (int i = 0; i < dimensions.size(); i++) {
                    List<Integer> dimension = dimensions.get(i);
                    List<String> dimDirections = scoringDirections.get(i);

                    // 转换为中文显示
                    String directionInfo = dimDirections.stream()
                        .map(dir -> "positive".equals(dir) ? "正向" : "反向")
                        .collect(java.util.stream.Collectors.joining(","));

                    summaryTableData.add(Arrays.asList(
                        String.format("维度%d得分方向（题目%s）", i + 1, dimension.toString()),
                        "-",
                        directionInfo,
                        "-",
                        "-",
                        "用户设置"
                    ));
                }
            }

            // 注意：题目均值统计信息已经在ReverseDataAdjustmentToolsAdvanced.buildSummaryMetrics中正确计算
            // 这里不再重新计算，避免使用数据库中反转后的数据导致统计错误
            // 如果需要显示题目均值信息，应该使用已经传递过来的detailedTableData

            // 添加总量表信度行
            if (originalTotalAlpha != null && actualTotalAlpha != null && targetTotalAlpha != null) {
                double totalImprovement = actualTotalAlpha - originalTotalAlpha;
                String totalImprovementStr = totalImprovement >= 0 ? String.format("+%.3f", totalImprovement) : String.format("%.3f", totalImprovement);
                String totalStatus = Math.abs(actualTotalAlpha - targetTotalAlpha) <= 0.02 ? "✓达标" : "接近目标";

                summaryTableData.add(Arrays.asList(
                    "总量表信度（全部题目）",
                    String.format("%.3f", originalTotalAlpha),
                    String.format("%.3f", actualTotalAlpha),
                    String.format("%.3f", targetTotalAlpha),
                    totalImprovementStr,
                    totalStatus
                ));
            }

            // 添加KMO行
            if (originalKMO != null && actualKMO != null && targetKMO != null) {
                double kmoImprovement = actualKMO - originalKMO;
                String kmoImprovementStr = kmoImprovement >= 0 ? String.format("+%.3f", kmoImprovement) : String.format("%.3f", kmoImprovement);
                String kmoStatus = Math.abs(actualKMO - targetKMO) <= 0.05 ? "✓达标" : "接近目标";

                summaryTableData.add(Arrays.asList(
                    "KMO效度指标",
                    String.format("%.3f", originalKMO),
                    String.format("%.3f", actualKMO),
                    String.format("%.3f", targetKMO),
                    kmoImprovementStr,
                    kmoStatus
                ));
            }

            // 添加调整统计信息行
            summaryTableData.add(Arrays.asList(
                "调整单元格数量",
                "-",
                "-",
                "-",
                actualChangedCount + "个"
            ));

            // 更新metrics中的基础表格数据（不包含题目均值信息）
            List<String> summaryTableHeaders = Arrays.asList("指标", "调整后", "目标值", "距离目标值", "状态");
            metrics.put("summaryTableData", summaryTableData);
            metrics.put("summaryTableHeaders", summaryTableHeaders);

            // 注意：不要覆盖detailedTableData和detailedTableHeaders
            // 这些数据已经在ReverseDataAdjustmentToolsAdvanced.buildSummaryMetrics中正确计算
            // 包含了基于正确得分方向的题目均值信息

            log.info("[表格数据更新] 已使用实际计算的指标更新基础表格数据（保留详细表格数据）");

        } catch (Exception e) {
            log.error("[表格数据更新] 更新失败", e);
        }
    }

    /**
     * 标准化数据用于KMO计算
     */
    private List<List<Double>> standardizeDataForKMO(List<List<Double>> columnData) {
        List<List<Double>> standardizedData = new ArrayList<>();

        for (List<Double> column : columnData) {
            DescriptiveStatistics stats = new DescriptiveStatistics();
            column.forEach(stats::addValue);

            double mean = stats.getMean();
            double std = stats.getStandardDeviation();

            List<Double> standardizedColumn = new ArrayList<>();
            for (Double value : column) {
                if (std == 0) {
                    standardizedColumn.add(0.0);
                } else {
                    standardizedColumn.add((value - mean) / std);
                }
            }
            standardizedData.add(standardizedColumn);
        }

        return standardizedData;
    }

    /**
     * 计算相关矩阵用于KMO计算
     */
    private RealMatrix calculateCorrelationMatrixForKMO(List<List<Double>> standardizedData) {
        int numVars = standardizedData.size();
        int sampleSize = standardizedData.get(0).size();

        // 转换为二维数组
        double[][] dataArray = new double[sampleSize][numVars];
        for (int i = 0; i < sampleSize; i++) {
            for (int j = 0; j < numVars; j++) {
                dataArray[i][j] = standardizedData.get(j).get(i);
            }
        }

        return new PearsonsCorrelation().computeCorrelationMatrix(dataArray);
    }

    /**
     * 使用标准算法计算KMO值 - 与AdjustDataTools.java完全相同
     */
    private double calculateStandardKMO(RealMatrix correlationMatrix) {
        int n = correlationMatrix.getRowDimension();
        RealMatrix inverseCorrelationMatrix;

        try {
            inverseCorrelationMatrix = new LUDecomposition(correlationMatrix).getSolver().getInverse();
        } catch (Exception e) {
            log.warn("[KMO计算] 无法计算相关矩阵的逆矩阵，可能存在共线性问题，KMO返回0.0");
            return 0.0;
        }

        double sumOfSquaredCorrelations = 0.0;
        double sumOfSquaredPartialCorrelations = 0.0;

        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i == j) continue;

                // 计算简单相关系数的平方和
                sumOfSquaredCorrelations += Math.pow(correlationMatrix.getEntry(i, j), 2);

                // 计算偏相关系数的平方和
                double partialCorrelation = -inverseCorrelationMatrix.getEntry(i, j) /
                        Math.sqrt(inverseCorrelationMatrix.getEntry(i, i) * inverseCorrelationMatrix.getEntry(j, j));
                sumOfSquaredPartialCorrelations += Math.pow(partialCorrelation, 2);
            }
        }

        // KMO = (sum_r^2) / (sum_r^2 + sum_u^2)
        double numerator = sumOfSquaredCorrelations;
        double denominator = sumOfSquaredCorrelations + sumOfSquaredPartialCorrelations;

        if (denominator == 0) {
            return 0.0;
        } else {
            return numerator / denominator;
        }
    }

    /**
     * 从Excel数据中计算指定列的得分均值（考虑得分方向）
     * 支持各种级数的量表（自动检测范围）
     */
    private double calculateItemScoreMeanFromExcel(List<List<String>> excelData, Integer col, String direction) {
        try {
            List<Double> options = new ArrayList<>();
            List<Double> scores = new ArrayList<>();

            // 跳过标题行，从第二行开始，先收集所有有效选项值
            for (int row = 1; row < excelData.size(); row++) {
                if (col - 1 < excelData.get(row).size()) {
                    String cellValue = excelData.get(row).get(col - 1);
                    try {
                        double option = Double.parseDouble(cellValue);
                        if (!Double.isNaN(option) && option > 0) {
                            options.add(option);
                        }
                    } catch (NumberFormatException e) {
                        // 跳过非数值数据
                    }
                }
            }

            if (options.isEmpty()) {
                return 0.0;
            }

            // 自动检测量表的范围
            double maxOption = options.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            double minOption = options.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);

            // 计算得分
            for (double option : options) {
                double score;
                if ("negative".equals(direction)) {
                    // 反向计分：选项值越小，得分越高
                    // 公式：得分 = (最大值 + 最小值) - 选项值
                    score = (maxOption + minOption) - option;
                } else {
                    // 正向计分：选项值就是得分值
                    score = option;
                }
                scores.add(score);
            }

            if (scores.isEmpty()) {
                return 0.0;
            }

            return scores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

        } catch (Exception e) {
            log.error("[题目均值计算] 计算失败，col={}, direction={}", col, direction, e);
            return 0.0;
        }
    }

}

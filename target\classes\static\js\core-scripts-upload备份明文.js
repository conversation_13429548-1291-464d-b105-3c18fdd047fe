let scriptContent = ''; // 用于存储上传的脚本内容

// 界面加载时的行为
window.onload = function () {
    try {
        document.getElementById('fill_time_start').value = 100;
        // 设置第二个输入框的默认值
        document.getElementById('fill_time_end').value = 200;
    } catch (e) {
        console.log("初始化填写时间失败");
    }
};

function initContentShow() {
    var i = $("#classicsCoverContent .coverTip .viewResultWrap"), n = $("#classicsCoverContent .coverTip").text();
    if (i[0]) window.location.href = i.find("a").attr("href");
    else if (window.coverTimeFinish) sessionStorage.skipfm = 1, window.location.href = window.location.href.replace(/#/gi, "");
    else if (n) return void layer.msg(n);
    divTip && ("none" != divTip.style.display || divTip.needShow) && (divTip.style.display = "", $("#tipHeight").show()),
        $("#divContent").show(), $("#pageDiv").show(),
        !isMobile ? ($("#divFengMian").hide(), $("html").scrollTop(0), $("#divPowerBy").show(), fminitshow(), fixBottom()) :
            ($("#coverShare").hide(), $("#slideChunk").hide(), $(divFengMian).animate({top: "-1000px"}, 300, "swing", function () {
                $("#divFengMian").hide(), $("html").scrollTop(0), $("#divPowerBy").show(), fminitshow(), fixBottom()
            }))
}

function setDefaultTimeRange() {
    var elementCount = document.querySelectorAll('.field.ui-field-contain').length;
    var defaultStartTime = elementCount * 5;
    var defaultEndTime = elementCount * 5 * 2;
    document.getElementById('fill_time_start').value = defaultStartTime;
    document.getElementById('fill_time_end').value = defaultEndTime;
}

// 验证填写用时范围
document.getElementById('fill_time_end').addEventListener('input', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;
    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        document.getElementById('error-message').style.display = 'block';
    } else {
        document.getElementById('error-message').style.display = 'none';
    }
});

document.getElementById('fill_time_end').addEventListener('blur', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;
    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        document.getElementById('fill_time_end').value = '';
    }
});

document.getElementById('fill_time_start').addEventListener('blur', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;
    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        document.getElementById('fill_time_start').value = '';
    }
});

// 拖放文件
const dropZone = document.getElementById('drop_zone');
const fileInput = document.getElementById('js_file');

dropZone.addEventListener('dragover', function (e) {
    e.preventDefault();
    dropZone.classList.add('active');
});

dropZone.addEventListener('dragleave', function () {
    dropZone.classList.remove('active');
});

dropZone.addEventListener('drop', function (e) {
    e.preventDefault();
    dropZone.classList.remove('active');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.name.endsWith('.js') || file.name.endsWith('.txt')) {
            fileInput.files = e.dataTransfer.files;
            readScript();
        } else {
            alert('只支持JS或TXT文件');
        }
    }
});

dropZone.addEventListener('click', function () {
    fileInput.click();
});
// 在文件选择后读取文件内容
fileInput.addEventListener('change', function () {
    if (fileInput.files.length > 0) {
        readScript(); // 调用文件读取函数
    }
});

function readScript() {
    const file = fileInput.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            const content = e.target.result;
            scriptContent = content; // 存储脚本内容

            // 检查内容中是否包含 window.is_complete_flag
            if (content.includes('window.is_complete_flag')) {
                const surveyLink = getWenjuanUrlFromContent(content);
                if (surveyLink) {
                    document.getElementById('survey_link').value = surveyLink;
                    document.getElementById('survey_link').disabled = false;
                    alert('识别脚本成功！');
                } else {
                    alert('未在脚本中找到问卷链接');
                }
            } else {
                alert('上传的脚本不是代码生成器生成的，请上传代码生成器生成的脚本！');
            }
        };
        reader.readAsText(file);
        document.getElementById('file_name_display').textContent = `已上传文件: ${file.name}`;
    }
}

function confirmScript() {
    const content = document.getElementById('js_text').value;
    scriptContent = content; // 存储脚本内容

    // 检查内容中是否包含 window.is_complete_flag
    if (content.includes('window.is_complete_flag')) {
        const surveyLink = getWenjuanUrlFromContent(content);
        if (surveyLink) {
            document.getElementById('survey_link').value = surveyLink;
            document.getElementById('survey_link').disabled = false;
            document.getElementById('js_text').disabled = true;
            alert('识别脚本成功！');
        } else {
            alert('未在脚本中找到问卷链接');
        }
    } else {
        alert('上传的脚本不是代码生成器生成的，请上传代码生成器生成的脚本！');
    }
}


function resetScript() {
    const jsText = document.getElementById('js_text');
    const surveyLink = document.getElementById('survey_link');
    jsText.disabled = false;
    jsText.value = '';
    surveyLink.value = '';
    surveyLink.disabled = true;
    scriptContent = ''; // 重置脚本内容
}


function toggleJSInput() {
    const jsFileDiv = document.getElementById('js_file_div');
    const jsTextDiv = document.getElementById('js_text_div');
    const jsText = document.getElementById('js_text');
    const surveyLink = document.getElementById('survey_link');
    const fileNameDisplay = document.getElementById('file_name_display');

    jsText.value = '';
    fileInput.value = '';
    surveyLink.value = '';
    fileNameDisplay.textContent = '';
    scriptContent = ''; // 重置脚本内容

    if (document.getElementById('js_text_option').checked) {
        jsFileDiv.style.display = 'none';
        jsTextDiv.style.display = 'block';
        jsText.disabled = false;
    } else {
        jsFileDiv.style.display = 'block';
        jsTextDiv.style.display = 'none';
        jsText.disabled = true;
    }
}


function getWenjuanUrlFromContent(content) {
    const lines = content.split('\n');
    for (let line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith("var wenjuan_url = ")) {
            const urlMatch = trimmedLine.match(/'(.*?)'/);
            if (urlMatch) {
                return urlMatch[1];
            }
        }
    }
    return null;
}

function updateRatio() {
    let mobileRatio = parseInt(document.getElementById('mobile_ratio').value);
    let linkRatio = parseInt(document.getElementById('link_ratio').value);
    let wechatRatio = 100 - mobileRatio - linkRatio;

    if (wechatRatio < 0) {
        document.getElementById('ratio_error').style.display = 'block';
    } else {
        document.getElementById('wechat_ratio').value = wechatRatio;
        document.getElementById('ratio_error').style.display = 'none';
    }

    document.getElementById('mobile_ratio_display').value = mobileRatio;
    document.getElementById('link_ratio_display').value = linkRatio;
    document.getElementById('wechat_ratio_display').value = wechatRatio;
}

; // 全局变量存储加载的数据
let cityToProvinceMap = {}; // 存储城市到省份的映射
// 声明全局布尔变量
let isCitySearchTriggered = false;
document.addEventListener('DOMContentLoaded', function () {
    // 获取当天日期标识（格式：YYYY-MM-DD）
    const getDayIdentifier = () => {
        const today = new Date();
        return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    };

    // 检查是否需要显示协议
    const shouldShowTerms = () => {
        const lastAgreedDayForJS = localStorage.getItem('lastAgreedDayForJS');
        const currentDay = getDayIdentifier();
        return !lastAgreedDayForJS || lastAgreedDayForJS !== currentDay;
    };

    if (shouldShowTerms()) {
        const termsModal = new bootstrap.Modal('#termsModal', {
            backdrop: 'static',
            keyboard: false
        });

        const agreeCheck = document.getElementById('agreeCheck');
        const confirmBtn = document.getElementById('confirmBtn');

        agreeCheck.addEventListener('change', function() {
            confirmBtn.disabled = !this.checked;
        });

        confirmBtn.addEventListener('click', function() {
            // 存储当天日期标识
            localStorage.setItem('lastAgreedDayForJS', getDayIdentifier());
            termsModal.hide();
            document.querySelector('.modal-backdrop').remove();
            document.body.style.overflow = 'auto';
        });

        termsModal.show();
    } else {
        // 确保移除可能残留的遮罩层
        const existingBackdrop = document.querySelector('.modal-backdrop');
        if (existingBackdrop) {
            existingBackdrop.remove();
            document.body.style.overflow = 'auto';
        }
    }


    // 动态加载 province_city_mapping.json 文件
    fetch('province_city_mapping.json')  // 确保使用正确的文件路径
        .then(response => response.json())
        .then(data => {
            loadedMappingData = data; // 将加载的数据存储在全局变量中
            createCityToProvinceMap(data); // 创建城市到省份的映射
            initializeProvinceCityMapping(loadedMappingData);  // 使用加载的数据初始化下拉菜单
        })
        .catch(error => {
            console.error('加载省市映射数据时出错:', error);
        });

    // 添加搜索输入框的事件监听器
    document.getElementById('province_search').addEventListener('input', function () {
        isCitySearchTriggered = false;
        filterDropdown('province', this.value, 'province_list_backup');
    });

    document.getElementById('city_search').addEventListener('input', function () {
        // 用户开始通过搜索框搜索城市时，把标志位设为 true
        isCitySearchTriggered = true;
        filterCityDropdown(this.value);
    });

    // 添加清空搜索按钮的事件监听器
    document.getElementById('clear_province_search').addEventListener('click', function () {
        isCitySearchTriggered = false;
        document.getElementById('province_search').value = '';
        filterDropdown('province', '', 'province_list_backup');
    });

    document.getElementById('clear_city_search').addEventListener('click', function () {
        isCitySearchTriggered = false;
        document.getElementById('city_search').value = '';
        populateCityDropdown(); // 恢复城市下拉框的所有选项
    });

    // 监听回车键触发搜索
    document.getElementById('province_search').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            filterDropdown('province', this.value, 'province_list_backup');
        }
    });

    document.getElementById('city_search').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            filterCityDropdown(this.value);
        }
    });
});

// 创建城市到省份的映射
function createCityToProvinceMap(mappingData) {
    cityToProvinceMap = {}; // 初始化
    for (const province in mappingData) {
        const cities = mappingData[province].cities;
        for (const city in cities) {
            cityToProvinceMap[city] = province;
        }
    }
}

// 初始化省份和城市下拉菜单
function initializeProvinceCityMapping(mappingData) {
    const provinceSelect = document.getElementById('province');

    // 清空现有选项
    provinceSelect.innerHTML = '';

    // 更新省份和城市映射关系
    for (const province in mappingData) {
        const option = document.createElement('option');
        option.value = province;
        option.textContent = province;
        provinceSelect.appendChild(option);
    }

    // 备份完整的省份列表用于搜索
    provinceSelect.dataset.fullOptions = JSON.stringify(
        Array.from(provinceSelect.options).map(option => ({ value: option.value, text: option.text }))
    );

    // 在初始化时调用 updateCityDropdown，并传入加载的数据
    populateCityDropdown();  // 初始化城市下拉框

    // 省份选择框的事件监听器，改变省份时更新城市列表
    provinceSelect.addEventListener('change', function () {
        updateCityDropdown(); // 使用已加载的数据更新城市下拉框
    });
}

// 更新城市下拉框，根据选中的省份或搜索结果
function updateCityDropdown() {
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    const selectedProvince = provinceSelect.value;

    isCitySearchTriggered = false;

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 使用选中的省份更新城市下拉列表
    if (selectedProvince && loadedMappingData[selectedProvince]) {
        const cities = loadedMappingData[selectedProvince].cities;

        // 遍历城市对象，创建下拉选项
        for (const city in cities) {
            const option = document.createElement('option');
            option.value = cities[city]; // 使用城市的代码作为值
            option.textContent = city; // 显示城市名称
            citySelect.appendChild(option);
        }

        // 备份完整的城市列表用于搜索
        citySelect.dataset.fullOptions = JSON.stringify(
            Array.from(citySelect.options).map(option => ({ value: option.value, text: option.text }))
        );

        // 自动选择第一个城市
        if (Object.keys(cities).length > 0) {
            citySelect.value = Object.values(cities)[0]; // 选择第一个城市
        }
    }
}

// 恢复城市下拉框的所有选项
function populateCityDropdown() {
    const citySelect = document.getElementById('city');
    const provinceSelect = document.getElementById('province');
    const selectedProvince = provinceSelect.value;

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 使用选中的省份更新城市下拉列表
    if (selectedProvince && loadedMappingData[selectedProvince]) {
        const cities = loadedMappingData[selectedProvince].cities;

        // 遍历城市对象，创建下拉选项
        for (const city in cities) {
            const option = document.createElement('option');
            option.value = cities[city]; // 使用城市的代码作为值
            option.textContent = city; // 显示城市名称
            citySelect.appendChild(option);
        }

        // 备份完整的城市列表用于搜索
        citySelect.dataset.fullOptions = JSON.stringify(
            Array.from(citySelect.options).map(option => ({ value: option.value, text: option.text }))
        );

        // 自动选择第一个城市
        if (Object.keys(cities).length > 0) {
            citySelect.value = Object.values(cities)[0]; // 选择第一个城市
        }
    }
}

// 过滤省份下拉选项的通用函数
function filterDropdown(selectId, searchTerm, backupKey) {
    const selectElement = document.getElementById(selectId);
    const fullOptions = JSON.parse(selectElement.dataset.fullOptions || '[]');

    // 清空当前选项
    selectElement.innerHTML = '';

    // 过滤选项
    const filteredOptions = fullOptions.filter(option =>
        option.text.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // 添加过滤后的选项
    filteredOptions.forEach(optionData => {
        const option = document.createElement('option');
        option.value = optionData.value;
        option.textContent = optionData.text;
        selectElement.appendChild(option);
    });

    // 如果是省份下拉框，更新城市下拉框
    if (selectId === 'province') {
        updateCityDropdown();
    }
}

// 过滤城市下拉选项的函数，支持全局搜索
function filterCityDropdown(searchTerm) {
    const citySelect = document.getElementById('city');
    const provinceSelect = document.getElementById('province');
    const citySearch = searchTerm.toLowerCase();

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 遍历所有城市，找到匹配的
    for (const province in loadedMappingData) {
        const cities = loadedMappingData[province].cities;
        for (const city in cities) {
            if (city.toLowerCase().includes(citySearch)) {
                const option = document.createElement('option');
                option.value = cities[city]; // 使用城市的代码作为值
                option.textContent = city; // 显示城市名称
                option.dataset.province = province; // 存储所属省份
                citySelect.appendChild(option);
            }
        }
    }

    // 如果有匹配的城市，自动选择第一个
    if (citySelect.options.length > 0) {
        citySelect.value = citySelect.options[0].value;
    }

    // ==== 在这里添加一段监听器逻辑 ====
    citySelect.onchange = function() {

        // 如果不是由“城市搜索”触发，就什么也不做
        if (!isCitySearchTriggered) {
            return;
        }

        if (this.selectedIndex >= 0) {
            // 1. 获取当前选中的城市选项
            const selectedOption = this.options[this.selectedIndex];
            // 2. 读取城市所属的省份
            const selectedProvince = selectedOption.dataset.province;

            // --- 关键操作：恢复省份下拉框为完整列表 ---
            const provinceSelect = document.getElementById('province');
            const fullOptions = JSON.parse(provinceSelect.dataset.fullOptions || '[]');

            // 先清空
            provinceSelect.innerHTML = '';
            // 再把完整列表添加回去
            fullOptions.forEach(optionData => {
                const option = document.createElement('option');
                option.value = optionData.value;
                option.textContent = optionData.text;
                provinceSelect.appendChild(option);
            });

            // 3. 设置当前城市对应的省份
            provinceSelect.value = selectedProvince;
        }
    };
}

// 切换城市选择功能
function toggleCitySelection() {
    const ipSelection = document.getElementById('ip_selection');
    ipSelection.style.display = document.getElementById('change_ip_yes').checked ? 'block' : 'none';

    // 如果选择“否”，则清空已添加城市列表
    if (!document.getElementById('change_ip_yes').checked) {
        const cityList = document.getElementById('city_list');
        cityList.innerHTML = ''; // 清空城市列表
        document.getElementById('added_city_section').style.display = 'none'; // 隐藏已添加城市部分
    }
}

// 添加城市功能
function addCity() {
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    const cityList = document.getElementById('city_list');
    const addedCitySection = document.getElementById('added_city_section');

    let selectedProvince = provinceSelect.value;
    let selectedCity = citySelect.options[citySelect.selectedIndex].text;

    // 如果当前是通过搜索添加的城市，获取其所属省份
    if (citySelect.options[citySelect.selectedIndex].dataset.province) {
        selectedProvince = citySelect.options[citySelect.selectedIndex].dataset.province;
    }

    const displayCity = selectedProvince + '-' + selectedCity;

    if (selectedCity) {
        const listItem = document.createElement('li');
        listItem.className = 'list-group-item d-flex justify-content-between align-items-center'; // 保证列表项的样式
        listItem.textContent = displayCity;

        // 创建删除按钮
        const deleteButton = document.createElement('button');
        deleteButton.className = 'btn btn-danger btn-sm';
        deleteButton.textContent = '删除';
        deleteButton.onclick = function () {
            cityList.removeChild(listItem); // 删除城市
            if (cityList.children.length === 0) {
                addedCitySection.style.display = 'none'; // 隐藏已添加城市部分
            }
        };

        listItem.appendChild(deleteButton);
        cityList.appendChild(listItem);
        addedCitySection.style.display = 'block'; // 显示已添加城市部分
    }
}


function goBack() {
    window.location.href = 'createOrderStep1'; // 跳转到指定页面
}


function calculateTotalCost() {
    // 获取是否换IP的选项
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value;

    // 获取目标份数
    const targetCount = parseInt(document.getElementById('target_count').value, 10);

    // 根据是否换IP确定单价
    const unitCost = changeIp === 'yes' ? 0.06 : 0.04;

    // 计算总费用
    let totalCost = targetCount * unitCost;

    // 应用最低消费 3 元
    if (totalCost < 3) {
        totalCost = 3; // 最低消费 3 元
    }
    return totalCost;
}

document.querySelector('.btn-submit').addEventListener('click', function (event) {
    event.preventDefault(); // 阻止默认提交行为

    // 收集表单数据
    const surveyLink = document.getElementById('survey_link').value;
    const targetCount = document.getElementById('target_count').value;
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    const mobileRatio = parseInt(document.getElementById('mobile_ratio_display').value, 10);
    const linkRatio = parseInt(document.getElementById('link_ratio_display').value, 10);
    const wechatRatio = parseInt(document.getElementById('wechat_ratio_display').value, 10);
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value;
    const fensanLevel = document.getElementById('fensan_level').value;
    const unitCost = changeIp === 'yes' ? 0.06 : 0.04;

    // 验证是否填写了目标份数
    if (!targetCount || parseInt(targetCount) < 10) {
        alert("目标份数必须大于等于10份");
        document.getElementById('target_count').focus();
        return;
    }

    // 验证填写的时间范围
    if (parseInt(fillTimeStart) >= parseInt(fillTimeEnd) || !fillTimeStart || !fillTimeEnd) {
        alert("请填写正确的用时范围");
        document.getElementById('fill_time_start').focus();
        return;
    }
    if (parseInt(fillTimeEnd) <= 5) {
        if (!confirm("检测到设置最大的填写用时小于等于5秒，注意设置的填写用时是以秒为单位，请确定是否继续操作？")) {
            return;
        }
    }

    // 验证提交来源比例
    if (isNaN(mobileRatio) || isNaN(linkRatio) || isNaN(wechatRatio) ||
        mobileRatio < 0 || linkRatio < 0 || wechatRatio < 0 ||
        mobileRatio + linkRatio + wechatRatio !== 100) {
        alert("提交来源比例必须是大于等于0的整数，且三者之和必须为100%");
        document.getElementById('mobile_ratio').focus();
        return;
    }
    // 验证是否上传了脚本文件或填写了脚本文本
    if (!scriptContent) {
        alert("请上传文件或填写脚本文本");
        if (document.getElementById('js_file_option').checked) {
            document.getElementById('js_file').focus(); // 如果选择了上传文件，聚焦到文件输入框
        } else {
            document.getElementById('js_text').focus(); // 如果选择了填写脚本，聚焦到脚本输入框
        }
        return;
    }

    // 获取已添加城市并去掉“删除”按钮的文字
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item')).map(item => item.textContent.replace('删除', '').trim());

    // 如果选择了换IP，验证是否添加了城市
    if (changeIp === 'yes' && addedCities.length === 0) {
        alert("请点击添加按钮，至少添加一个换IP区域");
        document.getElementById('province').focus();
        return;
    }

    // 如果选择了换IP，并且换IP城市超过 40 个城市
    if (addedCities.length > 40) {
        alert('换IP城市不能超过 40 个城市，请删除一点');
        document.getElementById('province').focus();
        return;
    }
    // 构建订单信息
    // 构建订单信息
    // 构建订单信息
    let orderInfo = `
    <table style="width: 100%; border-collapse: collapse; line-height: 2; padding: 4px;">
        <tr>
            <td class="label-cell">问卷链接:</td>
            <td class="value-cell">${surveyLink}</td>
        </tr>
        <tr>
            <td class="label-cell">目标份数:</td>
            <td class="value-cell">${targetCount}</td>
        </tr>
        <tr>
            <td class="label-cell">填写用时范围:</td>
            <td class="value-cell">${fillTimeStart} 秒 ~ ${fillTimeEnd} 秒</td>
        </tr>
        <tr>
            <td class="label-cell">提交来源比例:</td>
            <td class="value-cell">
                <span style="color: #007bff;">手机提交: ${mobileRatio}%</span>
                <span style="color: #007bff;">链接提交: ${linkRatio}%</span>
                <span style="color: #007bff;">微信提交: ${wechatRatio}%</span>
            </td>
        </tr>
        <tr>
            <td class="label-cell">分散级别:</td>
            <td class="value-cell">${fensanLevel}</td>
        </tr>
        <tr>
            <td class="label-cell">是否换IP:</td>
            <td class="value-cell" style="color: ${changeIp === 'yes' ? 'red' : 'green'};">${changeIp}</td>
        </tr>
    </table>
`;


    if (changeIp === 'yes' && addedCities.length > 0) {
        orderInfo += `
            <div style="font-size: 1.2rem; font-weight: bold; color: #007bff;">已添加地区:</div>
            <div style="margin-left: 10px; color: #333;">${addedCities.join(', ')}</div>
        `;
    }

    // 添加预计完成时间
    const fensanLevelToRange = {
        1: [2, 2], // 新的1级
        2: [5, 10],
        3: [10, 30],
        4: [30, 60],
        5: [60, 120],
        6: [120, 300],
        7: [180, 360], // 新的7级
        8: [240, 400],  // 新的8级
        11: [10, 60],
        12: [10, 120],
        13: [10, 180],
        14: [10, 240],
        15: [10, 300],
        16: [10, 360],
        17: [10, 420]
};
    const range = fensanLevelToRange[fensanLevel] || [5, 10];
    const medianTime = (range[0] + range[1]) / 2;
    const estimatedTimeInMinutes = Math.round((targetCount * medianTime + targetCount * 5) / 60);
    const formattedTime = estimatedTimeInMinutes >= 60 ? `${Math.floor(estimatedTimeInMinutes / 60)} 小时 ${estimatedTimeInMinutes % 60} 分钟` : `${estimatedTimeInMinutes} 分钟`;

    orderInfo += `
        <div style="font-size: 1.2rem; font-weight: bold; color: #007bff;">预计需要时间:</div>
        <div style="margin-left: 10px; color: #333;">大约 ${formattedTime} 完成</div>
    `;

    // 计算费用
    const totalCost = calculateTotalCost();
    orderInfo += `
        <div style="font-weight: bold; font-size: 1.5rem; color: red; margin-top: 10px;">
需要费用: ${targetCount * unitCost <= 3 ? '3 元' : `${targetCount} × ${unitCost} = ${totalCost.toFixed(2)} 元`}
        <span style="font-weight: bold; font-size: 1.2rem; color: forestgreen;">
代币支付所需: ${totalCost.toFixed(2)} 代币  <a href="/tokenvault" target="_blank" style="margin-left: 10px; color: #007bff;">获取/充值代币码点这</a>
        </span>
<span style="color: #007bff; cursor: pointer;" onclick="toggleDetails()">[查看详细]</span>
        </div>
    `;

    // 计算详细费用过程并添加到 orderInfo
    orderInfo += `
        <div id="detailedInfo" style="display: none; margin-top: 1px; padding: 5px; background-color: #f0f0f0; border-radius: 5px;">
            <div style="font-size: 1rem; color: #333;">
                <p><strong>单价计算规则:</strong> 是否换IP: ${changeIp === 'yes' ? '是' : '否'}</p>
                <ul style="margin-left: 10px; padding-left: 10px; list-style: circle; margin-bottom: 0;">
                    <li>换IP单价: 0.06 元/份</li>
                    <li>不换IP单价: 0.04 元/份</li>
                </ul>
                <p><strong>费用计算公式:</strong> 目标份数 × 单价</p>
                <p><strong>详细计算过程:</strong></p>
                <ul style="margin-left: 10px; padding-left: 10px; list-style: circle; margin-bottom: 0;">
                    <li>目标份数: ${targetCount} 份</li>
                    <li>单价: ${unitCost} 元/份</li>
                    <li><strong>计算公式:</strong> ${targetCount} × ${unitCost} = ${(targetCount * unitCost).toFixed(2)} 元</li>
                    <li><strong>总费用（最低消费 3 元）:</strong> ${totalCost < 3 ? '3.00' : totalCost.toFixed(2)} 元</li>
                </ul>
            </div>
        </div>
    `;

    // 显示确认模态框
    document.getElementById('previewMessage').innerHTML = orderInfo;
    document.getElementById('previewModal').style.display = 'block';
});
// 关闭模态框
document.querySelector('.close').addEventListener('click', function () {
    document.getElementById('previewModal').style.display = 'none';
});

// 点击窗口外部关闭模态框
window.onclick = function (event) {
    const modal = document.getElementById('previewModal');
    if (event.target === modal) {
        modal.style.display = "none";
    }
};
// 确认按钮的点击事件（在线支付）
document.querySelector('.confirm-button1').addEventListener('click', function () {
    // 比例填写正确，进行支付流程
    initiatePayment('alipay');
    document.getElementById('previewModal').style.display = "none"; // 关闭预览模态框
});

document.querySelector('.confirm-button2').addEventListener('click', function () {
    // 比例填写正确，进行支付流程
    initiatePayment('wxpay');
    document.getElementById('previewModal').style.display = "none"; // 关闭预览模态框
});

// 代币支付按钮点击事件
document.querySelector('.token-button').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'block';
    document.getElementById('previewModal').style.display = "none"; // 关闭预览模态框
});

// 动态加载动效的逻辑
function showLoadingSpinner() {
    // 创建并显示加载动效的 DOM 元素
    const spinner = document.createElement('div');
    spinner.id = 'loadingSpinner';

    // 整体背景样式
    spinner.style.position = 'fixed';
    spinner.style.top = '0';
    spinner.style.left = '0';
    spinner.style.width = '100%';
    spinner.style.height = '100%';
    spinner.style.backgroundColor = 'rgba(200, 200, 200, 0.8)'; // 浅灰色半透明背景
    spinner.style.zIndex = '1000';
    spinner.style.display = 'flex';
    spinner.style.justifyContent = 'center';
    spinner.style.alignItems = 'center';

    spinner.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);">
            <div class="spinner" style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <p style="margin-top: 10px; font-size: 16px; color: #333;">正在生成订单...请不要关闭页面！</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    document.body.appendChild(spinner);
}


function hideLoadingSpinner() {
    // 移除加载动效的 DOM 元素
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        document.body.removeChild(spinner);
    }
}

function showQueryingSpinner() {
    // 创建并显示查询动效的 DOM 元素
    const spinner = document.createElement('div');
    spinner.id = 'queryingSpinner';

    // 整体背景样式
    spinner.style.position = 'fixed';
    spinner.style.top = '0';
    spinner.style.left = '0';
    spinner.style.width = '100%';
    spinner.style.height = '100%';
    spinner.style.backgroundColor = 'rgba(200, 200, 200, 0.8)'; // 浅灰色半透明背景
    spinner.style.zIndex = '1000';
    spinner.style.display = 'flex';
    spinner.style.justifyContent = 'center';
    spinner.style.alignItems = 'center';

    spinner.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);">
            <div class="spinner" style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #ff5733; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <p style="margin-top: 10px; font-size: 16px; color: #333;">正在查询，请稍候...</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    document.body.appendChild(spinner);
}

function hideQueryingSpinner() {
    // 移除查询动效的 DOM 元素
    const spinner = document.getElementById('queryingSpinner');
    if (spinner) {
        document.body.removeChild(spinner);
    }
}

let paymentCountdownInterval;
const paymentTimeoutDuration = 3 * 60 * 1000; // 10分钟的支付时限（以毫秒为单位）
// 初始化支付流程
function initiatePayment(payType) {
    const totalCost = calculateTotalCost();
    const targetCount = document.getElementById('target_count').value;
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value === 'yes';


    const surveyLink = document.getElementById('survey_link').value;
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    const mobileRatio = document.getElementById('mobile_ratio_display').value;
    const linkRatio = document.getElementById('link_ratio_display').value;
    const wechatRatio = document.getElementById('wechat_ratio_display').value;
    const fensanLevel = document.getElementById('fensan_level').value;
    // 获取已添加城市列表并组合成字符串
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
        .map(item => item.textContent.replace('删除', '').trim());
    const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔

    // 获取当前时间
    let currentDate = new Date();
    let year = currentDate.getFullYear();
    let month = String(currentDate.getMonth() + 1).padStart(2, "0");
    let day = String(currentDate.getDate()).padStart(2, "0");
    let hours = String(currentDate.getHours()).padStart(2, "0");
    let minutes = String(currentDate.getMinutes()).padStart(2, "0");
    let seconds = String(currentDate.getSeconds()).padStart(2, "0");

    // 构建文件名
    let fileName = `油猴脚本VM版_${year}${month}${day}${hours}${minutes}${seconds}.txt`;

    showLoading();
    // 向后端请求生成支付二维码
    fetch('generate-payment-order-safety-js', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            amount: totalCost,
            targetCount: targetCount,
            changeIp: changeIp,
            type: 2, // 指定为上传脚本
            payType: payType,

            surveyLink: surveyLink,
            ipArea: ipArea, // 已添加的所有城市
            fensanLevel: fensanLevel,
            sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
            tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围

            content: scriptContent,
            fileName: fileName
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.outTradeNo && data.paymentImage) {
                // 显示支付二维码模态框
                showPaymentModal(data.paymentImage, totalCost.toFixed(2),payType);

                // 开始轮询支付状态
                startPaymentStatusPolling(data.outTradeNo);
            } else {
                alert('生成支付信息失败，请重试。');
            }
        })
        .catch(error => {
            console.error('生成支付信息时发生错误:', error);
            alert('生成支付信息时发生错误，请稍后重试。');
        })
        .finally(() => {
            hideLoading();
        });
}


function showPaymentModal(paymentImageUrl, amount,payType) {
    const paymentModal = document.getElementById('paymentModal');
    document.getElementById('payment-qr-code').src = paymentImageUrl;
    document.getElementById('payment-amount').textContent = amount;
    paymentModal.style.display = 'block';

    const titleElement = paymentModal.querySelector('h2');
    const qrCodeImg = document.getElementById('payment-qr-code');
    // 根据支付类型设置内容
    if(payType === 'wxpay') {
        titleElement.textContent = '请使用微信扫码支付';
        qrCodeImg.alt = '微信支付二维码';
        titleElement.style.color = '#07C160'; // 微信绿色
        // 更换微信支付LOGO（需要准备微信图标）
    } else {
        titleElement.textContent = '请使用支付宝扫码支付';
        qrCodeImg.alt = '支付宝支付二维码';
        titleElement.style.color = '#007BFF'; // 微信绿色
    }

    // 启动倒计时
    startPaymentCountdown(paymentTimeoutDuration);
}

function showConfirmModal(outTradeNo) {
    const modal = document.getElementById('confirmModal');
    modal.style.display = 'block';

    // 确认按钮点击事件
    document.getElementById('confirmOpen').onclick = function() {
        // 直接在这里调用 window.open（用户点击触发，不会被拦截）
        const newWindow = window.open(`queryOrder?ordernum=${outTradeNo}`, '_blank');
        // 如果窗口被拦截，提供回退方案
        if (!newWindow || newWindow.closed) {
            alert('弹窗被拦截，请手动点击首页的“查询订单”按钮。');
        }
        modal.style.display = 'none';
    };

    // 取消按钮点击事件
    document.getElementById('cancelOpen').onclick = function() {
        modal.style.display = 'none';
    };
}

let paymentStatusCheckInterval;

function startPaymentStatusPolling(outTradeNo) {
    let pollingAttempts = 0;
    const pollingInterval = 4000; // 轮询间隔（毫秒）
    const maxPollingDuration = paymentTimeoutDuration; // 与倒计时持续时间匹配
    const maxPollingAttempts = Math.ceil(maxPollingDuration / pollingInterval);

    paymentStatusCheckInterval = setInterval(function () {
        fetch(`checkPaymentForOrderSafety?outTradeNo=${outTradeNo}`, {
            method: 'GET'
        })
            .then(response => response.json())
            .then(data => {
                // console.log(data)
                if (data.is_pay === 1) {
                    clearInterval(paymentStatusCheckInterval);

                    // alert('付款成功！订单已生成，点击确定打开查询订单页面（如果没有弹出查询页面，可以在首页点击查询已付款订单手动查询）');
                    // 关闭支付模态框
                    document.getElementById('paymentModal').style.display = 'none';
                    // window.location.href = `queryOrder?ordernum=${outTradeNo}`; // 在当前页面跳转到查询页面
                    // 显示询问模态框，并传递订单号
                    showConfirmModal(outTradeNo);
                } else if (data.is_pay === 0) {
                    // 支付未完成，继续轮询
                    pollingAttempts++;
                    if (pollingAttempts >= maxPollingAttempts) {
                        clearInterval(paymentStatusCheckInterval);
                        handlePaymentTimeout();
                    }
                } else {
                    console.log("发生错误，需要处理这种情况");
                }
            })
            .catch(error => {
                console.error('检查支付状态时发生错误:', error);
            });
    }, pollingInterval);
}

// 关闭支付模态框
document.querySelector('.close-payment').addEventListener('click', function () {
    document.getElementById('paymentModal').style.display = 'none';
    clearInterval(paymentStatusCheckInterval); // 停止轮询
});

// 代币支付代币激活码输入模态框的关闭事件
document.querySelector('.close-token-input').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'none';
});

// 代币支付代币激活码确认按钮点击事件
document.querySelector('.confirm-identity-code').addEventListener('click', function () {
    const identityCode = document.getElementById('identityCodeInput').value.trim();;
    //显示正在查询动效
    showQueryingSpinner();
    if (identityCode) {
        // 发送请求到后端，查询代币激活码相关的代币信息
        fetch('tokenvault/getTokenInfo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({activationCode: identityCode})
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    //关闭正在查询动效
                    hideQueryingSpinner();
                    // 显示查询到的代币激活码相关信息
                    document.getElementById('identityCodeInfo').textContent = `${identityCode}，余额: ${data.tokenBalance} 代币`;
                    document.getElementById('tokenInputModal').style.display = 'none';
                    document.getElementById('tokenPaymentConfirmModal').style.display = 'block';
                } else {
                    //关闭正在查询动效
                    hideQueryingSpinner();
                    alert('代币激活码无效或未找到，请重新输入。');
                }
            })
            .catch(error => {
                //关闭正在查询动效
                hideQueryingSpinner();
                console.error('查询代币激活码时发生错误:', error);
                alert('查询代币激活码时发生错误，请稍后重试。');
            });
    } else {
        //关闭正在查询动效
        hideQueryingSpinner();
        alert('请输入有效的代币激活码');
    }
});

// 代币支付确认模态框的关闭事件
document.querySelector('.close-token-confirm').addEventListener('click', function () {
    document.getElementById('tokenPaymentConfirmModal').style.display = 'none';
});

// 最终确认代币支付按钮点击事件
document.querySelector('.final-confirm-token-payment').addEventListener('click', function () {
    const identityCode = document.getElementById('identityCodeInput').value.trim();;
    const totalCost = calculateTotalCost();
    const targetCount = parseInt(document.getElementById('target_count').value, 10);
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value === 'yes';

    const surveyLink = document.getElementById('survey_link').value;
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    const mobileRatio = document.getElementById('mobile_ratio_display').value;
    const linkRatio = document.getElementById('link_ratio_display').value;
    const wechatRatio = document.getElementById('wechat_ratio_display').value;
    const fensanLevel = document.getElementById('fensan_level').value;
    // 获取已添加城市列表并组合成字符串
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
        .map(item => item.textContent.replace('删除', '').trim());
    const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔

    // 获取当前时间
    let currentDate = new Date();
    let year = currentDate.getFullYear();
    let month = String(currentDate.getMonth() + 1).padStart(2, "0");
    let day = String(currentDate.getDate()).padStart(2, "0");
    let hours = String(currentDate.getHours()).padStart(2, "0");
    let minutes = String(currentDate.getMinutes()).padStart(2, "0");
    let seconds = String(currentDate.getSeconds()).padStart(2, "0");

    // 构建文件名
    let fileName = `油猴脚本VM版_${year}${month}${day}${hours}${minutes}${seconds}.txt`;

    if (!identityCode) {
        alert('请输入有效的代币激活码');
        return;
    }

    if (totalCost <= 0) {
        alert('无效的支付金额');
        return;
    }

    // 发送代币支付请求到后端
    fetch('tokenvault/payWithTokenForJS', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            activationCode: identityCode,
            amount: totalCost,
            targetCount: targetCount,
            changeIp: changeIp,
            type: 2, // 指定为脚本模式

            surveyLink: surveyLink,
            ipArea: ipArea, // 已添加的所有城市
            fensanLevel: fensanLevel,
            sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
            tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围

            content: scriptContent,
            fileName: fileName
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // alert('代币支付成功！正在生成订单。');
                document.getElementById('tokenPaymentConfirmModal').style.display = 'none';

                showConfirmModal(data.outTradeNo);
                // 显示加载动效
                // showLoadingSpinner();
                // 调用代币订单创建接口
                // createOrderForDB(identityCode);  // 不传递交易号，由后端 session 管理
            } else {
                alert(data.message || '代币支付失败，请检查您的余额是否足够（不够可以点击【获取代币码】或者【查询代币码】按钮后自助充值）');
            }
        })
        .catch(error => {
            console.error('代币支付时发生错误:', error);
            alert('代币支付时发生错误，请稍后重试。');
        });
});

// 代币支付取消按钮点击事件
document.querySelector('.cancel-token-payment').addEventListener('click', function () {
    document.getElementById('tokenPaymentConfirmModal').style.display = 'none';
});

// 代币支付代币激活码输入取消按钮点击事件
document.querySelector('.cancel-identity-code').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'none';
});

// 模拟发送请求到后端创建订单的函数
// function createOrder() {
//     // 获取其他表单数据
//     const targetCount = document.getElementById('target_count').value;
//     const surveyLink = document.getElementById('survey_link').value;
//     const fillTimeStart = document.getElementById('fill_time_start').value;
//     const fillTimeEnd = document.getElementById('fill_time_end').value;
//     const mobileRatio = document.getElementById('mobile_ratio_display').value;
//     const linkRatio = document.getElementById('link_ratio_display').value;
//     const wechatRatio = document.getElementById('wechat_ratio_display').value;
//     const fensanLevel = document.getElementById('fensan_level').value;
//
//     // 获取已添加城市列表并组合成字符串
//     const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
//         .map(item => item.textContent.replace('删除', '').trim());
//     const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔
//
//     // 创建表单数据对象
//     const formData = {
//         targetCount: targetCount,
//         surveyLink: surveyLink,
//         ipArea: ipArea, // 已添加的所有城市
//         fensanLevel: fensanLevel,
//         sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
//         tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
//         price: calculateTotalCost(),
//         scriptContent: scriptContent // 添加脚本内容
//     };
//
//     // 发送请求到后端创建订单
//     fetch('order/createOrderByJS', {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json' // 指定请求体为 JSON 格式
//         },
//         body: JSON.stringify(formData) // 将数据转换为 JSON 字符串
//     })
//         .then(response => response.json())
//         .then(data => {
//             if (data.success) {
//                 const orderNumber = data.orderNumber; // 获取生成的订单号
//                 console.log(`订单创建成功，订单号: ${orderNumber}`);
//                 // 关闭支付模态框
//                 document.getElementById('paymentModal').style.display = 'none';
//
//                 // 延迟 1 秒后调用 checkOrder 方法来验证订单脚本生成情况
//                 setTimeout(function () {
//                     checkOrder(orderNumber);
//                 }, 2000); // 1000 毫秒 = 1 秒
//             } else {
//                 // 隐藏加载动效
//                 hideLoadingSpinner();
//                 alert('订单创建失败，请重试。');
//             }
//         })
//         .catch(error => {
//             // 隐藏加载动效
//             hideLoadingSpinner();
//             console.error('创建订单时发生错误:', error);
//             alert('创建订单时发生错误，请稍后重试。');
//         });
// }


// 调用代币订单创建的函数
// function createOrderForDB(identityCode) {
//     // 获取其他表单数据
//     const targetCount = document.getElementById('target_count').value;
//     const surveyLink = document.getElementById('survey_link').value;
//     const fillTimeStart = document.getElementById('fill_time_start').value;
//     const fillTimeEnd = document.getElementById('fill_time_end').value;
//     const mobileRatio = document.getElementById('mobile_ratio_display').value;
//     const linkRatio = document.getElementById('link_ratio_display').value;
//     const wechatRatio = document.getElementById('wechat_ratio_display').value;
//     const fensanLevel = document.getElementById('fensan_level').value;
//
//     // 获取已添加城市列表并组合成字符串
//     const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
//         .map(item => item.textContent.replace('删除', '').trim());
//     const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔
//
//     // 创建表单数据对象
//     const formData = {
//         targetCount: targetCount,
//         surveyLink: surveyLink,
//         ipArea: ipArea, // 已添加的所有城市
//         fensanLevel: fensanLevel,
//         sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
//         tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
//         price: calculateTotalCost(),
//         activationCode: identityCode,
//         scriptContent: scriptContent // 添加脚本内容
//     };
//
//     // 发送请求到后端创建代币支付订单
//     fetch('order/createOrderByJSForDB', {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json' // 指定请求体为 JSON 格式
//         },
//         body: JSON.stringify(formData) // 将数据转换为 JSON 字符串
//     })
//         .then(response => response.json())
//         .then(data => {
//             if (data.success) {
//                 const orderNumber = data.orderNumber; // 获取生成的订单号
//                 console.log(`订单创建成功，订单号: ${orderNumber}`);
//
//                 // 延迟 1 秒后调用 checkOrder 方法来验证订单脚本生成情况
//                 setTimeout(() => checkOrder(orderNumber), 2000);
//             } else {
//                 // 隐藏加载动效
//                 hideLoadingSpinner();
//                 alert(data.message || '订单创建失败，请重试。');
//             }
//         })
//         .catch(error => {
//             // 隐藏加载动效
//             hideLoadingSpinner();
//             console.error('创建订单时发生错误:', error);
//             alert('创建订单时发生错误，请稍后重试。');
//         });
// }


// 检查订单是否成功生成并上传了脚本的函数
// function checkOrder(orderNumber) {
//     fetch(`order/checkOrder?orderNumber=${orderNumber}`, {
//         method: 'GET'
//     })
//         .then(response => response.json())
//         .then(data => {
//             if (data.status === 'success') {
//                 // 隐藏加载动效
//                 hideLoadingSpinner();
//                 alert('订单生成成功！点击确定跳转到查询订单页面');
//                 window.location.href = `queryOrder?ordernum=${orderNumber}`; // 在当前页面跳转到查询页面
//             } else {
//                 // 隐藏加载动效
//                 hideLoadingSpinner();
//                 alert('生成订单失败，未能成功生成脚本！');
//             }
//         })
//         .catch(error => {
//             // 隐藏加载动效
//             hideLoadingSpinner();
//             console.error('检查订单脚本时发生错误:', error);
//             alert('检查订单脚本时发生错误，请稍后重试。');
//         });
// }

// 取消按钮的点击事件
document.querySelector('.cancel-button').addEventListener('click', function () {
    document.getElementById('previewModal').style.display = "none"; // 关闭模态框
});

function toggleDetails() {
    const details = document.getElementById('detailedInfo');
    if (details.style.display === 'none') {
        details.style.display = 'block';
    } else {
        details.style.display = 'none';
    }
}

function startPaymentCountdown(duration) {
    const paymentCountdownElement = document.getElementById('payment-countdown');
    let timeRemaining = duration;

    // 清除任何现有的倒计时
    clearInterval(paymentCountdownInterval);

    // 立即更新倒计时显示
    updateCountdownDisplay(timeRemaining);

    paymentCountdownInterval = setInterval(function () {
        timeRemaining -= 1000; // 每次减少1秒（1000毫秒）

        if (timeRemaining <= 0) {
            clearInterval(paymentCountdownInterval);
            paymentCountdownElement.textContent = '00:00';
            alert('支付超时，请重新支付。');
            // 关闭支付模态框
            document.getElementById('paymentModal').style.display = 'none';
            // 停止支付状态轮询
            clearInterval(paymentStatusCheckInterval);
        } else {
            updateCountdownDisplay(timeRemaining);
        }
    }, 1000); // 每秒更新一次
}

function updateCountdownDisplay(timeRemaining) {
    const paymentCountdownElement = document.getElementById('payment-countdown');
    const totalSeconds = Math.floor(timeRemaining / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    // 格式化分钟和秒数，确保两位数显示
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
    const formattedSeconds = seconds < 10 ? '0' + seconds : seconds;

    paymentCountdownElement.textContent = formattedMinutes + ':' + formattedSeconds;
}

function showLoading() {
    document.getElementById('globalLoading').classList.remove('d-none');
}

function hideLoading() {
    document.getElementById('globalLoading').classList.add('d-none');
}

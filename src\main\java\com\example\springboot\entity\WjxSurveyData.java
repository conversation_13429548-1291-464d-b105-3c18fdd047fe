package com.example.springboot.entity;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class WjxSurveyData {

    private Long id;

    private String orderNumber;

    private LocalDateTime createdTime;

    private String surveyLink;

    private List<SurveyData> jsonData; // 直接存储对象列表

    private String htmlSource;

    private Integer orderType; // 订单类型

    @PrePersist
    protected void onCreate() {
        this.createdTime = LocalDateTime.now();
    }
}

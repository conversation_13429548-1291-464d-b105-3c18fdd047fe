package com.example.springboot.service.impl;

import com.example.springboot.entity.TokenVault;
import com.example.springboot.mapper.TokenVaultMapper;
import com.example.springboot.service.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import com.example.springboot.mapper.AIChatSessionMapper;

@Service
public class TokenServiceImpl implements TokenService {

    @Autowired
    private TokenVaultMapper tokenVaultMapper;

    @Autowired
    private AIChatSessionMapper sessionMapper;

    @Override
    public boolean validateToken(String tokenCode) {
        TokenVault tokenVault = tokenVaultMapper.findByTokenCode(tokenCode);
        return tokenVault != null && tokenVault.getTokenBalance().compareTo(BigDecimal.ZERO) > 0;
    }

    @Override
    public TokenVault getTokenVault(String tokenCode) {
        TokenVault tokenVault = tokenVaultMapper.findByTokenCode(tokenCode);
        if (tokenVault != null) {
            // 计算总消耗代币并设置到TokenVault对象中
            int totalConsumed = sessionMapper.getTotalTokenConsumedByTokenCode(tokenCode);
            tokenVault.setTotalTokensConsumed(totalConsumed);
        }
        return tokenVault;
    }

    @Override
    @Transactional
    public boolean consumeToken(String tokenCode, double amount) {
        return tokenVaultMapper.deductTokenBalance(tokenCode, amount) > 0;
    }
} 
{
  // 使用 IntelliSense 了解相关属性。 
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "FactorAnalysisTest",
      "request": "launch",
      "mainClass": "com.example.springboot.FactorAnalysisTest",
      "projectName": "MyWenJuanXing_Management2"
    },
    {
      "type": "java",
      "name": "Current File",
      "request": "launch",
      "mainClass": "${file}"
    },
    {
      "type": "java",
      "name": "SpringbootApplication",
      "request": "launch",
      "mainClass": "com.example.springboot.SpringbootApplication",
      "projectName": "MyWenJuanXing_Management2"
    },
    {
      "type": "java",
      "name": "PaymentUtil",
      "request": "launch",
      "mainClass": "com.example.springboot.Utils.PaymentUtil",
      "projectName": "MyWenJuanXing_Management2"
    },
    {
      "type": "java",
      "name": "QueryOrderExample",
      "request": "launch",
      "mainClass": "com.example.springboot.Utils.QueryOrderExample",
      "projectName": "MyWenJuanXing_Management2"
    },
    {
      "type": "java",
      "name": "Test",
      "request": "launch",
      "mainClass": "com.example.springboot.Utils.Test",
      "projectName": "MyWenJuanXing_Management2"
    }
  ]
}
SET  @username = '抹茶没有茶';
SET  @qq = '2229220306';
SET  @remark = '1个星期';
SET  @date_month = 1;
INSERT INTO info (
    username,
    code,
    createtime,
    isnormal,
		qq
)VALUES(@username,(SELECT activation_code from activation_code where username IS NULL or username='' LIMIT 1),now(),1,@qq);

update activation_code,info set activation_code.createtime = info.createtime,activation_code.username=info.username where activation_code.activation_code = info.`code`;

UPDATE activation_code SET deadlinetime = DATE_ADD(createtime, INTERVAL @date_month MONTH) ,remarks=@remark WHERE username = @username; 


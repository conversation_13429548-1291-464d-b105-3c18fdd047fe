package com.example.springboot.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.PathResourceResolver;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

@Configuration("customWebConfig")
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 1. 先添加默认资源处理器（处理所有非JS资源）
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/", "classpath:/public/")
                .resourceChain(true)
                .addResolver(new PathResourceResolver());

        // 2. 添加JS目录专用处理器（含拦截逻辑）
        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/static/js/") // 修正资源路径
                .resourceChain(true)
                .addResolver(new PathResourceResolver() {
                    @Override
                    protected Resource getResource(String resourcePath,
                                                   Resource location) throws IOException {
                        try {
                            // 解码URL路径
                            String decodedPath = URLDecoder.decode(resourcePath, String.valueOf(StandardCharsets.UTF_8));

                            // 调试输出（生产环境可删除）
//                            System.out.println("[DEBUG] 检查JS目录文件：" + decodedPath);

                            // 拦截包含敏感词的路径
                            if (decodedPath.contains("易风") || decodedPath.contains("明文")) {
//                                System.out.println("[拦截] 发现敏感词：" + decodedPath);
                                return null;
                            }

                            // 返回原始资源
                            return super.getResource(resourcePath, location);
                        } catch (Exception e) {
                            System.err.println("资源加载错误：" + e.getMessage());
                            return null;
                        }
                    }
                });
    }
}

package com.example.springboot.Utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class QueryOrderExample {

    public static void main(String[] args) {
        String apiUrl = "https://z-pay.cn/api.php";
        String act = "order";
        String pid = "20230826000228";
        String key = "k5Nov9OuU2fKDsymfs2vVETzqJWtJ3Lm";
        String outTradeNo = "201911914837526544451"; // Replace with the actual order number

        // Build query parameters
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("act", act);
        queryParams.put("pid", pid);
        queryParams.put("key", key);
        queryParams.put("out_trade_no", outTradeNo);

        try {
            // Construct the URL with query parameters
            String urlWithParams = apiUrl + "?" + buildQueryString(queryParams);

            // Create a URL object and open a connection
            URL url = new URL(urlWithParams);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // Set request method to GET
            connection.setRequestMethod("GET");

            // Get the response code
            int responseCode = connection.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                // Read the response
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                // Process and print the response
                System.out.println("Response: " + response.toString());
            } else {
                System.out.println("Request failed with response code: " + responseCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String buildQueryString(Map<String, String> params) {
        StringBuilder query = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (query.length() > 0) {
                query.append("&");
            }
            query.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return query.toString();
    }
}


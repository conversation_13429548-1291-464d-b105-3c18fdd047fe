// package com.example.springboot;

// import com.example.springboot.service.impl.WJXTaskService;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.ActiveProfiles;

// @SpringBootTest(properties = {
// 	"spring.main.allow-bean-definition-overriding=true",
// 	"spring.main.allow-circular-references=true"
// })
// @ActiveProfiles("test")
// class SpringbootApplicationTests {

// 	@Autowired
// 	private WJXTaskService wjxTaskService;

// 	@Test
// 	void contextLoads() {
// 		wjxTaskService.checkOrderTimeout();
// 	}

// }

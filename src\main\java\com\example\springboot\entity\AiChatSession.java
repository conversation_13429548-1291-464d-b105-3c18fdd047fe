package com.example.springboot.entity;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
public class AiChatSession {
    private Long id;
    private String uuid;
    private String sessionName;
    private String tokenCode;
    private String surveyLink;
    private String excelData;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Integer tokenConsumed;
    private Boolean isDeleted;
    private LocalDateTime lastMessageTime;

    public void onCreate() {
        this.uuid = UUID.randomUUID().toString();
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.isDeleted = false;
    }
} 
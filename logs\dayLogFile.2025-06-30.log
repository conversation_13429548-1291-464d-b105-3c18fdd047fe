2025-06-30 13:07:45.134 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-30 13:07:45.248 INFO  [restartedMain] c.e.s.SpringbootApplication - Starting SpringbootApplication using Java 17.0.6 with PID 31260 (D:\cursorProjects\MyWenJuanXing_Management2\target\classes started by ZhangYangyang in D:\cursorProjects\MyWenJuanXing_Management2)
2025-06-30 13:07:45.251 DEBUG [restartedMain] c.e.s.SpringbootApplication - Running with Spring Boot v3.4.3, Spring v6.2.3
2025-06-30 13:07:45.252 INFO  [restartedMain] c.e.s.SpringbootApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-30 13:07:45.703 INFO  [restartedMain] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-30 13:07:45.704 INFO  [restartedMain] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-30 13:07:46.941 INFO  [restartedMain] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30 13:07:47.167 INFO  [restartedMain] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 190 ms. Found 1 JPA repository interface.
2025-06-30 13:07:49.288 INFO  [restartedMain] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 9090 (http)
2025-06-30 13:07:49.333 INFO  [restartedMain] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
2025-06-30 13:07:49.340 INFO  [restartedMain] o.a.c.c.StandardService - Starting service [Tomcat]
2025-06-30 13:07:49.341 INFO  [restartedMain] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-06-30 13:07:49.534 INFO  [restartedMain] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-06-30 13:07:49.536 INFO  [restartedMain] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3832 ms
2025-06-30 13:07:49.881 INFO  [restartedMain] c.z.h.HikariDataSource - HikariPool-1 - Starting...
2025-06-30 13:07:50.616 INFO  [restartedMain] c.z.h.p.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@67b5ddc2
2025-06-30 13:07:50.617 INFO  [restartedMain] c.z.h.HikariDataSource - HikariPool-1 - Start completed.
2025-06-30 13:07:51.047 INFO  [restartedMain] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30 13:07:51.265 INFO  [restartedMain] o.h.Version - HHH000412: Hibernate ORM core version 6.6.8.Final
2025-06-30 13:07:51.397 INFO  [restartedMain] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-30 13:07:51.722 INFO  [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30 13:07:51.903 INFO  [restartedMain] o.h.o.c.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.36
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-30 13:07:52.810 INFO  [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30 13:07:52.813 INFO  [restartedMain] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30 13:07:55.207 WARN  [restartedMain] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30 13:07:55.553 INFO  [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-30 13:07:57.927 INFO  [restartedMain] o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-30 13:07:58.000 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-30 13:07:58.002 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@657eb99d]]
2025-06-30 13:07:58.005 INFO  [restartedMain] o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-30 13:07:58.032 INFO  [restartedMain] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 9090 (http) with context path '/'
2025-06-30 13:07:58.051 INFO  [restartedMain] c.e.s.SpringbootApplication - Started SpringbootApplication in 13.282 seconds (process running for 14.451)
2025-06-30 13:07:58.058 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 13:07:58.879 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-06-30 13:07:58.883 INFO  [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-06-30 13:07:58.884 INFO  [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@657eb99d]]
2025-06-30 13:07:58.884 INFO  [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-06-30 13:07:58.890 INFO  [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-30 13:07:58.896 INFO  [SpringApplicationShutdownHook] c.z.h.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-30 13:07:58.914 INFO  [SpringApplicationShutdownHook] c.z.h.HikariDataSource - HikariPool-1 - Shutdown completed.

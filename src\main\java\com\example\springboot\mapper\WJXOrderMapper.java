package com.example.springboot.mapper;


import com.example.springboot.entity.WJXOrder;
import com.example.springboot.entity.WJXSubmitData;
import org.apache.ibatis.annotations.*;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface WJXOrderMapper {

    /**
     * 查询所有未完成且状态有效的订单，并按创建时间升序排序
     *
     * @return 未完成订单列表
     */
    @Select("SELECT o.*, t.content AS jsText " +
            "FROM wjx_order o " +
            "LEFT JOIN txtcontent t ON o.order_number = t.tokenValue " +
            "WHERE o.is_completed = 0 AND o.order_status > 0 AND o.is_deleted = 0 AND o.is_pay = 1 " + // 添加了 order_status > 0 条件
            "AND (o.thread_lock IS NULL OR o.thread_lock = '') " + // 新增条件：thread_lock 必须是空或者空字符串
            "ORDER BY o.created_time ASC")
    @Results({
            @Result(property = "jsText", column = "jsText")
    })
    List<WJXOrder> findIncompleteOrders();

    @Select("SELECT o.*, t.content AS jsText " +
            "FROM wjx_order o " +
            "LEFT JOIN txtcontent t ON o.order_number = t.tokenValue " +
            "WHERE o.is_completed = 0 AND o.order_status > 0 AND o.is_deleted = 0 AND o.is_pay = 1 " + // 添加了 order_status > 0 条件
            "ORDER BY o.created_time ASC")
    @Results({
            @Result(property = "jsText", column = "jsText")
    })
    List<WJXOrder> findIncompleteOrdersNoThread();


    /**
     * 创建新的订单并插入到数据库
     *
     * @param order WJXOrder 对象，包含订单的详细信息
     */
    @Insert("INSERT INTO wjx_order (order_number, created_time, target_count, completed_count, real_completed_count, " +
            "is_completed, survey_link, token_value, js_text, ip_area, fensan_level, source_bili, tianxie_time, order_status, " +
            "remark, is_real_completed, thread_lock, trade_no, price, is_pay,activation_code,bili_data,type,remaining_reboot_count,is_deleted,is_refund,is_settled,pay_type,refund_amount,message,export_excel_count,is_need_pause) " +
            "VALUES (#{orderNumber}, #{createdTime}, #{targetCount}, #{completedCount}, #{realCompletedCount}, " +
            "#{isCompleted}, #{surveyLink}, #{tokenValue}, #{jsText}, #{ipArea}, #{fensanLevel}, #{sourceBili}, " +
            "#{tianxieTime}, #{orderStatus}, #{remark}, #{isRealCompleted}, #{threadLock}, #{tradeNo}, #{price}, #{isPay},#{activationCode},#{biliData},#{type},#{remainingRebootCount},#{isDeleted},#{isRefund},#{isSettled},#{payType},#{refundAmount},#{message},#{exportExcelCount},#{isNeedPause})")
    void createOrder(WJXOrder order);


    /**
     * 保存提交数据记录到数据库
     *
     * @param submitData WJXSubmitData 对象，包含提交数据的详细信息
     */
    @Insert("INSERT INTO wjx_submit_data (value, order_number, is_used, created_time) " +
            "VALUES (#{value}, #{orderNumber}, #{isUsed}, #{createdTime})")
    void saveSubmitData(WJXSubmitData submitData);

    /**
     * 根据订单号查找订单
     *
     * @param orderNumber 订单号
     * @return 找到的订单对象，如果不存在则返回 null
     */
    @Select("SELECT * FROM wjx_order WHERE order_number = #{orderNumber}")
    WJXOrder findOrderByNumber(String orderNumber);

    /**
     * 更新订单的完成数量和完成状态
     *
     * @param order 包含更新后的完成数量和状态的 WJXOrder 对象
     */
    @Update("UPDATE wjx_order SET completed_count = #{completedCount}, is_completed = #{isCompleted},order_status = #{orderStatus} WHERE order_number = #{orderNumber} AND is_deleted = 0 AND is_pay = 1")
    void updateOrderCompletion(WJXOrder order);

    /**
     * 根据订单ID查找订单
     *
     * @param orderId 订单ID
     * @return 找到的订单对象，如果不存在则返回 null
     */
    @Select("SELECT * FROM wjx_order WHERE id = #{orderId} AND is_deleted = 0 AND is_pay = 1")
    WJXOrder findOrderById(Long orderId);

    /**
     * 更新订单信息，包括完成数量和完成状态
     *
     * @param order 包含更新信息的 WJXOrder 对象
     */
    @Update("UPDATE wjx_order " +
            "SET " +
            "order_number = #{orderNumber}, " +
            "created_time = #{createdTime}, " +
            "target_count = #{targetCount}, " +
            "real_completed_count = #{realCompletedCount}, " +
            "is_real_completed = #{isRealCompleted}, " +
            "completed_count = #{completedCount}, " +
            "is_completed = #{isCompleted}, " +
            "survey_link = #{surveyLink}, " +
            "token_value = #{tokenValue}, " +
            "js_text = #{jsText}, " +
            "ip_area = #{ipArea}, " +
            "fensan_level = #{fensanLevel}, " +
            "source_bili = #{sourceBili}, " +
            "tianxie_time = #{tianxieTime}, " +
            "order_status = #{orderStatus}, " +
            "remark = #{remark}, " +
            "thread_lock = #{threadLock}, " +
            "price = #{price}, " +
            "is_pay = #{isPay}, " +
            "activation_code = #{activationCode}, " +
            "trade_no = #{tradeNo}, " +
            "type = #{type}, " +
            "remaining_reboot_count = #{remainingRebootCount}, " +
            "is_deleted = #{isDeleted}, " +
            "is_settled = #{isSettled}, " +
            "pay_type = #{payType}, " +
            "refund_amount = #{refundAmount}, " +
            "message = #{message}, " +
            "export_excel_count = #{exportExcelCount}, " +
            "is_need_pause = #{isNeedPause}, " +
            "is_refund = #{isRefund} " +
            "WHERE id = #{id}")
    void updateOrder(WJXOrder order);


    /**
     * 根据订单号查找未使用的提交数据
     *
     * @param orderNumber 订单号
     * @return 找到的未使用的提交数据对象，如果不存在则返回 null
     */
    @Select("SELECT * FROM wjx_submit_data WHERE order_number = #{orderNumber} AND is_used = 0 LIMIT 1")
    WJXSubmitData findUnusedSubmitDataByOrderNumber(String orderNumber);


    /**
     * 更新提交数据的使用状态
     *
     * @param submitDataId 提交数据的ID
     */
    @Update("UPDATE wjx_submit_data SET is_used = 1, use_time = NOW() WHERE id = #{submitDataId}")
    void updateSubmitDataUsage(Long submitDataId);

    @Select({
            "SELECT * FROM wjx_order WHERE (order_number = #{identifier} OR trade_no = #{identifier}) AND is_deleted = 0 AND is_pay = 1"
    })
    WJXOrder findOrderByIdentifier(@Param("identifier") String identifier);


    // 根据订单号查询订单信息
    @Select("SELECT * FROM wjx_order WHERE order_number = #{orderNumber} AND is_deleted = 0")
    WJXOrder findOrderByOrderNumber(String orderNumber);

    @Select("SELECT COUNT(*) FROM txtcontent WHERE tokenValue = #{orderNumber} AND content IS NOT NULL")
    int checkOrderScriptExist(String orderNumber);

    @Update("UPDATE wjx_order SET order_status = #{status} WHERE order_number = #{orderNumber} AND is_deleted = 0 AND is_pay = 1 AND order_status != -10")
    int updateOrderStatus(@Param("orderNumber") String orderNumber, @Param("status") int status);

    @Select("SELECT * FROM wjx_order WHERE is_real_completed = 0 AND order_status > 0  AND order_status!=6 AND is_deleted = 0 AND is_pay = 1")
    List<WJXOrder> findRealIncompleteOrders();

    @Select("SELECT * FROM wjx_order WHERE is_completed = 1 AND is_real_completed = 0 AND order_status >=3 AND order_status <6  AND is_deleted = 0 AND is_pay = 1")
    List<WJXOrder> getAvailRealOrders();

    @Update("UPDATE wjx_order SET thread_lock = #{threadLock} WHERE order_number = #{orderNumber} AND is_deleted = 0 AND is_pay = 1")
    int updateOrderThreadLock(@Param("orderNumber") String orderNumber, @Param("threadLock") String threadLock);

    @Select("SELECT * FROM wjx_order WHERE is_deleted = 0 AND is_pay = 1")
    List<WJXOrder> findAllOrders();

    // 使用注解实现查询 outTradeNo 是否已经被使用
    @Select("SELECT COUNT(*) FROM wjx_order WHERE order_number = #{orderNumber} AND is_deleted = 0 AND is_pay = 1 ")
    int countByOutOrderNumber(String orderNumber);

    @Select("SELECT * FROM wjx_order WHERE is_deleted = 0  AND is_pay = 1 ORDER BY created_time DESC LIMIT #{offset}, #{pageSize}")
    List<WJXOrder> findOrdersByPage(@Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("SELECT COUNT(*) FROM wjx_order WHERE is_deleted = 0 AND is_pay = 1 ")
    int countOrders();


    @Update("UPDATE wjx_order SET completed_count = 0, is_completed = 0, is_real_completed = 0 WHERE order_number = #{orderNumber}  AND is_deleted = 0")
    void reSetOrderToStatus_2(@Param("orderNumber") String orderNumber);

    @Delete("DELETE FROM wjx_submit_data WHERE order_number = #{orderNumber}")
    void deleteSubmitDataByOrderNumber(@Param("orderNumber") String orderNumber);

    default void resetOrderAndDeleteSubmitData(String orderNumber) {
        reSetOrderToStatus_2(orderNumber);
        deleteSubmitDataByOrderNumber(orderNumber);
    }

    @Select("<script>" +
            "SELECT * FROM wjx_order " +
            "WHERE is_deleted = 0 AND is_pay = 1 " +
            "<if test='orderNumber != null'>" +
            "   AND (order_number LIKE CONCAT('%', #{orderNumber}, '%') " +
            "   OR trade_no LIKE CONCAT('%', #{orderNumber}, '%') " +
            "   OR survey_link LIKE CONCAT('%', #{orderNumber}, '%') " +
            "   OR activation_code LIKE CONCAT('%', #{orderNumber}, '%')) " +
            "</if> " +
            "<if test='incomplete'>AND order_status != 6 </if> " +
            "<if test='isApplyRefund'>AND order_status &lt; 0 AND order_status != -9 AND order_status != -10 </if> " +
            "ORDER BY created_time DESC " +
            "LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<WJXOrder> findOrdersByCriteria(@Param("offset") int offset,
                                        @Param("pageSize") int pageSize,
                                        @Param("orderNumber") String orderNumber,
                                        @Param("incomplete") boolean incomplete,
                                        @Param("isApplyRefund") boolean isApplyRefund);

    @Select("<script>" +
            "SELECT COUNT(*) FROM wjx_order " +
            "WHERE is_deleted = 0 AND is_pay = 1 " +
            "<if test='orderNumber != null'>" +
            "   AND (order_number LIKE CONCAT('%', #{orderNumber}, '%') " +
            "   OR trade_no LIKE CONCAT('%', #{orderNumber}, '%') " +
            "   OR survey_link LIKE CONCAT('%', #{orderNumber}, '%') " +
            "   OR activation_code LIKE CONCAT('%', #{orderNumber}, '%')) " +
            "</if> " +
            "<if test='incomplete'>AND order_status != 6 </if>" +
            "<if test='isApplyRefund'>AND order_status &lt; 0 AND order_status != -9 AND order_status != -10 </if>" +
            "</script>")
    int countOrdersByCriteria(@Param("orderNumber") String orderNumber,
                              @Param("incomplete") boolean incomplete,
                              @Param("isApplyRefund") boolean isApplyRefund);


    // 删除订单
//    @Delete("DELETE FROM wjx_order WHERE order_number = #{orderNumber}")
    @Update("UPDATE wjx_order SET is_deleted = 1 WHERE order_number = #{orderNumber} AND is_pay = 1")
    void deleteOrderByOrderNumber(@Param("orderNumber") String orderNumber);


    @Delete("DELETE FROM txtcontent WHERE tokenValue = #{orderNumber}")
    void deleteTxtContentByTokenValue(@Param("orderNumber") String orderNumber);


    // 查询订单脚本内容，通过连接表 txtcontent 的 tokenValue
    @Select("SELECT t.content AS jsText FROM txtcontent t " +
            "JOIN wjx_order o ON t.tokenValue = o.order_number " +
            "WHERE o.order_number = #{orderNumber} AND o.is_deleted = 0 AND o.is_pay = 1 ")
    String getOrderScript(String orderNumber);

    // 更新 txtcontent 表中的脚本内容
    @Update("UPDATE txtcontent SET content = #{scriptContent} WHERE tokenValue = #{orderNumber}")
    int updateOrderScript(@Param("orderNumber") String orderNumber, @Param("scriptContent") String scriptContent);


    @Select("SELECT COUNT(*) FROM wjx_order WHERE order_number = #{orderNumber} AND is_deleted = 0")
    int countByOrderNumber(@Param("orderNumber") String orderNumber);

    @Select("SELECT * FROM wjx_order WHERE is_pay = 0 AND created_time < #{date} AND is_deleted = 0")
    List<WJXOrder> findUnpaidOrdersOlderThan(@Param("date") Date date);

    @Select("SELECT * FROM wjx_order WHERE survey_link = #{surveyLink} AND created_time >= #{startTime} AND created_time <= #{now} AND is_deleted = 0")
    List<WJXOrder> findOrdersBySurveyLinkAndCreatedTime(String surveyLink, LocalDateTime startTime, LocalDateTime now);

    @Select("SELECT COUNT(*) FROM wjx_submit_data WHERE order_number = #{orderNumber}")
    int doesOrderSubmitDataExist(String orderNumber);



    @Select({
            "<script>",
            "SELECT * FROM wjx_order WHERE order_number IN",
            "<foreach item='orderNum' collection='orderNumbers' open='(' separator=',' close=')'>",
            "   #{orderNum}",
            "</foreach>",
            "</script>"
    })
    List<WJXOrder> selectByOrderNumbers(@Param("orderNumbers") List<String> orderNumbers);


    @Update({
            "<script>",
            "UPDATE wjx_order SET is_settled = 1 WHERE order_number IN",
            "<foreach item='item' collection='orderNumbers' open='(' separator=',' close=')'>",
            "   #{item}",
            "</foreach>",
            "</script>"
    })
    int batchUpdateSettledStatus(@Param("orderNumbers") List<String> orderNumbers);

    @Update(
            "UPDATE wjx_order " +
            "SET order_status = 4 " +
            "WHERE order_status = -43 " +
            "AND created_time >= CURDATE() - INTERVAL 7 DAY"
    )
    void reSetOrder_43ToStatus_4();

    @Update(
            "UPDATE wjx_order " +
                    "SET order_status = 4 " +
                    "WHERE order_status = -7 " +
                    "AND created_time >= CURDATE() - INTERVAL 7 DAY"
    )
    void reSetOrder_7ToStatus_4();

    @Select("SELECT * FROM wjx_order WHERE order_status = -12 AND is_deleted = 0 AND is_pay = 1 AND (pay_type = 'alipay' || pay_type = 'wxpay') ")
    List<WJXOrder> findPendingRefundOrders();

    @Select("SELECT o.order_number, o.fensan_level, MAX(sd.use_time) AS last_use_time " +
            "FROM wjx_order o " +
            "JOIN wjx_submit_data sd ON o.order_number = sd.order_number " +
            "WHERE o.order_status = 5 " +
            "AND o.is_deleted = 0 " +
            "AND o.is_pay = 1 " +
            "AND o.type != 3 " +  //不包含EXCEL回填版
            "AND sd.use_time IS NOT NULL " +
            "GROUP BY o.order_number " +
            "HAVING TIMESTAMPDIFF(MINUTE, MAX(sd.use_time), NOW()) > 1")
    List<Map<String, Object>> findPotentialTimeoutOrders();

    @Update("UPDATE wjx_order SET export_excel_count = export_excel_count + 1 WHERE order_number = #{orderNumber}")
    void incrementExportExcelCount(String orderNumber);
}

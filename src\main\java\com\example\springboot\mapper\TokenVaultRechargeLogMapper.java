package com.example.springboot.mapper;

import com.example.springboot.entity.TokenVaultRechargeLog;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

@Mapper
public interface TokenVaultRechargeLogMapper {

    @Insert("INSERT INTO tokenvault_recharge_log (activation_code, recharge_amount, created_at, remark,out_trade_no,trade_no,invitation_code,actual_pay) " +
            "VALUES (#{activationCode}, #{rechargeAmount}, NOW(), #{remark}, #{outTradeNo}, #{tradeNo}, #{invitationCode}, #{actualPay})")
    void insertRechargeLog(TokenVaultRechargeLog rechargeLog);

    // 查询充值记录（支持搜索和分页）
    @Select("<script>" +
            "SELECT * FROM tokenvault_recharge_log " +
            "WHERE activation_code = #{activationCode} " +
            "<if test='search != null and search != \"\"'> " +
            "AND remark LIKE CONCAT('%', #{search}, '%') " +
            "</if> " +
            "ORDER BY created_at DESC " +
            "LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<TokenVaultRechargeLog> findRechargeLogsByActivationCode(
            @Param("activationCode") String activationCode,
            @Param("search") String search,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize);

    // 查询充值记录总数（支持搜索）
    @Select("<script>" +
            "SELECT COUNT(*) FROM tokenvault_recharge_log " +
            "WHERE activation_code = #{activationCode} " +
            "<if test='search != null and search != \"\"'> " +
            "AND remark LIKE CONCAT('%', #{search}, '%') " +
            "</if> " +
            "</script>")
    int countRechargeLogsByActivationCodeAndSearch(
            @Param("activationCode") String activationCode,
            @Param("search") String search);


    @Select("SELECT activation_code, recharge_amount, created_at, remark, out_trade_no, trade_no " +
            "FROM tokenvault_recharge_log " +
            "WHERE (out_trade_no = #{orderNo} OR trade_no = #{orderNo}) " +
            "AND created_at >= DATE_SUB(NOW(), INTERVAL 3 DAY)")
    List<TokenVaultRechargeLog> findByOrderNo(@Param("orderNo") String orderNo);



    // 查询充值记录总数
    @Select("<script>" +
            "SELECT COUNT(*) " +
            "FROM tokenvault_recharge_log " +
            "WHERE activation_code = #{activationCode} " +
            "<if test='search != null and search != \"\"'> " +
            "   AND remark LIKE CONCAT('%', #{search}, '%') " + // 统一用 remark 字段查询
            "</if>" +
            "<if test='abnormal'> " +
            "   AND out_trade_no NOT REGEXP '[一-龥]' " +  // 过滤中文订单号
            "</if> " +
            "<if test='incomplete'> " +
            "   AND out_trade_no NOT REGEXP '[一-龥]' " +  // 过滤中文订单号
            "</if> " +
            "</script>")
    int countRechargeLogsByActivationCode(@Param("activationCode") String activationCode, @Param("search") String search,@Param("abnormal") boolean abnormal,@Param("incomplete") boolean incomplete);

    // TokenVaultRechargeLogMapper接口新增
    @Select("<script>" +
            "SELECT * FROM tokenvault_recharge_log " +
            "<where>" +
            "  <if test='search != null'>" +
            "    out_trade_no LIKE CONCAT('%', #{search}, '%') OR " +
            "    activation_code LIKE CONCAT('%', #{search}, '%') OR " +
            "    trade_no LIKE CONCAT('%', #{search}, '%')" +
            "  </if>" +
            "</where>" +
            "ORDER BY created_at DESC" +
            "</script>")
    List<TokenVaultRechargeLog> searchByOrderNoOrTradeNo(@Param("search") String search);

    @Select("SELECT * FROM tokenvault_recharge_log ORDER BY created_at DESC")
    List<TokenVaultRechargeLog> findAll();

    @Select("SELECT * FROM tokenvault_recharge_log WHERE out_trade_no = #{outTradeNo}")
    Optional<TokenVaultRechargeLog> findByOutTradeNo(String outTradeNo);

    @Select("SELECT * FROM tokenvault_recharge_log WHERE trade_no = #{tradeNo}")
    Optional<TokenVaultRechargeLog> findByTradeNo(String tradeNo);
}

package com.example.springboot.controller;

import com.example.springboot.entity.TokenVault;
import com.example.springboot.service.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/token")
public class TokenController {

    @Autowired
    private TokenService tokenService;

    @PostMapping("/validate")
    public ResponseEntity<?> validateToken(@RequestBody Map<String, String> request) {
        String tokenCode = request.get("tokenCode");
        boolean isValid = tokenService.validateToken(tokenCode);
        if (isValid) {
            TokenVault tokenVault = tokenService.getTokenVault(tokenCode);
            return ResponseEntity.ok(Map.of(
                "valid", true,
                "tokenCount", tokenVault.getTokenBalance(),
                "totalTokensConsumed", tokenVault.getTotalTokensConsumed()
            ));
        }
        return ResponseEntity.ok(Map.of("valid", false));
    }

    @GetMapping("/count")
    public ResponseEntity<?> getTokenCount(@RequestParam String tokenCode) {
        TokenVault tokenVault = tokenService.getTokenVault(tokenCode);
        return ResponseEntity.ok(Map.of("count", tokenVault.getTokenBalance()));
    }

    @PostMapping("/consume")
    public ResponseEntity<?> consumeToken(@RequestBody Map<String, String> request) {
        String tokenCode = request.get("tokenCode");
        double amount = Double.parseDouble(request.get("amount"));
        boolean success = tokenService.consumeToken(tokenCode, amount);
        if (success) {
            TokenVault tokenVault = tokenService.getTokenVault(tokenCode);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "remainingTokens", tokenVault.getTokenBalance()
            ));
        }
        return ResponseEntity.ok(Map.of("success", false));
    }
} 
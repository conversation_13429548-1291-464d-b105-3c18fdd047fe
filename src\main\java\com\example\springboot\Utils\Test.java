package com.example.springboot.Utils;

import javax.crypto.KeyGenerator;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class Test {
    public static void main(String[] args) throws Exception {
        String aa = "https://www.wjx.cn/vm/eaVvb5B.aspx# ";
//        String patternStr = "/m/([A-Za-z0-9]+)\\.aspx";
//        Pattern pattern = Pattern.compile(patternStr);
//        Matcher matcher = pattern.matcher(aa);
//        String shortid = "";
//        if (matcher.find()) {
//            shortid = matcher.group(1);
//        }
//        System.out.println(shortid);
//        String surveyLink = aa;
//        surveyLink = surveyLink.substring(0, surveyLink.indexOf("#")); // 截取第一个#之前的字符
//        System.out.println(surveyLink);

//        System.out.println(processBili("33,33,34"));
//        System.out.println(processBili("10,80,10"));


//        String secretKeyBase64 = "DorweI1qz8jlRo6JERrqW9BoLM+NVH3eXnv2lCKVAtc=";
//        byte[] keyBytes = Base64.getDecoder().decode(secretKeyBase64);
//        System.out.println("Key Length (Java): " + keyBytes.length); // 必须为 32
//        System.out.println("Key Length (Java): " + keyBytes); // 必须为 32
//
//        String key = generateAESKey();
//        System.out.println("Base64 Key: " + key);
//        System.out.println("Key Length: " + Base64.getDecoder().decode(key).length); // 必须输出 32

        List<String> names = Arrays.asList("alice", "bob", "charlie", "david");
//        List<String> collect = names.stream().map(String::toUpperCase).collect(Collectors.toList());
        List<String> collect = names.stream().map(s -> s.toUpperCase()).collect(Collectors.toList());
        System.out.println(names);
        System.out.println(collect);
        List<String> stringStream = names.stream().map(s -> {
            System.out.println(1);
            return s.toUpperCase();

        }).collect(Collectors.toList());
        System.out.println(stringStream);

        names.stream().map(s -> {
                    System.out.println();
                    return s.toUpperCase();

                }).collect(Collectors.toList())
                .forEach(System.out::println);
    }
    public static String generateAESKey() throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(256); // 256位 = 32字节
        byte[] key = keyGen.generateKey().getEncoded();
        return Base64.getEncoder().encodeToString(key);
    }

    public static String processBili(String sourceBili) {
        // 将sourceBili字符串按","分隔成数组
        String[] parts = sourceBili.split(",");

        // 检查输入数组长度，确保至少有3个元素
        if (parts.length < 3) {
            throw new IllegalArgumentException("输入的字符串必须包含至少三个数字");
        }

        // 将字符串数组转换为整数数组
        int[] bili = new int[parts.length];
        for (int i = 0; i < parts.length; i++) {
            bili[i] = Integer.parseInt(parts[i]);
        }

        // 修改最后一个位置为0
        bili[bili.length - 1] = 0;

        // 计算第一个和第二个位置的占比
        int sum = bili[0] + bili[1];
        bili[0] = (sum == 0) ? 50 : (bili[0] * 100) / sum;  // 防止除以零
        bili[1] = 100 - bili[0];  // 保证两个比例之和为100

        // 将数组转换为字符串并返回
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < bili.length; i++) {
            result.append(bili[i]);
            if (i < bili.length - 1) {
                result.append(",");
            }
        }

        return result.toString();
    }

}

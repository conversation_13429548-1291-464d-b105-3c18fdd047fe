package com.example.springboot.mapper;

import com.example.springboot.entity.WjxSurveyData;
import com.example.springboot.handler.SurveyDataListTypeHandler;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface WJXSurveyDataMapper {

    // 插入数据（自动回填主键）
    @Insert("INSERT INTO wjx_survey_data (order_number, created_time, survey_link, json_data, html_source, order_type) " +
            "VALUES (#{orderNumber}, #{createdTime}, #{surveyLink}," +
            "#{jsonData,jdbcType=VARCHAR,typeHandler=com.example.springboot.handler.SurveyDataListTypeHandler}, " +
            " #{htmlSource}, #{orderType})")
    int insert(WjxSurveyData data);

    // 根据 ID 查询
    @Select("SELECT id, order_number, created_time, survey_link, json_data, html_source, order_type " +
            "FROM wjx_survey_data WHERE id = #{id}")
    @Results({
            @Result(column = "json_data", property = "jsonData",
                    typeHandler = SurveyDataListTypeHandler.class)
    })
    WjxSurveyData selectById(Long id);

    // 删除数据
    @Delete("DELETE FROM wjx_survey_data WHERE id = #{id}")
    int deleteById(Long id);

    // 删除数据
    @Delete("DELETE FROM wjx_survey_data WHERE order_number = #{orderNumber}")
    int deleteByOrderNumber(String orderNumber);

    // 查询全部数据（可选）
    @Select("SELECT * FROM wjx_survey_data")
    @Results({
            @Result(column = "json_data", property = "jsonData",
                    typeHandler = SurveyDataListTypeHandler.class)
    })
    List<WjxSurveyData> selectAll();

    @Select("SELECT * FROM wjx_survey_data " +
            "WHERE order_number = #{orderNumber} AND order_type = #{orderType}")
    @Results({
            @Result(column = "json_data", property = "jsonData",
                    typeHandler = SurveyDataListTypeHandler.class)
    })
    WjxSurveyData selectByOrderNumberAndType(
            @Param("orderNumber") String orderNumber,
            @Param("orderType") Integer orderType
    );

    @Select("SELECT * FROM wjx_survey_data WHERE order_number = #{orderNumber}")
    @Results({
            @Result(column = "json_data", property = "jsonData",
                    typeHandler = SurveyDataListTypeHandler.class)
    })
    WjxSurveyData findByOrderNumber(String orderNumber);
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无权限访问</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(to right, #e0f7fa, #80deea); /* 渐变背景 */
            height: 100vh; /* 设置高度为视口高度 */
            display: flex; /* 使用 flexbox 布局 */
            align-items: center; /* 垂直居中 */
            justify-content: center; /* 水平居中 */
            margin: 0; /* 清除默认边距 */
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 15px; /* 添加圆角 */
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
            max-width: 600px;
            width: 100%; /* 确保容器宽度适应 */
            text-align: center;
        }

        h1 {
            color: #e74c3c; /* 标题颜色 */
            font-weight: 600; /* 标题字体加粗 */
        }

        p {
            font-size: 18px;
            margin-top: 10px;
            color: #333333;
        }

        .button {
            display: inline-block;
            padding: 10px 20px;
            margin-top: 20px;
            background-color: #3498db;
            color: #ffffff;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .button:hover {
            background-color: #2980b9;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .logo {
            max-width: 60px;
            margin-right: 15px; /* Logo 和文字之间的间距 */
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #007bff; /* 文字颜色 */
        }
    </style>
</head>
<body>
<div class="container">
    <!-- Logo 和文字 -->
    <div class="logo-container">
        <a href="/">
            <img src="imgs/logo.png" alt="Logo" class="logo">
        </a>
        <span class="logo-text">易风问卷</span>
    </div>
    <h1>抱歉，访问失败</h1>
    <p>您没有足够的权限来查看此内容。</p>
    <a href="/" class="button">返回首页</a>
</div>
</body>
</html>

package com.example.springboot.entity;
import java.util.List;
import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;
@Data
public class DataQuery {
  @ToolParam(required = true, description = "是否只查询数据。true表示只查询数据并返回结果，false表示只返回需要操作的单元格坐标。必须为布尔值true或false。例如：true")
  private Boolean onlyQuery;

  @ToolParam(required = false, description = "需要返回的列号列表（从1开始）。如[1,2]表示返回第1、2列的数据。标准JSON格式，不能有注释。例如：[1,3]")
  private List<Integer> returnCols;

  @ToolParam(required = false, description = "筛选条件列表。每个条件为对象，包含col（列号，从1开始）、op（操作符）、value（值）。操作符可选：eq(等于), neq(不等于), contains(包含), gt(大于), lt(小于), gte(大于等于), lte(小于等于), in(在列表中)。value可以是字符串、数字或列表。标准JSON格式，示例：[{\"col\":8, \"op\":\"eq\", \"value\":1}, {\"col\":9, \"op\":\"contains\", \"value\":\"张\"}]")
  private List<Condition> conditions;

  @ToolParam(required = false, description = "是否去重。true表示去除重复行。布尔值，标准JSON格式。例如：true")
  private Boolean distinct;

  @ToolParam(required = false, description = "分组字段，指定按哪些列分组（从1开始）。如[2]表示按第2列分组。标准JSON格式。例如：[2]")
  private List<Integer> groupBy;

  @ToolParam(required = false, description = "需要返回的行号列表（（从1开始）正数表示从上往下数第几行，-1表示倒数第1行，-2表示倒数第2行，以此类推）。如[1,2,-1]表示返回第1、2和倒数第1行。标准JSON格式。例如：[1,-1]")
  private List<Integer> rowNums;

  @Data
  public static class Condition {
      @ToolParam(required = true, description = "列号，从1开始。例如：8表示第8列")
      private Integer col;
      @ToolParam(required = true, description = "操作符。可选：eq(等于), neq(不等于), contains(包含), gt(大于), lt(小于), gte(大于等于), lte(小于等于), in(在列表中)。例如：\"eq\"")
      private String op;
      @ToolParam(required = true, description = "筛选值。可以是字符串、数字或列表。例如：1，\"张\"，或[1,2,3]")
      private Object value;
  }
}

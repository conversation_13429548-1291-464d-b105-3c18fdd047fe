package com.example.springboot.exception;


import com.example.springboot.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Slf4j
public class ExceptionHandle {

    @ExceptionHandler(value = ServiceException.class)
    public Result serviceExceptionError(ServiceException e) {
        log.error("业务异常发生: {}, 异常位置: {}", e.getMessage(), getExceptionDetails(e), e);
        return Result.error(e.getMessage());
    }

    @ExceptionHandler(value = Exception.class)
    public Result exceptionError(Exception e) {
        log.error("系统错误发生: {}, 异常位置: {}", e.getMessage(), getExceptionDetails(e), e);
        return Result.error("系统错误");
    }

    /**
     * 获取异常的详细信息，包括类名、方法名和行号
     */
    private String getExceptionDetails(Exception e) {
        StackTraceElement element = e.getStackTrace()[0];
        return String.format("%s.%s(%s:%d)",
                element.getClassName(),   // 类名
                element.getMethodName(), // 方法名
                element.getFileName(),   // 文件名
                element.getLineNumber()  // 行号
        );
    }

}

package com.example.springboot.entity;

import lombok.Data;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Data
public class TokenVault {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // ID

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal tokenBalance; // 代币余额

    @Column(unique = true, nullable = false, length = 64)
    private String activationCode; // 激活码（唯一）

    @Column(length = 255)
    private String remark; // 备注字段

    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt; // 创建时间

    @Column
    private LocalDateTime updatedAt; // 更新时间

    @Column(unique = true, nullable = false, length = 64)
    private String invitationCode; // 邀请码

    @Column(length = 64)
    private String invitedByCode; // 邀请码

    @Column(nullable = true, precision = 10, scale = 2)
    private BigDecimal rebateReceived; // 推广返利获得的余额

    @Column(nullable = true)
    private int totalTokensConsumed; // 总消耗代币，将类型改为 int，并移除 precision 和 scale

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

}

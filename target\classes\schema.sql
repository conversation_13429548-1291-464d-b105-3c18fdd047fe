-- AI聊天会话表
CREATE TABLE IF NOT EXISTS ai_chat_session (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) NOT NULL UNIQUE,
    session_name VARCHAR(255) NOT NULL,
    token_code VARCHAR(50) NOT NULL,
    survey_link TEXT NOT NULL,
    excel_data LONGTEXT,
    create_time DATETIME NOT NULL,
    update_time DATETIME,
    token_consumed INT DEFAULT 0,
    is_deleted TINYINT(1) DEFAULT 0,
    last_message_time DATETIME,
    INDEX idx_token_code (token_code),
    INDEX idx_uuid (uuid)
);

-- AI聊天消息表
CREATE TABLE IF NOT EXISTS ai_chat_message (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    create_time DATETIME NOT NULL,
    message_order INT NOT NULL,
    data_modifications JSON,
    complete_excel_data TEXT,
    table_data TEXT,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (session_id) REFERENCES ai_chat_session(uuid),
    INDEX idx_session_id (session_id),
    INDEX idx_message_order (message_order)
);

-- 添加table_data字段（如果不存在）
ALTER TABLE ai_chat_message ADD COLUMN IF NOT EXISTS table_data TEXT;

-- 问卷数据表
CREATE TABLE IF NOT EXISTS wjx_survey_data (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    order_number VARCHAR(255) NOT NULL COMMENT '外键（普通订单或者是AI订单）',
    created_time DATETIME NOT NULL COMMENT '创建时间',
    survey_link VARCHAR(255) NOT NULL COMMENT '问卷链接',
    json_data JSON DEFAULT NULL COMMENT '问卷详细数据（题目，题号，题目类型，选项内容列表）',
    html_source MEDIUMTEXT COMMENT 'HTML源码',
    order_type INT NOT NULL COMMENT '订单类型，1代表是普通订单，2代表是AI分析订单',
    PRIMARY KEY (id, order_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
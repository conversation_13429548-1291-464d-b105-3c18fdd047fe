package com.example.springboot.service.impl;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.example.springboot.Utils.ExcelGenerator;
import com.example.springboot.entity.AiChatMessage;
import com.example.springboot.entity.AiChatSession;
import com.example.springboot.entity.SurveyData;
import com.example.springboot.entity.WjxSurveyData;
import com.example.springboot.mapper.AIChatMessageMapper;
import com.example.springboot.mapper.AIChatSessionMapper;
import com.example.springboot.mapper.WJXSurveyDataMapper;
import com.example.springboot.service.AIChatService;
import com.example.springboot.service.TokenService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class AIChatServiceImpl implements AIChatService {

    @Autowired
    private AIChatSessionMapper sessionMapper;

    @Autowired
    private AIChatMessageMapper messageMapper;

    @Autowired
    private WJXSurveyDataMapper surveyDataMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ObjectMapper objectMapper;


    @Value("${myservice}")
    private String serviceHost;

    @Value("${myport}")
    private String servicePort;

    @Autowired
    private ChatClient chatClient;

    // 添加数据修改记录Map
    private final Map<String, List<CellChange>> sessionCellChanges = new ConcurrentHashMap<>();

    // 添加数据修改记录类
    @Data
    @AllArgsConstructor
    private static class CellChange {
        private int row;
        private int col;
        private String oldValue;
        private String newValue;
        private LocalDateTime changeTime;
        private String changeReason; // 添加修改原因
    }

    @Override
    @Transactional
    public AiChatSession createSession(String tokenCode, String surveyLink, List<List<String>> excelData) {
        // 验证代币余额是否足够
        if (!tokenService.validateToken(tokenCode)) {
            throw new RuntimeException("无效的代币码或代币余额不足");
        }

        // 创建会话对象
        AiChatSession session = new AiChatSession();
        session.setTokenCode(tokenCode);
        session.setSurveyLink(surveyLink);
        session.setSessionName("新会话_" + new Date().getTime());
        session.setUuid(UUID.randomUUID().toString()); // 生成UUID
        session.setCreateTime(LocalDateTime.now());
        session.setIsDeleted(false);
        session.setTokenConsumed(0); // 初始化为0，等解析成功后再设置为1
        session.setLastMessageTime(session.getCreateTime());

        try {
            // 保存Excel数据
            String excelDataJson = objectMapper.writeValueAsString(excelData);
            session.setExcelData(excelDataJson);

            // 获取并保存问卷数据
            List<SurveyData> surveyData = fetchSurveyData(surveyLink);
            WjxSurveyData wjxSurveyData = new WjxSurveyData();
            wjxSurveyData.setOrderNumber(session.getUuid());
            wjxSurveyData.setOrderType(2);
            wjxSurveyData.setSurveyLink(surveyLink);
            wjxSurveyData.setJsonData(surveyData);
            wjxSurveyData.setCreatedTime(LocalDateTime.now());
            surveyDataMapper.insert(wjxSurveyData);

            // 数据解析成功，现在可以扣款了
            tokenService.consumeToken(tokenCode, 1.0);
            session.setTokenConsumed(1); // 设置已消耗的代币数

            // 保存会话
            sessionMapper.insert(session);

            return session;

        } catch (Exception e) {
            throw new RuntimeException("保存数据失败: " + e.getMessage());
        }
    }

    // 获取问卷数据
    private List<SurveyData> fetchSurveyData(String surveyUrl) {
        try {
            // 发起HTTP请求获取原始HTML
            RestTemplate restTemplate = new RestTemplate();
            String fullHtml = restTemplate.getForObject(surveyUrl, String.class);

            if (fullHtml == null || fullHtml.isEmpty()) {
                throw new RuntimeException("获取问卷HTML失败");
            }

            // 使用Jsoup提取指定DIV内容
            Document doc = Jsoup.parse(fullHtml);
            Element targetDiv = doc.selectFirst("#divQuestion");

            if (targetDiv == null) {
                throw new RuntimeException("未找到问卷内容");
            }

            // 获取DIV内部HTML
            String extractedHtml = targetDiv.outerHtml();

            // 解析问卷数据
            List<SurveyData> surveyData = ExcelGenerator.parseSurveyData(extractedHtml);
            return surveyData;
        } catch (Exception e) {
            log.error("解析问卷信息失败: " + e.getMessage());
            throw new RuntimeException("解析问卷信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<AiChatSession> getSessions(String tokenCode) {
        return sessionMapper.findByTokenCode(tokenCode);
    }

    @Override
    public AiChatSession getSession(Long sessionId) {
        return sessionMapper.findById(sessionId);
    }

    @Override
    @Transactional
    public Flux<AiChatMessage> sendMessage(Long sessionId, String message) {
        // 获取会话
        AiChatSession session = sessionMapper.findById(sessionId);
        if (session == null) {
            return Flux.error(new RuntimeException("会话不存在"));
        }

        // 验证代币
        if (!tokenService.validateToken(session.getTokenCode())) {
            return Flux.error(new RuntimeException("代币余额不足"));
        }

        // 注意：代币扣除将在AI成功回复并保存消息后进行

        // 获取当前最大消息顺序
        int currentMaxOrder = messageMapper.getMaxMessageOrder(session.getUuid());

        // 保存用户消息
        AiChatMessage userMessage = new AiChatMessage();
        userMessage.setSessionId(session.getUuid());
        userMessage.setRole("user");
        // 去除用户消息内容首尾可能的双引号
        String processedMessage = message.trim();
        if (processedMessage.startsWith("\"") && processedMessage.endsWith("\"")) {
            processedMessage = processedMessage.substring(1, processedMessage.length() - 1);
        }
        userMessage.setContent(processedMessage);
        userMessage.setMessageOrder(currentMaxOrder + 1);
        userMessage.onCreate();
        messageMapper.insert(userMessage);

        // 添加日志记录，记录当前正在处理的消息
        log.info("正在处理用户消息 - sessionId: {}, message: {}", session.getUuid(), processedMessage);

        // AI回复的消息顺序是 userMessage.getMessageOrder() + 1
        int aiMessageOrder = currentMaxOrder + 2;
        String aiSessionId = session.getUuid();

        // 获取当前表格数据
        final List<List<String>> currentData = parseExcelData(getLatestExcelDataBySession(session));

        // 获取问卷数据
        WjxSurveyData surveyData = surveyDataMapper.findByOrderNumber(session.getUuid());
        List<SurveyData> surveyDataList = surveyData != null ? surveyData.getJsonData() : null;

        // 构建完整的提示词
        String fullPrompt = buildAnalysisPrompt(processedMessage, currentData, surveyDataList, session.getUuid());

        // 用于累积AI的完整响应
        StringBuilder aiResponseBuilder = new StringBuilder();

        // 预先创建一个"等待AI填充"的消息，供工具调用时使用
        AiChatMessage waitingMessage = new AiChatMessage();
        waitingMessage.setSessionId(session.getUuid());
        waitingMessage.setRole("assistant");
        waitingMessage.setContent("等待AI填充");
        waitingMessage.setMessageOrder(aiMessageOrder);
        waitingMessage.onCreate();
        messageMapper.insert(waitingMessage);
        log.info("预先创建等待AI填充的消息，ID={}", waitingMessage.getId());

        return chatClient.prompt()
                .user(fullPrompt)
                .advisors(a -> a.param("conversationId", aiSessionId))
                .stream()
                .content()
                .map(content -> {
                    log.info("AI原始内容: {}", content); // 打印AI返回的原始内容
                    aiResponseBuilder.append(content);
                    AiChatMessage aiMessageChunk = new AiChatMessage();
                    aiMessageChunk.setSessionId(aiSessionId);
                    aiMessageChunk.setRole("assistant");
                    aiMessageChunk.setContent(content);
                    aiMessageChunk.onCreate();
                    return aiMessageChunk;
                })
                .concatWith(Mono.fromCallable(() -> {
                    // 流式响应完成后，保存完整的AI消息到数据库并返回包含表格数据的消息
                    log.info("AI完整内容: {}", aiResponseBuilder.toString());
                    AiChatMessage lastMsg = messageMapper.findLatestWaitingFillBySessionId(session.getUuid());
                    boolean isNew = false;
                    if (lastMsg == null) {
                        lastMsg = new AiChatMessage();
                        lastMsg.setSessionId(session.getUuid());
                        lastMsg.setRole("assistant");
                        lastMsg.onCreate();
                        isNew = true;
                    }
                    lastMsg.setMessageOrder(aiMessageOrder);
                    String aiContent = aiResponseBuilder.toString();
                    try {
                        String jsonToParse = aiContent;
                        if (!aiContent.trim().startsWith("{\"explanation")) {
                            int idx = aiContent.indexOf("{\"explanation");
                            if (idx != -1) {
                                jsonToParse = aiContent.substring(idx);
                            }
                        }
                        JsonNode root = objectMapper.readTree(jsonToParse);
                        String explanation = root.has("explanation") ? root.get("explanation").asText() : aiContent;
                        lastMsg.setContent(explanation);
                        // 获取上一次完整excel
                        AiChatMessage latestMsg = messageMapper.findLatestBySessionId(aiSessionId);
                        List<List<String>> currentExcel = latestMsg != null && latestMsg.getCompleteExcelData() != null
                                ? objectMapper.readValue(latestMsg.getCompleteExcelData(),
                                        new TypeReference<List<List<String>>>() {})
                                : (currentData != null ? currentData : new ArrayList<>());
                        List<List<String>> newExcel = currentExcel;
                        if (root.has("changedCells")) {
                            JsonNode changedCellsNode = root.get("changedCells");
                            // 只有当changedCells不为空时才处理
                            if (changedCellsNode.isArray() && changedCellsNode.size() > 0) {
                                String changedCellsStr = objectMapper.writeValueAsString(changedCellsNode);
                                lastMsg.setDataModifications(changedCellsStr);
                                log.info("[AI响应处理] AI返回了非空的changedCells，更新dataModifications，长度={}", changedCellsStr.length());

                                // 解析changedCells为List<List<Object>>
                                List<List<Object>> changedCells = objectMapper.readValue(changedCellsStr,
                                        new TypeReference<List<List<Object>>>() {});
                                List<AiChatMessage.DataModification> modifications = new ArrayList<>();
                                for (List<Object> cell : changedCells) {
                                    AiChatMessage.DataModification mod = new AiChatMessage.DataModification();
                                    mod.setRow(((Number) cell.get(0)).intValue());
                                    mod.setCol(((Number) cell.get(1)).intValue());
                                    if (cell.size() == 3) {
                                        mod.setOldValue(null);
                                        mod.setNewValue(String.valueOf(cell.get(2)));
                                    } else if (cell.size() > 3) {
                                        mod.setOldValue(String.valueOf(cell.get(2)));
                                        mod.setNewValue(String.valueOf(cell.get(3)));
                                    }
                                    mod.setType("update");
                                    modifications.add(mod);
                                }
                                // 用通用方法计算新Excel
                                newExcel = calculateCompleteExcelData(currentExcel, modifications);
                            } else {
                                log.info("[AI响应处理] AI返回了空的changedCells，保留现有dataModifications，长度={}",
                                        lastMsg.getDataModifications() != null ? lastMsg.getDataModifications().length() : 0);
                            }
                        }
                        // 总是赋值
                        lastMsg.setCompleteExcelData(objectMapper.writeValueAsString(newExcel));

                        // 添加调试日志
                        log.info("[AI响应处理] 正常分支 - 消息ID={}, dataModifications长度={}",
                                lastMsg.getId(),
                                lastMsg.getDataModifications() != null ? lastMsg.getDataModifications().length() : 0);

                    } catch (Exception e) {
                        log.error("AI消息结构解析失败，按原样存储", e);
                        lastMsg.setContent(aiContent);

                        // 保留已有的dataModifications，不要重置为空数组
                        String existingModifications = lastMsg.getDataModifications();
                        log.info("[AI响应处理] 异常处理分支 - 现有dataModifications长度={}",
                                existingModifications != null ? existingModifications.length() : 0);

                        if (existingModifications == null || existingModifications.trim().isEmpty()) {
                            lastMsg.setDataModifications("[]");
                            log.info("[AI响应处理] 设置dataModifications为空数组");
                        } else {
                            log.info("[AI响应处理] 保留现有dataModifications，长度={}", existingModifications.length());
                        }
                        // 如果已有dataModifications，则保持不变

                        // 依然赋值当前Excel
                        try {
                            AiChatMessage fallbackMsg = messageMapper.findLatestBySessionId(aiSessionId);
                            List<List<String>> currentExcel = fallbackMsg != null && fallbackMsg.getCompleteExcelData() != null
                                    ? objectMapper.readValue(fallbackMsg.getCompleteExcelData(),
                                            new TypeReference<List<List<String>>>() {})
                                    : (currentData != null ? currentData : new ArrayList<>());
                            lastMsg.setCompleteExcelData(objectMapper.writeValueAsString(currentExcel));
                        } catch (Exception ex) {
                            log.error("设置完整Excel数据失败", ex);
                        }
                    } finally {
                        try {
                            log.info("[AI响应处理] 准备保存到数据库 - 消息ID={}, isNew={}, dataModifications长度={}",
                                    lastMsg.getId(), isNew,
                                    lastMsg.getDataModifications() != null ? lastMsg.getDataModifications().length() : 0);

                            if (isNew) {
                                messageMapper.insert(lastMsg);
                            } else {
                                messageMapper.update(lastMsg);
                            }

                            // 验证保存后的数据
                            AiChatMessage savedMsg = messageMapper.findMessageById(lastMsg.getId());
                            log.info("[AI响应处理] 保存后验证 - 消息ID={}, dataModifications是否为空={}, 长度={}",
                                    savedMsg.getId(),
                                    savedMsg.getDataModifications() == null || savedMsg.getDataModifications().trim().isEmpty(),
                                    savedMsg.getDataModifications() != null ? savedMsg.getDataModifications().length() : 0);

                            // AI消息成功保存到数据库后，扣除代币
                            try {
                                boolean tokenConsumed = tokenService.consumeToken(session.getTokenCode(), 1);
                                if (tokenConsumed) {
                                    sessionMapper.updateTokenConsumed(sessionId, 1);
                                    log.info("[代币扣除] AI聊天成功，扣除1个代币，会话ID={}", sessionId);
                                } else {
                                    log.warn("[代币扣除] 代币扣除失败，会话ID={}", sessionId);
                                }
                            } catch (Exception tokenError) {
                                log.error("[代币扣除] 代币扣除异常，会话ID={}", sessionId, tokenError);
                            }

                        } catch (Exception e) {
                            log.error("保存AI消息到数据库失败", e);
                        }
                    }
                    // 更新会话的最后消息时间
                    sessionMapper.updateLastMessageTime(session.getId(), LocalDateTime.now(), LocalDateTime.now());

                    // 返回包含表格数据的最终消息，但内容为空以避免重复显示
                    AiChatMessage finalMessage = new AiChatMessage();
                    finalMessage.setSessionId(aiSessionId);
                    finalMessage.setRole("assistant");
                    finalMessage.setContent(""); // 空内容，避免重复显示
                    finalMessage.setTableData(lastMsg.getTableData()); // 传递表格数据
                    finalMessage.onCreate();
                    return finalMessage;
                }))
                .doOnError(e -> {
                    log.error("AI流式生成失败", e);
                    AiChatMessage errorMessage = messageMapper.findLatestWaitingFillBySessionId(session.getUuid());
                    if (errorMessage == null) {
                        errorMessage = new AiChatMessage();
                    }
                    errorMessage.setSessionId(aiSessionId);
                    errorMessage.setRole("system");
                    errorMessage.setContent("AI助手出现错误: " + e.getMessage());
                    errorMessage.setMessageOrder(aiMessageOrder);
                    errorMessage.onCreate();
                    messageMapper.insert(errorMessage);
                });
    }

    @Override
    public byte[] exportExcel(Long sessionId, List<List<String>> data) {
        try {
            log.info("Received data for Excel export for session {}: {}", sessionId, data);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelGenerator.generateExcelFromList(data, outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportExcel(String sessionId) {
        try {
            // 获取会话数据
            AiChatSession session = sessionMapper.findByUuid(sessionId);
            if (session == null) {
                throw new RuntimeException("会话不存在");
            }

            // 解析Excel数据
            List<List<String>> excelData = parseExcelData(getLatestExcelDataBySession(session));

            // 生成Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelGenerator.generateExcelFromList(excelData, outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportExcelByUuid(String uuid, List<List<String>> data) {
        try {
            // 根据UUID查找会话，确保会话存在
            AiChatSession session = sessionMapper.findByUuid(uuid);
            if (session == null) {
                throw new RuntimeException("会话不存在");
            }

            log.info("Received data for Excel export for session {}: {}", uuid, data);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelGenerator.generateExcelFromList(data, outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage());
        }
    }

    @Override
    public void updateExcelData(Long sessionId, List<List<String>> data) {
        try {
            String excelData = objectMapper.writeValueAsString(data);
            sessionMapper.updateExcelData(sessionId, excelData, LocalDateTime.now());
        } catch (Exception e) {
            throw new RuntimeException("更新Excel数据失败: " + e.getMessage());
        }
    }


    @Override
    public List<AiChatMessage> getMessages(Long sessionId) {
        AiChatSession session = sessionMapper.findById(sessionId);
        if (session == null) {
            throw new RuntimeException("会话不存在");
        }
        return messageMapper.findBySessionId(session.getUuid());
    }

    @Override
    public AiChatSession getSessionByUuid(String uuid) {
        AiChatSession session = sessionMapper.findByUuid(uuid);
        if (session == null)
            return null;
        // 优先从消息表找最新的complete_excel_data
        AiChatMessage latestMsg = findLatestAssistantMessageWithExcel(uuid);
        if (latestMsg != null && latestMsg.getCompleteExcelData() != null
                && !latestMsg.getCompleteExcelData().isEmpty()) {
            session.setExcelData(latestMsg.getCompleteExcelData());
        }
        return session;
    }

    @Override
    public void renameSession(String uuid, String newName) {
        AiChatSession session = sessionMapper.findByUuid(uuid);
        if (session == null) {
            throw new RuntimeException("会话不存在");
        }
        session.setSessionName(newName);
        session.setUpdateTime(LocalDateTime.now());
        sessionMapper.updateSessionName(session);
    }

    @Override
    public void deleteSession(String uuid) {
        AiChatSession session = sessionMapper.findByUuid(uuid);
        if (session == null) {
            throw new RuntimeException("会话不存在");
        }
        session.setIsDeleted(true);
        session.setUpdateTime(LocalDateTime.now());
        sessionMapper.updateSessionDeleted(session);
    }

    private String buildAnalysisPrompt(String userMessage, List<List<String>> currentData,
            List<SurveyData> surveyData, String sessionId) {
        StringBuilder prompt = new StringBuilder();
        // 默认添加最近6次对话内容
        List<AiChatMessage> recentMsgs = messageMapper.findBySessionId(sessionId);
        // 只保留user/assistant消息，按messageOrder正序
        List<AiChatMessage> dialogMsgs = recentMsgs.stream()
                .filter(m -> "user".equals(m.getRole()) || "assistant".equals(m.getRole()))
                .sorted((a, b) -> Integer.compare(a.getMessageOrder() == null ? 0 : a.getMessageOrder(), b.getMessageOrder() == null ? 0 : b.getMessageOrder()))
                .collect(Collectors.toList());
        int startIdx = Math.max(0, dialogMsgs.size() - 6);
        List<AiChatMessage> last6 = dialogMsgs.subList(startIdx, dialogMsgs.size());
        if (!last6.isEmpty()) {
            prompt.append("【最近6次对话聊天记录】\n");
            int idx = 1;
            for (int i = 0; i < last6.size(); i++) {
                AiChatMessage msg = last6.get(i);
                if ("user".equals(msg.getRole())) {
                    prompt.append(idx).append(". 用户：").append(msg.getContent() == null ? "" : msg.getContent().replaceAll("\n", " ")).append("\n");
                    // 查找下一个assistant回复
                    if (i + 1 < last6.size() && "assistant".equals(last6.get(i + 1).getRole())) {
                        AiChatMessage assistantMsg = last6.get(i + 1);
                        prompt.append("   助手：").append(assistantMsg.getContent() == null ? "" : assistantMsg.getContent().replaceAll("\n", " ")).append("\n");
                        // 新增：输出changedCells（data_modifications字段）
                        if (assistantMsg.getDataModifications() != null && !assistantMsg.getDataModifications().trim().isEmpty()) {
                            prompt.append("   修改内容：").append(assistantMsg.getDataModifications().replaceAll("\n", " ")).append("\n");
                        }
                        i++; // 跳过已配对的assistant
                    }
                    idx++;
                }
            }
        }
        // 如果需要查看历史记录，AI可以调用 getChatHistory 工具
        // 获取会话信息
        AiChatSession session = sessionMapper.findByUuid(sessionId);
        String surveyInfoText;
        surveyInfoText = formatSurveyDataForPrompt(surveyData);
        prompt.append(surveyInfoText);
        prompt.append("\n注意：上面问卷结构详细信息仅供你理解，禁止原样输出给用户。用户只关心具体解释和结论。\n");

        // 明确告知sessionId
        prompt.append("\n\n当前会话ID（sessionId）：").append(sessionId).append("\n");
        prompt.append("\n\n本次用户需求：").append(userMessage);
        prompt.append("\n请根据给出的条件信息帮助用户解决问题");
        prompt.append("\n\n如果需要了解对话历史，可以使用 getChatHistory 工具获取聊天记录。");

        // 添加日志记录，记录构建提示词时使用的消息
        log.info("构建提示词 - sessionId: {}, userMessage: {}", sessionId, userMessage);

        return prompt.toString();
    }

    // 添加缺失的方法
    private static List<List<String>> parseExcelData(String excelDataJson) {
        if (excelDataJson == null)
            return new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(excelDataJson,
                    mapper.getTypeFactory().constructCollectionType(List.class, List.class));
        } catch (Exception e) {
            log.error("解析Excel数据失败", e);
            return new ArrayList<>();
        }
    }


    // 添加获取数据修改历史的方法
    public List<CellChange> getCellChanges(String sessionId) {
        return sessionCellChanges.getOrDefault(sessionId, new ArrayList<>());
    }

    @Override
    public List<AiChatMessage> getSessionMessages(String sessionId) {
        return messageMapper.findBySessionId(sessionId);
    }

    @Override
    public void applyDataModification(String sessionId, AiChatMessage.DataModification modification) {
        List<CellChange> changes = sessionCellChanges.computeIfAbsent(sessionId, k -> new ArrayList<>());
        changes.add(new CellChange(
                modification.getRow(),
                modification.getCol(),
                modification.getOldValue(),
                modification.getNewValue(),
                modification.getModifyTime(),
                "用户修改"));
    }

    @Override
    public List<AiChatMessage.DataModification> getSessionDataModifications(String sessionId) {
        List<CellChange> changes = sessionCellChanges.getOrDefault(sessionId, new ArrayList<>());
        return changes.stream()
                .map(change -> {
                    AiChatMessage.DataModification mod = new AiChatMessage.DataModification();
                    mod.setRow(change.getRow());
                    mod.setCol(change.getCol());
                    mod.setOldValue(change.getOldValue());
                    mod.setNewValue(change.getNewValue());
                    mod.setModifyTime(change.getChangeTime());
                    return mod;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getSessionContext(String sessionId) {
        // 不需要实现
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getSessionDataState(String sessionId) {
        Map<String, Object> state = new HashMap<>();
        AiChatSession session = sessionMapper.findByUuid(sessionId);
        if (session != null) {
            state.put("excelData", getLatestExcelDataBySession(session));
            state.put("modifications", getSessionDataModifications(sessionId));
        }
        return state;
    }

   
    @Override
    public String getLatestExcelData(String sessionId) {
        try {
            AiChatSession session = sessionMapper.findByUuid(sessionId);
            if (session == null) {
                throw new RuntimeException("会话不存在");
            }
            return getLatestExcelDataBySession(session);
        } catch (Exception e) {
            log.error("获取最新Excel数据失败", e);
            throw new RuntimeException("获取最新Excel数据失败: " + e.getMessage());
        }
    }

    @Override
    public void processDataModifications(String sessionId, List<AiChatMessage.DataModification> modifications) {
        AiChatSession session = sessionMapper.findByUuid(sessionId);
        if (session == null)
            throw new RuntimeException("会话不存在");

        // 1. 获取最新完整数据
        List<List<String>> excelData = parseExcelData(getLatestExcelDataBySession(session));

        // 2. 应用本次修改
        for (AiChatMessage.DataModification mod : modifications) {
            if ("update".equals(mod.getType())) {
                excelData.get(mod.getRow()).set(mod.getCol(), mod.getNewValue());
            } else if ("addRow".equals(mod.getType())) {
                List<String> newRow = new com.google.gson.Gson().fromJson(mod.getNewValue(),
                        new com.google.gson.reflect.TypeToken<List<String>>() {
                        }.getType());
                excelData.add(newRow);
            } else if ("deleteRow".equals(mod.getType())) {
                excelData.remove((int) mod.getRow());
            }
        }

        // 3. 存储消息
        AiChatMessage message = new AiChatMessage();
        message.setSessionId(sessionId);
        message.setRole("system");
        message.setContent("数据已调整");
        message.setCreateTime(java.time.LocalDateTime.now());
        message.setDataModifications(new com.google.gson.Gson().toJson(modifications));
        message.setCompleteExcelData(new com.google.gson.Gson().toJson(excelData));
        messageMapper.insert(message);

        // 4. 更新会话的完整数据
        session.setExcelData(new com.google.gson.Gson().toJson(excelData));
        sessionMapper.updateExcelData(session.getId(), session.getExcelData(), java.time.LocalDateTime.now());
    }

    @Override
    public void saveMessage(AiChatMessage message) {
        try {
            // 对于系统消息或简单文本消息，直接保存
            if ("system".equals(message.getRole()) || !isJsonContent(message.getContent())) {
                messageMapper.insert(message);
                return;
            }

            // 解析消息内容中的JSON（仅对包含JSON的消息）
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(message.getContent());

            if (jsonNode.has("changedCells")) {
                // 提取数据修改
                List<AiChatMessage.DataModification> modifications = new ArrayList<>();
                JsonNode changedCells = jsonNode.get("changedCells");
                for (JsonNode cell : changedCells) {
                    AiChatMessage.DataModification mod = new AiChatMessage.DataModification();
                    mod.setRow(cell.get("row").asInt());
                    mod.setCol(cell.get("col").asInt());
                    mod.setOldValue(cell.has("oldValue") ? cell.get("oldValue").asText() : null);
                    mod.setNewValue(cell.has("newValue") ? cell.get("newValue").asText() : null);
                    mod.setType(cell.get("type").asText());
                    mod.setModifyTime(LocalDateTime.now());
                    modifications.add(mod);
                }
                message.setDataModifications(objectMapper.writeValueAsString(modifications));

                // 获取最新的Excel数据
                AiChatMessage latestMessage = messageMapper.findLatestBySessionId(message.getSessionId());
                List<List<String>> currentData = latestMessage != null
                        ? parseExcelData(latestMessage.getCompleteExcelData())
                        : new ArrayList<>();

                // 计算新的完整Excel数据
                List<List<String>> newData = calculateCompleteExcelData(currentData, modifications);
                message.setCompleteExcelData(mapper.writeValueAsString(newData));
            }

            // 保存消息
            messageMapper.insert(message);

            // 如果有数据修改，更新相关字段
            if (message.getDataModifications() != null) {
                messageMapper.updateDataModifications(message.getId(), message.getDataModifications());
                messageMapper.updateCompleteExcelData(message.getId(), message.getCompleteExcelData());
            }
        } catch (Exception e) {
            log.error("保存消息失败", e);
            throw new RuntimeException("保存消息失败: " + e.getMessage());
        }
    }

    /**
     * 检查内容是否为JSON格式
     */
    private boolean isJsonContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }

        String trimmed = content.trim();
        return (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
               (trimmed.startsWith("[") && trimmed.endsWith("]"));
    }

    @Override
    public List<AiChatMessage> getSessionContext(String sessionId, int limit) {
        try {
            return messageMapper.findBySessionId(sessionId).stream()
                    .limit(limit)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取会话上下文失败", e);
            throw new RuntimeException("获取会话上下文失败: " + e.getMessage());
        }
    }

    // 计算完整的Excel数据
    private List<List<String>> calculateCompleteExcelData(List<List<String>> currentData,
            List<AiChatMessage.DataModification> modifications) {
        if (modifications == null || modifications.isEmpty()) {
            return currentData;
        }

        // 创建数据副本
        List<List<String>> newData = new ArrayList<>();
        for (List<String> row : currentData) {
            newData.add(new ArrayList<>(row));
        }

        // 应用修改
        for (AiChatMessage.DataModification mod : modifications) {
            // 先扩展行，且新行补齐列数
            while (newData.size() <= mod.getRow()) {
                int colCount = newData.isEmpty() ? (mod.getCol() + 1) : newData.get(0).size();
                List<String> newRow = new ArrayList<>();
                for (int i = 0; i < colCount; i++) {
                    newRow.add("");
                }
                newData.add(newRow);
            }
            // 先扩展列
            while (newData.get(mod.getRow()).size() <= mod.getCol()) {
                newData.get(mod.getRow()).add("");
            }

            if ("update".equals(mod.getType()) || "insert".equals(mod.getType())) {
                newData.get(mod.getRow()).set(mod.getCol(), mod.getNewValue());
            } else if ("delete".equals(mod.getType())) {
                newData.get(mod.getRow()).set(mod.getCol(), "");
            }
        }

        // 删除所有全为空的行（不含表头）
        for (int i = newData.size() - 1; i >= 1; i--) { // i=1是首条数据，0是表头
            List<String> row = newData.get(i);
            boolean allEmpty = row.stream().allMatch(cell -> cell == null || cell.isEmpty());
            if (allEmpty) {
                newData.remove(i);
            }
        }
        return newData;
    }

    @Override
    public AiChatMessage findLatestAssistantMessageWithExcel(String sessionId) {
        AiChatMessage msg = messageMapper.findLatestBySessionId(sessionId);
        if (msg == null || msg.getCompleteExcelData() == null || msg.getCompleteExcelData().isEmpty()) {
            // 查不到则从session表获取原始excelData
            AiChatSession session = sessionMapper.findByUuid(sessionId);
            if (session != null && session.getExcelData() != null && !session.getExcelData().isEmpty()) {
                AiChatMessage fallback = new AiChatMessage();
                fallback.setSessionId(sessionId);
                fallback.setRole("assistant");
                fallback.setCompleteExcelData(session.getExcelData());
                return fallback;
            }
            return null;
        }
        return msg;
    }

    // 工具方法：优先获取最新Excel数据
    private String getLatestExcelDataBySession(AiChatSession session) {
        if (session == null)
            return null;
        AiChatMessage latestMsg = findLatestAssistantMessageWithExcel(session.getUuid());
        if (latestMsg != null && latestMsg.getCompleteExcelData() != null
                && !latestMsg.getCompleteExcelData().isEmpty()) {
            return latestMsg.getCompleteExcelData();
        }
        return session.getExcelData();
    }

    /**
     * 获取会话的所有Excel数据版本历史
     */
    @Override
    public List<Map<String, Object>> getExcelVersionHistory(String sessionId) {
        // 获取所有包含数据修改的消息
        List<AiChatMessage> messages = messageMapper.findBySessionIdAndHasModifications(sessionId);
        List<Map<String, Object>> result = new ArrayList<>();
        DateTimeFormatter isoFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

        // 添加AI修改的版本
        for (AiChatMessage msg : messages) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", msg.getId());
            // ISO格式时间
            if (msg.getCreateTime() != null) {
                String isoTime = msg.getCreateTime().atZone(ZoneId.systemDefault()).format(isoFormatter);
                map.put("createTime", isoTime);
            } else {
                map.put("createTime", "");
            }
            map.put("dataModifications", msg.getDataModifications());
            map.put("content", msg.getContent());
            map.put("isInitialVersion", false);
            result.add(map);
        }

        // 添加初始版本（从session表获取）
        AiChatSession session = sessionMapper.findByUuid(sessionId);
        if (session != null && session.getExcelData() != null && !session.getExcelData().isEmpty()) {
            Map<String, Object> initialMap = new HashMap<>();
            // 使用一个特殊的ID来标识初始版本
            initialMap.put("id", "initial_" + session.getId());
            // 使用会话创建时间
            if (session.getCreateTime() != null) {
                String isoTime = session.getCreateTime().atZone(ZoneId.systemDefault()).format(isoFormatter);
                initialMap.put("createTime", isoTime);
            } else {
                initialMap.put("createTime", "");
            }
            initialMap.put("dataModifications", "[]"); // 初始版本没有修改记录
            initialMap.put("content", "初始上传的数据");
            initialMap.put("isInitialVersion", true);
            result.add(initialMap);
        }

        return result;
    }

    /**
     * 获取指定版本的Excel数据
     */
    @Override
    public String getExcelVersionData(String sessionId, Long messageId) {
        AiChatMessage message = messageMapper.findById(messageId);
        if (message == null || !sessionId.equals(message.getSessionId())) {
            throw new RuntimeException("未找到指定的版本数据");
        }
        if (message.getDataModifications() == null || "[]".equals(message.getDataModifications())) {
            throw new RuntimeException("该消息没有数据修改记录");
        }
        return message.getCompleteExcelData();
    }

    /**
     * 恢复指定版本的数据为当前版本
     */
    @Override
    public void restoreExcelVersion(String sessionId, Long messageId) {
        // 获取要恢复的版本数据
        AiChatMessage sourceMessage = messageMapper.findById(messageId);
        if (sourceMessage == null || !sessionId.equals(sourceMessage.getSessionId())) {
            throw new RuntimeException("未找到指定的版本数据");
        }
        if (sourceMessage.getDataModifications() == null || "[]".equals(sourceMessage.getDataModifications())) {
            throw new RuntimeException("该消息没有数据修改记录");
        }
        // 获取当前会话的最新消息（messageOrder最大）
        AiChatMessage currentMessage = findLatestAssistantMessageWithExcel(sessionId);
        if (currentMessage == null) {
            throw new RuntimeException("未找到当前版本数据");
        }
        // 格式化原消息时间
        String timeStr = "";
        if (sourceMessage.getCreateTime() != null) {
            timeStr = sourceMessage.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        // 创建新的消息记录，包含恢复的数据
        AiChatMessage newMessage = new AiChatMessage();
        newMessage.setSessionId(sessionId);
        newMessage.setRole("system");
        newMessage.setContent("已将" + timeStr + "的历史版本恢复为最新版本");
        newMessage.setCompleteExcelData(sourceMessage.getCompleteExcelData());
        newMessage.setDataModifications(sourceMessage.getDataModifications());
        newMessage.setMessageOrder(currentMessage.getMessageOrder() + 1);
        newMessage.onCreate();
        // 不再更新当前版本的isLast字段
        // 保存新版本
        messageMapper.insert(newMessage);
    }

    /**
     * 恢复初始版本的数据为当前版本
     */
    @Override
    public void restoreInitialExcelVersion(String sessionId) {
        // 获取会话信息
        AiChatSession session = sessionMapper.findByUuid(sessionId);
        if (session == null) {
            throw new RuntimeException("未找到指定的会话");
        }
        if (session.getExcelData() == null || session.getExcelData().isEmpty()) {
            throw new RuntimeException("会话中没有初始Excel数据");
        }

        // 获取当前最新的Excel数据
        AiChatMessage currentMessage = findLatestAssistantMessageWithExcel(sessionId);
        if (currentMessage == null) {
            throw new RuntimeException("未找到当前版本数据");
        }

        // 使用正确的方法获取下一个消息排序号
        int nextOrder = messageMapper.getMaxMessageOrder(sessionId) + 1;

        // 格式化会话创建时间
        String timeStr = "";
        if (session.getCreateTime() != null) {
            timeStr = session.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        try {
            // 解析当前数据和初始数据，计算差异
            List<List<String>> currentData = objectMapper.readValue(currentMessage.getCompleteExcelData(),
                new TypeReference<List<List<String>>>() {});
            List<List<String>> initialData = objectMapper.readValue(session.getExcelData(),
                new TypeReference<List<List<String>>>() {});

            // 生成数据修改记录（从当前版本到初始版本的变化）
            List<List<Object>> modifications = generateDataModifications(currentData, initialData);
            String modificationsJson = objectMapper.writeValueAsString(modifications);

            // 创建新的消息记录，包含恢复的初始数据
            AiChatMessage newMessage = new AiChatMessage();
            newMessage.setSessionId(sessionId);
            newMessage.setRole("system");
            newMessage.setContent("已将" + timeStr + "的初始版本恢复为最新版本");
            newMessage.setCompleteExcelData(session.getExcelData());
            newMessage.setDataModifications(modificationsJson);
            newMessage.setMessageOrder(nextOrder);
            newMessage.onCreate();

            // 保存新版本
            messageMapper.insert(newMessage);

        } catch (Exception e) {
            log.error("恢复初始版本失败", e);
            throw new RuntimeException("恢复初始版本失败: " + e.getMessage());
        }
    }

    /**
     * 生成数据修改记录（比较两个数据版本的差异）
     */
    private List<List<Object>> generateDataModifications(List<List<String>> fromData, List<List<String>> toData) {
        List<List<Object>> modifications = new ArrayList<>();

        int maxRows = Math.max(fromData.size(), toData.size());

        for (int row = 0; row < maxRows; row++) {
            List<String> fromRow = row < fromData.size() ? fromData.get(row) : new ArrayList<>();
            List<String> toRow = row < toData.size() ? toData.get(row) : new ArrayList<>();

            int maxCols = Math.max(fromRow.size(), toRow.size());

            for (int col = 0; col < maxCols; col++) {
                String fromValue = col < fromRow.size() ? fromRow.get(col) : "";
                String toValue = col < toRow.size() ? toRow.get(col) : "";

                // 如果值不同，记录修改
                if (!Objects.equals(fromValue, toValue)) {
                    List<Object> modification = new ArrayList<>();
                    modification.add(row);  // 行索引（0-based）
                    modification.add(col);  // 列索引（0-based）
                    modification.add(toValue);  // 新值
                    modifications.add(modification);
                }
            }
        }

        return modifications;
    }

    @Override
    public Map<String, Object> getExcelVersionDataWithModifications(String sessionId, String messageId) {
        Map<String, Object> result = new HashMap<>();

        // 检查是否是初始版本的特殊ID
        if (messageId.startsWith("initial_")) {
            // 处理初始版本，从session表获取数据
            AiChatSession session = sessionMapper.findByUuid(sessionId);
            if (session == null) {
                throw new RuntimeException("未找到指定的会话");
            }
            result.put("excelData", session.getExcelData());
            result.put("dataModifications", "[]"); // 初始版本没有修改记录
            return result;
        }

        // 处理普通的消息版本
        Long messageIdLong;
        try {
            messageIdLong = Long.parseLong(messageId);
        } catch (NumberFormatException e) {
            throw new RuntimeException("无效的消息ID格式");
        }

        AiChatMessage message = messageMapper.findById(messageIdLong);
        if (message == null || !sessionId.equals(message.getSessionId())) {
            throw new RuntimeException("未找到指定的版本数据");
        }
        result.put("excelData", message.getCompleteExcelData());
        result.put("dataModifications", message.getDataModifications());
        return result;
    }


    /**
     * 将Excel列字母（如A, B, AA）转换为1-based的数字索引
     * 
     * @param columnAlphabets 列字母字符串
     * @return 1-based的数字索引
     */
    private int parseColumn(String columnAlphabets) {
        int col = 0;
        for (char c : columnAlphabets.toUpperCase().toCharArray()) {
            col = col * 26 + (c - 'A' + 1);
        }
        return col;
    }

    /**
     * 格式化问卷详细信息以便添加到AI提示词中
     * 
     * @param surveyData 问卷数据列表
     * @return 格式化的问卷信息字符串
     */
    private String formatSurveyDataForPrompt(List<SurveyData> surveyData) {
        if (surveyData == null || surveyData.isEmpty()) {
            return "问卷详细信息：无（重要报错！！！！！！！！！！！！！！！）";
        }

        try {
            // 创建不包含html字段的简化对象列表，字段顺序严格按照promptBuilder
            List<Map<String, Object>> simplifiedData = new ArrayList<>();
            for (SurveyData data : surveyData) {
                Map<String, Object> simplified = new LinkedHashMap<>();
                simplified.put("numId", data.getNumId());
                simplified.put("title", data.getTitle());
                simplified.put("type", data.getType());
                simplified.put("typeInfo", data.getTypeInfo());
                simplified.put("options", data.getOptions());

                // 过滤掉subQuestions中的null值
                if (data.getSubQuestions() != null) {
                    List<SurveyData.SubQuestion> filteredSubQuestions = data.getSubQuestions().stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    simplified.put("subQuestions", filteredSubQuestions.isEmpty() ? null : filteredSubQuestions);
                } else {
                    simplified.put("subQuestions", null);
                }

                simplified.put("colIndices", data.getColIndices());
                simplifiedData.add(simplified);
            }

            // 直接以JSON格式输出问卷信息（不包含html字段）
            String surveyDataJson = objectMapper.writeValueAsString(simplifiedData);

            StringBuilder promptBuilder = new StringBuilder();
            promptBuilder.append("【仅供AI理解，禁止原样输出给用户】\n");
            promptBuilder.append("问卷题目结构详细信息说明：\n");
            promptBuilder.append("这是一个JSON数组，每个对象代表一道题目，包含以下字段：\n");
            promptBuilder.append("- numId: 题号，从1开始。\n");
            promptBuilder.append("- title: 题目标题。\n");
            promptBuilder.append("- type: 题型编号，例如 '3' 代表单选题。\n");
            promptBuilder.append("- typeInfo: 题型中文名称，例如 '单选题'。\n");
            promptBuilder.append("- options: 题目的选项列表。对于单选、多选、排序题等，这里会列出所有选项的文本。\n");
            promptBuilder.append("- subQuestions: 仅用于矩阵题，包含多个小题。每个小题有自己的 'title' 和 'options'。\n");
            promptBuilder.append("- colIndices: 数组，表示该题目在Excel数据中对应的列号（从1开始）。单选题通常只有一个列号，多选题或矩阵题有多个。\n\n");
            promptBuilder.append("问卷题目结构详细信息JSON：\n");
            promptBuilder.append(surveyDataJson);
            promptBuilder.append("\n【仅供AI理解，禁止原样输出给用户】\n");
            return promptBuilder.toString();
        } catch (Exception e) {
            log.error("序列化问卷数据失败", e);
            return "问卷详细信息：序列化失败";
        }
    }

    @Override
    public AiChatMessage getLatestAssistantMessage(String sessionId) {
        return messageMapper.findLatestAssistantMessage(sessionId);
    }

    /**
     * 根据会话ID获取问卷数据
     * @param sessionId 会话ID
     * @return 问卷数据
     */
    public WjxSurveyData getSurveyDataBySessionId(String sessionId) {
        try {
            return surveyDataMapper.findByOrderNumber(sessionId);
        } catch (Exception e) {
            log.error("获取问卷数据失败，sessionId: {}", sessionId, e);
            return null;
        }
    }
}
package com.example.springboot.handler;

import com.example.springboot.entity.SurveyData;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class SurveyDataListTypeHandler extends BaseTypeHandler<List<SurveyData>> {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<SurveyData> parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, parameter == null ? null : objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new SQLException("JSON 序列化失败", e);
        }
    }

    @Override
    public List<SurveyData> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return rs.wasNull() ? null : parseJson(json); // 关键修改：检查 wasNull()
    }

    // 其他两个 getNullableResult 方法保持相同逻辑
    @Override
    public List<SurveyData> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return rs.wasNull() ? null : parseJson(json);
    }

    @Override
    public List<SurveyData> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return cs.wasNull() ? null : parseJson(json);
    }

    private List<SurveyData> parseJson(String json) {
        try {
            if (json == null || json.trim().isEmpty()) {
                return null;
            }
            
            List<SurveyData> surveyDataList = objectMapper.readValue(json,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, SurveyData.class));
            
            // 过滤掉subQuestions中的null值
            if (surveyDataList != null) {
                for (SurveyData surveyData : surveyDataList) {
                    if (surveyData.getSubQuestions() != null) {
                        List<SurveyData.SubQuestion> filteredSubQuestions = surveyData.getSubQuestions().stream()
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        surveyData.setSubQuestions(filteredSubQuestions.isEmpty() ? null : filteredSubQuestions);
                    }
                }
            }
            
            return surveyDataList;
        } catch (Exception e) {
            throw new RuntimeException("JSON 反序列化失败", e);
        }
    }
}

package com.example.springboot.entity;

import lombok.Data;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Data
public class WJXOrder {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // ID

    @Column(unique = true, nullable = false, length = 255)
    private String orderNumber; // 订单号

    @Column(nullable = false)
    private LocalDateTime createdTime; // 创建时间

    @Column(nullable = false)
    private Integer  targetCount; // 目标份数

    @Column(nullable = false, columnDefinition = "int default 0")
    private Integer  realCompletedCount = 0; // 真实刷完问卷的完成份数

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private boolean isRealCompleted = false; // 是否完成订单

    @Column(nullable = false, columnDefinition = "int default 0")
    private Integer  completedCount = 0; // submitdata的完成份数

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private boolean isCompleted = false; // 是否完成订单

    @Column(nullable = false, length = 255)
    private String surveyLink; // 问卷链接

    @Column(nullable = false, length = 255)
    private String tokenValue; // Token令牌

    @Lob
    @Column(columnDefinition = "MEDIUMTEXT")
    private String jsText; // 存储JS脚本内容

    @Column(length = 255)
    private String ipArea; // 换IP地区，如果为空表示不换IP

    private Integer fensanLevel; // 分散提交等级

    @Column(length = 255)
    private String sourceBili; // 来源比例

    @Column(length = 255)
    private String tianxieTime; // 填写用时，例如 "100,200"

    private Integer orderStatus; // 订单状态，-1表示不正常，正常是1,2,3,4...

    private String remark; // 备注

    private String message; // 给用户看的提示信息

    @Column(length = 255)
    private String threadLock; // python生成submitdata时候使用的锁

    private Double price;

    private Double refundAmount;

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private boolean isPay = false; // 是否完成订单

    @Column(length = 255)
    private String tradeNo; // 支付宝订单号

    @Column(length = 64)
    private String activationCode; // 备注

    @Lob
    @Column(columnDefinition = "TEXT")
    private String biliData; // 导出的比例数据 (TEXT 类型)

    private Integer type; // 订单类型

    private Integer remainingRebootCount = 3; // 剩余重启次数，默认3次

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private boolean isDeleted = false; // 是否完成订单

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private boolean isRefund = false; // 是否退款

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private boolean isSettled = false; // 是否清算

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private boolean isNeedPause = false; // 是否生成完答案先暂停

    private Integer exportExcelCount = 0; // 导出EXCEL数据次数


    @Column(length = 50)
    private String payType; // 支付宝：alipay 微信支付：wxpay 代币支付：dbpay

    @PrePersist
    protected void onCreate() {
        this.createdTime = LocalDateTime.now();
    }

    // Getters and setters will be auto-generated by Lombok's @Data
}

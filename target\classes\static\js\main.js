var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.createTemplateTagFirstArg=function(c){return c.raw=c};$jscomp.createTemplateTagFirstArgWithRaw=function(c,k){c.raw=k;return c};$jscomp.arrayIteratorImpl=function(c){var k=0;return function(){return k<c.length?{done:!1,value:c[k++]}:{done:!0}}};$jscomp.arrayIterator=function(c){return{next:$jscomp.arrayIteratorImpl(c)}};
$jscomp.makeIterator=function(c){var k="undefined"!=typeof Symbol&&Symbol.iterator&&c[Symbol.iterator];if(k)return k.call(c);if("number"==typeof c.length)return $jscomp.arrayIterator(c);throw Error(String(c)+" is not an iterable or ArrayLike");};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(c,k,n){if(c==Array.prototype||c==Object.prototype)return c;c[k]=n.value;return c};$jscomp.getGlobal=function(c){c=["object"==typeof globalThis&&globalThis,c,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var k=0;k<c.length;++k){var n=c[k];if(n&&n.Math==Math)return n}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(c,k,n){if(!n||null!=c){n=$jscomp.propertyToPolyfillSymbol[k];if(null==n)return c[k];n=c[n];return void 0!==n?n:c[k]}};
$jscomp.polyfill=function(c,k,n,A){k&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(c,k,n,A):$jscomp.polyfillUnisolated(c,k,n,A))};$jscomp.polyfillUnisolated=function(c,k,n,A){n=$jscomp.global;c=c.split(".");for(A=0;A<c.length-1;A++){var H=c[A];if(!(H in n))return;n=n[H]}c=c[c.length-1];A=n[c];k=k(A);k!=A&&null!=k&&$jscomp.defineProperty(n,c,{configurable:!0,writable:!0,value:k})};
$jscomp.polyfillIsolated=function(c,k,n,A){var H=c.split(".");c=1===H.length;A=H[0];A=!c&&A in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var S=0;S<H.length-1;S++){var U=H[S];if(!(U in A))return;A=A[U]}H=H[H.length-1];n=$jscomp.IS_SYMBOL_NATIVE&&"es6"===n?A[H]:null;k=k(n);null!=k&&(c?$jscomp.defineProperty($jscomp.polyfills,H,{configurable:!0,writable:!0,value:k}):k!==n&&(void 0===$jscomp.propertyToPolyfillSymbol[H]&&(n=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[H]=$jscomp.IS_SYMBOL_NATIVE?
    $jscomp.global.Symbol(H):$jscomp.POLYFILL_PREFIX+n+"$"+H),$jscomp.defineProperty(A,$jscomp.propertyToPolyfillSymbol[H],{configurable:!0,writable:!0,value:k})))};$jscomp.underscoreProtoCanBeSet=function(){var c={a:!0},k={};try{return k.__proto__=c,k.a}catch(n){}return!1};
$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(c,k){c.__proto__=k;if(c.__proto__!==k)throw new TypeError(c+" is not extensible");return c}:null;$jscomp.generator={};$jscomp.generator.ensureIteratorResultIsObject_=function(c){if(!(c instanceof Object))throw new TypeError("Iterator result "+c+" is not an object");};
$jscomp.generator.Context=function(){this.isRunning_=!1;this.yieldAllIterator_=null;this.yieldResult=void 0;this.nextAddress=1;this.finallyAddress_=this.catchAddress_=0;this.finallyContexts_=this.abruptCompletion_=null};$jscomp.generator.Context.prototype.start_=function(){if(this.isRunning_)throw new TypeError("Generator is already running");this.isRunning_=!0};$jscomp.generator.Context.prototype.stop_=function(){this.isRunning_=!1};
$jscomp.generator.Context.prototype.jumpToErrorHandler_=function(){this.nextAddress=this.catchAddress_||this.finallyAddress_};$jscomp.generator.Context.prototype.next_=function(c){this.yieldResult=c};$jscomp.generator.Context.prototype.throw_=function(c){this.abruptCompletion_={exception:c,isException:!0};this.jumpToErrorHandler_()};$jscomp.generator.Context.prototype["return"]=function(c){this.abruptCompletion_={"return":c};this.nextAddress=this.finallyAddress_};
$jscomp.generator.Context.prototype.jumpThroughFinallyBlocks=function(c){this.abruptCompletion_={jumpTo:c};this.nextAddress=this.finallyAddress_};$jscomp.generator.Context.prototype.yield=function(c,k){this.nextAddress=k;return{value:c}};$jscomp.generator.Context.prototype.yieldAll=function(c,k){var n=$jscomp.makeIterator(c),A=n.next();$jscomp.generator.ensureIteratorResultIsObject_(A);if(A.done)this.yieldResult=A.value,this.nextAddress=k;else return this.yieldAllIterator_=n,this.yield(A.value,k)};
$jscomp.generator.Context.prototype.jumpTo=function(c){this.nextAddress=c};$jscomp.generator.Context.prototype.jumpToEnd=function(){this.nextAddress=0};$jscomp.generator.Context.prototype.setCatchFinallyBlocks=function(c,k){this.catchAddress_=c;void 0!=k&&(this.finallyAddress_=k)};$jscomp.generator.Context.prototype.setFinallyBlock=function(c){this.catchAddress_=0;this.finallyAddress_=c||0};$jscomp.generator.Context.prototype.leaveTryBlock=function(c,k){this.nextAddress=c;this.catchAddress_=k||0};
$jscomp.generator.Context.prototype.enterCatchBlock=function(c){this.catchAddress_=c||0;c=this.abruptCompletion_.exception;this.abruptCompletion_=null;return c};$jscomp.generator.Context.prototype.enterFinallyBlock=function(c,k,n){n?this.finallyContexts_[n]=this.abruptCompletion_:this.finallyContexts_=[this.abruptCompletion_];this.catchAddress_=c||0;this.finallyAddress_=k||0};
$jscomp.generator.Context.prototype.leaveFinallyBlock=function(c,k){var n=this.finallyContexts_.splice(k||0)[0];if(n=this.abruptCompletion_=this.abruptCompletion_||n){if(n.isException)return this.jumpToErrorHandler_();void 0!=n.jumpTo&&this.finallyAddress_<n.jumpTo?(this.nextAddress=n.jumpTo,this.abruptCompletion_=null):this.nextAddress=this.finallyAddress_}else this.nextAddress=c};$jscomp.generator.Context.prototype.forIn=function(c){return new $jscomp.generator.Context.PropertyIterator(c)};
$jscomp.generator.Context.PropertyIterator=function(c){this.object_=c;this.properties_=[];for(var k in c)this.properties_.push(k);this.properties_.reverse()};$jscomp.generator.Context.PropertyIterator.prototype.getNext=function(){for(;0<this.properties_.length;){var c=this.properties_.pop();if(c in this.object_)return c}return null};$jscomp.generator.Engine_=function(c){this.context_=new $jscomp.generator.Context;this.program_=c};
$jscomp.generator.Engine_.prototype.next_=function(c){this.context_.start_();if(this.context_.yieldAllIterator_)return this.yieldAllStep_(this.context_.yieldAllIterator_.next,c,this.context_.next_);this.context_.next_(c);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.return_=function(c){this.context_.start_();var k=this.context_.yieldAllIterator_;if(k)return this.yieldAllStep_("return"in k?k["return"]:function(n){return{value:n,done:!0}},c,this.context_["return"]);this.context_["return"](c);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.throw_=function(c){this.context_.start_();if(this.context_.yieldAllIterator_)return this.yieldAllStep_(this.context_.yieldAllIterator_["throw"],c,this.context_.next_);this.context_.throw_(c);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.yieldAllStep_=function(c,k,n){try{var A=c.call(this.context_.yieldAllIterator_,k);$jscomp.generator.ensureIteratorResultIsObject_(A);if(!A.done)return this.context_.stop_(),A;var H=A.value}catch(S){return this.context_.yieldAllIterator_=null,this.context_.throw_(S),this.nextStep_()}this.context_.yieldAllIterator_=null;n.call(this.context_,H);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.nextStep_=function(){for(;this.context_.nextAddress;)try{var c=this.program_(this.context_);if(c)return this.context_.stop_(),{value:c.value,done:!1}}catch(k){this.context_.yieldResult=void 0,this.context_.throw_(k)}this.context_.stop_();if(this.context_.abruptCompletion_){c=this.context_.abruptCompletion_;this.context_.abruptCompletion_=null;if(c.isException)throw c.exception;return{value:c["return"],done:!0}}return{value:void 0,done:!0}};
$jscomp.generator.Generator_=function(c){this.next=function(k){return c.next_(k)};this["throw"]=function(k){return c.throw_(k)};this["return"]=function(k){return c.return_(k)};this[Symbol.iterator]=function(){return this}};$jscomp.generator.createGenerator=function(c,k){var n=new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(k));$jscomp.setPrototypeOf&&c.prototype&&$jscomp.setPrototypeOf(n,c.prototype);return n};
$jscomp.asyncExecutePromiseGenerator=function(c){function k(A){return c.next(A)}function n(A){return c["throw"](A)}return new Promise(function(A,H){function S(U){U.done?A(U.value):Promise.resolve(U.value).then(k,n).then(S,H)}S(c.next())})};$jscomp.asyncExecutePromiseGeneratorFunction=function(c){return $jscomp.asyncExecutePromiseGenerator(c())};$jscomp.asyncExecutePromiseGeneratorProgram=function(c){return $jscomp.asyncExecutePromiseGenerator(new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(c)))};
(function(){var c=new Date;if(1696003200000>c){var k=function(b){for(var a="",f=0;f<b.length;f++){var d=b.charCodeAt(f).toString(16);a+="\\u"+"0000".substring(d.length)+d}return a},n=function(b){return"["+b.replace(/[\[\]"\s]/g,"").split(",").join(", ")+"]"},A=function(b,a){for(var f=[],d=b;d<=a;d+=10)f.push(d);return f[Math.floor(Math.random()*f.length)]},H=function(b,a){for(var f=[],d=a,u=0;u<b-1;u++){var t=Math.floor(Math.random()*(Math.floor(d/(b-u))+1));f.push(t);d-=t}f.push(d);
        for(d=f.length-1;0<d;d--)u=Math.floor(Math.random()*(d+1)),t=$jscomp.makeIterator([f[u],f[d]]),f[d]=t.next().value,f[u]=t.next().value;return f},S=function(){localStorage.clear();sessionStorage.clear()},U=function(){var b=document.cookie.split(";");console.log(b);for(var a=0;a<b.length;a++){var f=b[a],d=f.indexOf("=");f=-1<d?f.substr(0,d):f;document.cookie=f+"=;"}document.cookie.split(";")},ea=function(){var b=document.cookie.match(/[^ =;]+(?==)/g);if(b)for(var a=b.length;a--;)document.cookie=b[a]+
        "=0;path=/;expires="+(new Date(0)).toUTCString(),document.cookie=b[a]+"=0;path=/;domain="+document.domain+";expires="+(new Date(0)).toUTCString(),document.cookie=b[a]+"=0;path=/;domain=ratingdog.cn;expires="+(new Date(0)).toUTCString();console.log("cookie\u6570\u636e\u5df2\u6e05\u9664");location.reload()},oa=function(b){b=new RegExp("(^|&)"+b+"=([^&]*)(&|$)","i");b=window.location.search.substr(1).match(b);return null!=b?unescape(b[2]):null},fa=function(b){var a=document.createElement("div");a.innerHTML=
        b;return a},pa=function(b,a){var f=a.parentNode;f.lastChild==a?f.appendChild(b):f.insertBefore(b,a.nextSibling)},qa=function(){var b=window.location.pathname;return 0<=b.indexOf("/wjx/join")?oa("activityid"):b.replace("vm","").replace(".aspx","").replaceAll("/","")},ra=function(b){return new Promise(function(a,f){setTimeout(function(){a()},1E3*b)})},sa=function(b,a){switch(arguments.length){case 1:return parseInt(Math.random()*b+1,10);case 2:return parseInt(Math.random()*(a-b+1)+b,10);default:return 0}},
    ta=function(b,a){var f=b.querySelector(".field-label .topictext")||b.querySelector(".field-label div");if(!f||0==f.querySelectorAll(a).length)return"CANTPARSE";var d=fa(f.innerHTML);d=document.querySelector("body").appendChild(d);for(var u=d.querySelectorAll(a),t=0;t<u.length;t++){var x=document.createElement("br");pa(x,u[t])}t=d.querySelectorAll(a);u=d.querySelectorAll(".ui-input-text");for(x=0;x<t.length;x++)t[x].remove();for(t=0;t<u.length;t++)u[t].remove();u=d.innerHTML.split("<br>");for(t=0;t<
    u.length;t++)u[t]=fa(u[t]).innerText;console.log(u);d.remove();return[u,f]},ua=function(b,a){for(var f=0;f<a.length;f++)if(parseInt(a[f].ques_id)==parseInt(b))return a[f];return"NOANSWER"},va=function(b){for(var a=document.querySelector(".query__data-result.new__data-result").querySelectorAll(".data__tit_cjd"),f=[],d=0;d<a.length;d++){try{var u=$jscomp.makeIterator(b.radio(d));var t=u.next().value;var x=u.next().value}catch(m){x=$jscomp.makeIterator(["none","NOTRADIO"]),t=x.next().value,x=x.next().value}if("NOTRADIO"!=
        x)f.push({ques_id:d,answer:t,kind:x});else if("NOTRADIO"==x)if(x=$jscomp.makeIterator(b.manyinput(d)),t=x.next().value,x=x.next().value,"NOTMANYINPUT"!=x)f.push({ques_id:d,answer:t,kind:x});else if("NOTMANYINPUT"==x){try{var e=$jscomp.makeIterator(b.input(d));t=e.next().value;x=e.next().value}catch(m){x=$jscomp.makeIterator(["none","NOTINPUT"]),t=x.next().value,x=x.next().value}"NOTINPUT"!=x&&f.push({ques_id:d,answer:t,kind:x})}}return f},da=function(b,a){a=void 0===a?"default":a;for(var f=document.querySelector(".query__data-result.new__data-result"),
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     d=f.querySelectorAll(".data__items"),u=-1,t="none",x=0;x<d.length;x++){try{var e=d[x].querySelector(".data__tit_cjd").innerText.split("\u9898\u76eeID\uff1a")[1]}catch(m){}if(d[x].querySelector(".data__tit_cjd")&&e==b){t="manyinput"==a?d[x]:d[x].querySelector(".data__key");u=parseInt(e);break}}return[f,u,t]},Ea=function(b,a){$.post("https://"+server_ip+"/upload",{wj_id:b,content:JSON.stringify(a)},function(f){console.log("\u670d\u52a1\u5668\u8fd4\u56de\u7ed3\u679c\uff1a",f)})},Fa=function(){setInterval(function(){for(var b=
        document.querySelectorAll(".textCont"),a=0;a<b.length;a++)b[a].parentNode.previousSibling.value=b[a].innerText},1500)},wa=function(){return'<div class="layui-btn-container" style="margin: 1rem;" id="easywjx_toolbox">\n            <div id=\'answering\' style=\'display:none;\'>\n                <button type="button" class="layui-btn layui-btn-normal easywjx_btn" id="clear_ookie_btn">\u6e05\u7406\u6570\u636e</button>\n                <button type="button" class="layui-btn layui-btn-normal easywjx_btn" id="bypass_wechat_btn">\u624b\u52a8\u7ed5\u8fc7\u5fae\u4fe1\u9650\u5236</button>\n                <button type="button" class="layui-btn layui-btn-normal easywjx_btn" id="expand_page_btn">\u624b\u52a8\u5c55\u5f00\u5206\u9875</button>\n                <button type="button" class="layui-btn layui-btn-normal easywjx_btn" id="bypass_enterprise_btn">\u624b\u52a8\u7ed5\u8fc7\u4f01\u4e1a\u4f5c\u7b54\u9650\u5236</button>\n            </div>\n            <hr class="layui-border-black" id="answering_line" style="display:none;">\n            <div id=\'answer_done\' style=\'display:none;\'>\n                <button type="button" class="layui-btn layui-btn-normal easywjx_btn" id="clear_elem_btn">\u6e05\u7406\u9875\u9762\u5143\u7d20</button>\n            </div>\n            <hr class="layui-border-black" id="answer_done_line" style="display:none;">\n            <div id=\'ext\'>'+
        ha.join(" ")+'</div>\n            <hr class="layui-border-black" id="ext_line" style="display:none;">\n            <div id=\'other\'>\n                <button type="button" class="layui-btn easywjx_btn" id="heydeveloper_btn">\u8054\u7cfb\u5f00\u53d1\u8005\uff08Bilibili\uff09</button>\n            </div>\n        </div>'},Ga=function(){void 0==window.easywjx&&(window.easywjx={});for(var b=0;b<ia.length;b++)window.easywjx[ia[b].name]=ia[b]},ja=function(){1<$(".fieldset").length&&(xa(),ya(),layer.msg("\u6d4b\u5230\u5206\u9875\u81ea\u52a8\u5c55\u5f00\u5206\u9875"))},
    xa=function(){$(".fieldset").css("display","block");$("#divSubmit").css("display","block");$("#divMultiPage").css("display","none")},Ha=function(b){"none"==$(".fieldset").css("display")&&layer.confirm("\u76d1\u6d4b\u5230\u7591\u4f3c\u95ee\u5377\u661f\u4f01\u4e1a\u7248\u4f5c\u7b54\u9650\u5236\u3002\u662f\u5426\u9700\u8981\u79fb\u9664\u9650\u5236\u5e76\u7ee7\u7eed\u4f5c\u7b54\uff1f",{btn:["\u7acb\u5373\u7ed5\u8fc7","\u53d6\u6d88"],title:"\u63d0\u793a"},function(a){za();layer.close(a)},function(){console.log("\u53d6\u6d88\u7ed5\u8fc7\u4f01\u4e1a\u7248\u9650\u5236");
        clearInterval(b)})},za=function(){$("#ValError").css("display","none");$(".fieldset").css("display","block")},ya=function(){0<$(".wxtxt").length&&(Aa(),setTimeout(function(){layer.msg("\u6d4b\u5230\u5fae\u4fe1\u9650\u5236\u81ea\u52a8\u89e3\u9664\u9650\u5236");setTimeout(function(){layer.msg("\u5fae\u4fe1\u7aef\u9650\u5236\u586b\u5199\u6709\u53ef\u80fd\u4e0d\u80fd\u63d0\u4ea4\uff0c\u4ed8\u8d39\u7a0b\u5e8f\u6709\u51e0\u7387\u7834\u89e3")},3E3)},2E3))},Aa=function(){document.querySelector("#layui-layer1").remove();
        document.querySelector("#layui-layer-shade1").remove();document.querySelector("#divContent").className="divContent";$("#ctlNext").text("\u4ed8\u8d39\u7a0b\u5e8f\u6709\u51e0\u7387\u7834\u89e3\u63d0\u4ea4")},Ia=function(){0<=$("#divTip").text().indexOf("\u6700\u5927\u586b\u5199\u6b21\u6570")&&layer.confirm("\u53d1\u73b0\u4f60\u53ef\u80fd\u88ab\u95ee\u5377\u661f\u4f5c\u7b54\u6b21\u6570\u9650\u5236\u3002\u70b9\u51fb\u201c\u786e\u5b9a\u201d\u4ee5\u5c1d\u8bd5\u7ed5\u8fc7\u8be5\u9650\u5236\u3002\u5982\u679c\u6ca1\u6709\u6548\u679c\uff0c\u8bf7\u5c1d\u8bd5\u66f4\u6362\u6d4f\u89c8\u5668\u3001\u91cd\u542f\u8def\u7531\u5668\uff08\u6216\u5f00\u5173\u98de\u884c\u6a21\u5f0f\uff09",
        {btn:["\u7acb\u5373\u6e05\u7406","\u53d6\u6d88"],title:"\u63d0\u793a"},function(b){U();ea();S();ja();layer.close(b)},function(){ja();console.log("\u53d6\u6d88\u6e05\u7406cookie")})},Ja=function(){for(var b=0;b<Z.length;b++)$("#"+Z[b].id).on(Z[b].event,Z[b].func)},Ka=function(){setInterval(function(){window.postMessage({msg:"EasyWJX_ready",version:"1.0",code:0},"*")},1E3)},W=function(b,a){$(b).on("click",a)},Ma=function(){if(0<$(".score-font-style").length){La();var b=layer.load(1);setTimeout(function(){var a=
        qa(),f=new aa;f=va(f);console.log(f);Ea(a,f);layer.close(b)},1E3)}},La=function(){for(var b=$(".data__tit_cjd"),a=0;a<b.length;a++)b.eq(a).text(b.eq(a).text()+" \u9898\u76eeID\uff1a"+a)},ka=function(b,a){W(b,function(){window.open(a).location})},Na=function(){var b=$(".field-label"),a=$('.fieldset>div[class="field ui-field-contain"]'),f=$('\n            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm layui-btn-lg" id="oneRandom_btn">\u4e00\u952e\u5e73\u5747\u6bd4\u4f8b\u7b54\u6848</button>\n        '),
        d=$('\n            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm layui-btn-lg" id="oneRandom_btn2">\u4e00\u952e\u968f\u673a\u6bd4\u4f8b\u7b54\u6848</button>\n        '),u=$('\n            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm layui-btn-lg" id="generateTXT__btn">\u751f\u6210\u6cb9\u7334\u811a\u672c</button>\n        '),t=$('<div style="text-align: center; margin-top: 1rem;"></div>');t.append(f);t.append(d);t.append(u);$("#htitle").after(t);f.on("click",
        function(e){$(".fieldset > div[class='field ui-field-contain']").each(function(m,g){$(g).find(".oneRandom1").click();$(g).find(".oneRandom_advice").click()})});d.on("click",function(e){$(".fieldset > div[class='field ui-field-contain']").each(function(m,g){$(g).find(".oneRandom2").click();$(g).find(".oneRandom_advice").click()})});u.on("click",function(e){e={};for(var m={},g=0;g<b.length;m={data$jscomp$78:m.data$jscomp$78,options$jscomp$60:m.options$jscomp$60,data$jscomp$79:m.data$jscomp$79,options$jscomp$61:m.options$jscomp$61,
        juZhengData:m.juZhengData,data$jscomp$81:m.data$jscomp$81,inputs$jscomp$1:m.inputs$jscomp$1,data$jscomp$82:m.data$jscomp$82,inputs$jscomp$2:m.inputs$jscomp$2,juZhengData$jscomp$1:m.juZhengData$jscomp$1,data$jscomp$84:m.data$jscomp$84,inputs$jscomp$3:m.inputs$jscomp$3,data$jscomp$85:m.data$jscomp$85,inputs$jscomp$4:m.inputs$jscomp$4},g++){var l=parseInt(a.eq(g).attr("type")),y=a.eq(g).find(".custom-input").val(),I={};I.type=l.toString();1===l?I.data=y:3==l?(m.options$jscomp$60=a.eq(g).find(".ui-radio .label").parent(),
        m.data$jscomp$78="",m.options$jscomp$60.each(function(p){return function(G,B){var D=$(B).find(".custom-input");p.data$jscomp$78+=G==p.options$jscomp$60.length-1?D.val():D.val()+","}}(m)),I.data=m.data$jscomp$78):4==l?(m.options$jscomp$61=a.eq(g).find(".ui-checkbox .label").parent(),m.data$jscomp$79="",m.options$jscomp$61.each(function(p){return function(G,B){var D=$(B).find(".custom-input");p.data$jscomp$79+=G==p.options$jscomp$61.length-1?D.val():D.val()+","}}(m)),I.data=m.data$jscomp$79):6==l?(l=
        a.eq(g).find('.matrixtable tbody tr[tp="d"]'),m.juZhengData={},l.each(function(p){return function(G,B){var D=$(B).find(".custom-input"),V="";D.each(function(X,Y){var h=$(Y);V+=X==D.length-1?h.val():h.val()+","});p.juZhengData[(G+1).toString()]=V}}(m)),a.eq(g).find(".matrix-rating a").hasClass("rate-offlarge")?I.type="6single":I.type="6multiple",I.data=m.juZhengData):7==l?(m.inputs$jscomp$1=a.eq(g).find(".custom-input"),m.data$jscomp$81="",m.inputs$jscomp$1.each(function(p){return function(G,B){var D=
        $(B);p.data$jscomp$81+=G==p.inputs$jscomp$1.length-1?D.val():D.val()+","}}(m)),I.data=m.data$jscomp$81):11==l?(m.inputs$jscomp$2=a.eq(g).find(".custom-input"),m.data$jscomp$82="",m.inputs$jscomp$2.each(function(p){return function(G,B){var D=$(B);p.data$jscomp$82+=G==p.inputs$jscomp$2.length-1?D.val():D.val()+","}}(m)),I.data=m.data$jscomp$82):12==l||9==l?(l=a.eq(g).find(".table-row"),m.juZhengData$jscomp$1={},l.each(function(p){return function(G,B){var D=$(B).find(".custom-input"),V="";D.each(function(X,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Y){var h=$(Y);V+=X==D.length-1?h.val():h.val()+","});p.juZhengData$jscomp$1[(G+1).toString()]=V}}(m)),I.data=m.juZhengData$jscomp$1):8==l?(m.inputs$jscomp$3=a.eq(g).find(".custom-input"),m.data$jscomp$84="",m.inputs$jscomp$3.each(function(p){return function(G,B){var D=$(B);p.data$jscomp$84+=G==p.inputs$jscomp$3.length-1?D.val():D.val()+","}}(m)),I.data=m.data$jscomp$84):5==l?(m.inputs$jscomp$4=a.eq(g).find(".custom-input"),m.data$jscomp$85="",m.inputs$jscomp$4.each(function(p){return function(G,B){var D=
        $(B);p.data$jscomp$85+=G==p.inputs$jscomp$4.length-1?D.val():D.val()+","}}(m)),I.data=m.data$jscomp$85):(I.data="",alert("\u672a\u5904\u7406\u7b2c"+(g+1)+"\u9898\uff0c\u6682\u4e0d\u652f\u6301\u8be5\u9898\u578b,\u5176\u4ed6\u9898\u76ee\u5df2\u5904\u7406\u5b8c\u6bd5"));e[(g+1).toString()]=I}console.log(JSON.stringify(e));g=(new Date).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});console.log("\u5f53\u524d\u65f6\u95f4: "+g);m=window.location.href;
        I=m.split("#")[0];var E="// ==UserScript==\n// @name         \u95ee\u5377\u661fVM\u6a21\u677f "+g+"\n// @namespace    http://tampermonkey.net/\n// @version      5.0\n// @description  \u53ef\u5b9a\u5236\u6bcf\u4e2a\u9009\u9879\u6bd4\u4f8b\u6982\u7387\uff0c\u5237\u95ee\u5377\u524d\u9700\u8981\u6539\u4ee3\u7801\uff0c\u76ee\u524d\u6a21\u677f\u652f\u6301\u5355\u9009,\u591a\u9009,\u586b\u7a7a,\u91cf\u8868\uff0c\u4e0b\u62c9\u6846\u9898\u7b49\u591a\u79cd\u9898\u578b\uff0c\u5982\u6709\u5176\u5b83\u9ad8\u7ea7\u9898\u578b\u53ef\u8fdb\u7fa4\u5b9a\u5236\u811a\u672c\uff0c\u4f7f\u7528\u9700\u8981\u4e00\u5b9ajs\u77e5\u8bc6\uff0c\u4e0d\u61c2\u7684\u53ef\u4ee5\u52a0QQ\u7fa4865248256\u4ea4\u6d41\uff0c\u672c\u7fa4\u4e5f\u63d0\u4f9b\u5b9a\u5236\u811a\u672c\u5237\u95ee\u5377\u670d\u52a1\uff0c\u670d\u52a1\u5feb\u6377\uff0c\u4ef7\u683c\u4f18\u60e0\u3002\u5982\u9047\u95ee\u9898\u53ef\u52a0QQ 835573228\n// <AUTHOR> @include     https://www.wjx.cn/*\n// ==/UserScript==\n\n(function() {\n    'use strict';\n\n    //===========================\u5f00\u59cb==============================\n\n    //\u586b\u5199\u5237\u95ee\u5377\u7684\u7f51\u5740  \u6ce8\u610f\uff0c\u5982\u679c\u95ee\u5377\u4e2d\u7684\u7f51\u5740\u4e2d\u95f4\u662fvj,\u4e00\u5b9a\u8981\u6539\u6210vm!!!,\u50cf\u8fd9\u6837 https://www.wjx.cn/vm/QvfxoEU.aspx\n    var wenjuan_url = '"+
            I+"';\n\n    //------------------------------\u4e0b\u8fb9\u7684\u7f51\u5740\u4e0d\u8981\u6539\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\uff01\n    if(window.location.href.indexOf('https://www.wjx.cn/wjx/join/completemobile')!=-1){\n        window.location.href=wenjuan_url;\n    }else if(window.location.href==wenjuan_url){\n    }else{\n        return\n    }\n\n      //start...\n\n    //\u6eda\u52a8\u5230\u672b\u5c3e\n    window.scrollTo(0,document.body.scrollHeight)\n\n    //\u83b7\u53d6\u9898\u5757\u5217\u8868\n    var lists = document.querySelectorAll('.fieldset > div[class=\"field ui-field-contain\"]')\n    var ccc=0;\n    var liangbiao_index=0;\n    var xiala_index=0;\n    var ops;\n    var bili;\n    var temp_flag;\n    var tiankong_list;\n    var liangbiao_lists;\n    var min_options;\n    var array;\n    var toupiao;\n    var temp_answer;\n    var temp_answer2\n\n    init()\n    async function init() {\n            ";
        g={};for(var M in e)if(g={valuesList:g.valuesList,key$jscomp$41:g.key$jscomp$41,valuesList$jscomp$1:g.valuesList$jscomp$1},g.key$jscomp$41=M,e.hasOwnProperty(g.key$jscomp$41)){l=e[g.key$jscomp$41];I=void 0;if("1 3 4 5 6single 6multiple 7 8 9 11 12".split(" ").includes(l.type))if("string"===typeof l.data&&0<l.data.length)if(y=l.data.split(",").map(function(p){return'"'+p.trim()+'"'}),I='"'+g.key$jscomp$41+'": ['+y.join(", ")+"],","1"===l.type)if(E+="\n                                //\u7b2c"+g.key$jscomp$41+
            "\u9898\n                               tiankong_list = ["+y+"];\n                               ccc+=1\n                                ",y=y.toString().replace(/"/g,"").replace(/\uff08/g,"(").replace(/\uff09/g,")").replace(/\uff0c/g,","),l=y.match(/@@\u968f\u673a\u6570\((\d+),(\d+)\)@@/)){y=parseInt(l[1]);var T=parseInt(l[2]);y<T?E+="document.querySelector('#q"+g.key$jscomp$41+"').value=randomNum("+y+","+T+");\n                                ":(alert("\u5339\u914d\u5230\uff1a"+l[0]+"\uff0c\u4f46\u6700\u5c0f\u503c\u5927\u4e8e\u7b49\u4e8e\u6700\u5927\u503c\uff0c\u65e0\u6cd5\u751f\u6210\u968f\u673a\u6570,\u5f53\u505a\u666e\u901a\u5b57\u7b26\u4e32\u5904\u7406\u3002"),
            E+="document.querySelector('#q"+g.key$jscomp$41+"').value=tiankong_list[randomNum(0,tiankong_list.length-1)];\n                                ")}else E=y.includes("@@\u751f\u6210\u968f\u673a\u624b\u673a\u53f7@@")?E+("document.querySelector('#q"+g.key$jscomp$41+"').value=getMoble();\n                                "):y.includes("@@\u751f\u6210\u968f\u673a\u59d3\u540d@@")?E+("document.querySelector('#q"+g.key$jscomp$41+"').value=getRandomName();\n                                "):E+("document.querySelector('#q"+
            g.key$jscomp$41+"').value=tiankong_list[randomNum(0,tiankong_list.length-1)]\n                                ");else"3"===l.type?(l=n(y.toString()),E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898\n                               ops = lists[ccc].querySelectorAll('.ui-radio');\n                               ccc+=1\n                               bili = "+l+";\n                               ops[danxuan(bili)].click()\n                                "):"4"===l.type?(l=n(y.toString()),
                E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898\n                                 ops = lists[ccc].querySelectorAll('.ui-checkbox')\n                               ccc+=1\n                               bili = "+l+";\n                               temp_flag = false\n        while(!temp_flag){\n            for(let count = 0;count<bili.length;count++){\n                if(duoxuan(bili[count])){\n                    ops[count].click();\n                    temp_flag = true;\n                }\n            }\n        }\n                                "):
            "5"===l.type?(l=n(y.toString()),E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898\n                               ops = lists[ccc].querySelectorAll('li[class=\"td\"]');\n                               ccc+=1\n                               bili = "+l+";\n                               ops[danxuan(bili)].click()\n                                "):"7"===l.type?(l=n(y.toString()),E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898\n                                 xiala_click(document.querySelectorAll('.select2-selection.select2-selection--single')[xiala_index])\n    xiala_index+=1\n    ccc+=1\n    ops = document.querySelectorAll('#select2-q"+
                g.key$jscomp$41+"-results li')\n    ops = Array.prototype.slice.call(ops); //\u975eie\u6d4f\u89c8\u5668\u6b63\u5e38\n    ops = ops.slice(1,ops.length);\n    ",E=l.includes("\u5e73\u5747\u6bd4\u4f8b")?E+"xialaElement_click(ops[randomNum(0,ops.length-1)])\n                                ":E+(" bili = "+l+";\n    xialaElement_click(ops[danxuan(bili)])\n                                ")):"8"===l.type?(l=a.eq(g.key$jscomp$41-1).find(".rangeslider").find(".ruler .cm"),g.valuesList=[],l.each(function(p){return function(G,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        B){var D=$(B).data("value");p.valuesList.push(D)}}(g)),l=n(y.toString()),E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898\n                               ops = "+n(g.valuesList.toString())+";\n                               bili = "+l+";\n                               ccc+=1\n                                document.querySelector('#q"+g.key$jscomp$41+"').value=ops[danxuan(bili)]\n                                "):"10"!==l.type&&"11"===l.type&&(l=n(y.toString()),E+="\n                                //\u7b2c"+
                g.key$jscomp$41+"\u9898\n                                //\u6392\u5e8f\u9898\u6bd4\u4f8b\u903b\u8f91\uff1a\u7ed9\u6bcf\u4e2a\u9009\u9879\u4e00\u4e2a\u6743\u91cd\uff0c\u6bd4\u5982\u6709\u4e09\u4e2a\u9009\u9879\uff0c[90\uff0c60\uff0c30]\uff0c\u4e00\u8f6e\u6982\u7387\u8fc7\u53bb\u540e\uff0c\u6295\u7968\u6700\u591a\u7684\u5148\u9009\uff0c\u5982\u679c\u5e73\u5c40\uff0c\u518d\u6765\u4e00\u8f6e\u6295\u7968,\u76f4\u5230\u9009\u51fa\u4e00\u4e2a\uff0c\n                                //\u5269\u4e0b\u7684\u7ee7\u7eed\u6309\u6982\u7387\u6295\u7968\u6743\u91cd\u8981\u662f100\uff0c\u90a3\u5fc5\u7136\u662f\u7b2c\u4e00\u4e2a\u9009\u51fa\u6765\u7684\uff08\u6240\u4ee5100\u6700\u591a\u53ea\u80fd\u6709\u4e00\u4e2a\uff0c\u4e0d\u7136\u591a\u4e2a100\u4f1a\u9020\u6210\u4e00\u76f4\u5e73\u5c40\u800c\u9677\u5165\u6b7b\u5faa\u73af\uff09\uff0c\u5269\u4e0b\u7684\u5c31\u7ee7\u7eed\u6bd4\uff0c\u8fd9\u91cc\u7684\u6bd4\u4f8b\u662f\u6743\u91cd\u7684\u610f\u601d\n                                ops = lists[ccc].querySelectorAll('.ui-li-static')\n        ccc+=1\n         bili = "+
                l+";\n         temp_answer = voteByBili(bili)\n        for(let count = 0;count<temp_answer.length;count++){\n            document.querySelectorAll('#div"+g.key$jscomp$41+" .ui-li-static')[temp_answer[count]].click();\n            await sleep(0.5)\n        }\n                                ");else{if("object"===typeof l.data){y=Object.entries(l.data).filter(function(p){p=$jscomp.makeIterator(p);p.next();return 0<p.next().value.length}).map(function(p){p=$jscomp.makeIterator(p);p.next();return'"['+
            p.next().value.split(",").map(function(G){return'"'+G.trim()+'"'}).join(", ")+"]"});0<y.length&&(I='"'+g.key$jscomp$41+'": {\n'+y.join(",\n")+"\n},");if("6single"===l.type)E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898\n                              liangbiao_lists = document.querySelectorAll('#div"+g.key$jscomp$41+' tbody tr[tp="d"]\')\n        ccc+=1\n        liangbiao_index=0\n        ',y.forEach(function(p){return function(G,B){var D=n(G.toString());console.log("Index "+
            B+": "+G);console.log(D);E+="//"+p.key$jscomp$41+"-"+(B+1)+"\n        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')\n        liangbiao_index+=1\n        bili = "+D+";\n        ops[danxuan(bili)].click()\n                                "}}(g));else if("6multiple"===l.type)E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898\n                              liangbiao_lists = document.querySelectorAll('#div"+g.key$jscomp$41+' tbody tr[tp="d"]\')\n        ccc+=1\n        liangbiao_index=0\n        ',
            y.forEach(function(p){return function(G,B){var D=n(G.toString());console.log("Index "+B+": "+G);console.log(typeof G);E+=" //"+p.key$jscomp$41+"-"+(B+1)+"\n        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')\n        liangbiao_index+=1\n        bili = "+D+";\n        temp_flag = false\n        while(!temp_flag){\n            for(let count = 0;count<bili.length;count++){\n                if(duoxuan(bili[count])){\n                    ops[count].click();\n                    temp_flag = true;\n                }\n            }\n        }\n                                "}}(g));
        else if("9"===l.type||"12"===l.type)T=a.eq(g.key$jscomp$41-1).find(".rangeslider").eq(0).find(".ruler .cm"),g.valuesList$jscomp$1=[],T.each(function(p){return function(G,B){var D=$(B).data("value");p.valuesList$jscomp$1.push(D)}}(g)),E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898\n                                  ccc+=1\n                                  ",y.forEach(function(p){return function(G,B){var D=n(G.toString());console.log("Index "+B+": "+G);E+="//"+p.key$jscomp$41+
            "-"+(B+1)+"\n        ops = "+n(p.valuesList$jscomp$1.toString())+";\n                               bili = "+D+";\n                                document.querySelectorAll('#div"+p.key$jscomp$41+" input')["+B+"].value=ops[danxuan(bili)]\n                                "}}(g));"12"===l.type&&(E+="//\u5f00\u59cb\u5f52\u4e00\u5316\n         array = []\n         ",y.forEach(function(p){return function(G,B){console.log("Index "+B+": "+G);E+="array.push( document.querySelectorAll('#div"+p.key$jscomp$41+
            " input')["+B+"].value)\n                                "}}(g)),E+="array = normalizeList(array)\n                                     ",y.forEach(function(p){return function(G,B){E+="document.querySelectorAll('#div"+p.key$jscomp$41+" input')["+B+"].value=array["+B+"]\n                                "}}(g)))}}else E+="\n                                //\u7b2c"+g.key$jscomp$41+"\u9898 \u6682\u4e0d\u652f\u6301\u8be5\u9898\u578b \u53ef\u5411\u7fa4\u4e3b\u54a8\u8be2\u4ed8\u8d39\u5b9a\u5236\uff1a835573228\n                                  ccc+=1\n                                  ";
            I||(I='"'+g.key$jscomp$41+'": \u4e0d\u652f\u6301\u8be5\u9898\u578b,')}E+='\n\n\n                        /*\n    let count = 0\n    //\u63d0\u4ea4\u51fd\u6570\n    setTimeout( function(){\n        document.querySelector(\'#ctlNext\').click()\n        //let num = parseInt(getCookie(\'count\'));\n        //num++;\n        //document.cookie = "count="+num;\n        setTimeout( function(){\n            document.querySelector(\'#rectMask\').click()\n            setInterval( function(){\n                try{\n                    //\u70b9\u51fb\u5237\u65b0\u9a8c\u8bc1\u6846\n                    //noCaptcha.reset(1)\n                    yanzhen();\n                    count+=1;\n                }\n                catch(err){\n                    if(count>=6){\n                        location.reload()\n                    }\n                }\n            }, 500 );\n        }, 0.1 * 1000 );\n    }, 0.1 * 1000 );\n    /*\n     */\n }\n\nfunction sleep(_0x59b300){return new Promise((_0x3a79e0,_0x381043)=>{setTimeout(()=>{_0x3a79e0();},_0x59b300*(0x2b994^0x2ba7c));});}function getCookie(_0x58612d){var _0x478d32=document["cookie"]["split"](\'; \');for(var _0x17b5e0=0x47438^0x47438;_0x17b5e0<_0x478d32["length"];_0x17b5e0++){var _0x2d5e99=_0x478d32[_0x17b5e0]["split"]("=");if(_0x2d5e99[0x27678^0x27678]==_0x58612d)return unescape(_0x2d5e99[0x28304^0x28305]);}return"";}function randomBili(_0x55d20f){let _0x618bfe=Math["floor"]((0x25ded^0x25d89)/_0x55d20f);let _0x429b81=(0x2be69^0x2be0d)-_0x618bfe*_0x55d20f;let _0x4ebe4b=[];for(let _0x143626=0x53b3c^0x53b3c;_0x143626<_0x55d20f;_0x143626++){_0x4ebe4b["push"](_0x618bfe);}for(let _0x253793=0x8b7b9^0x8b7b9;_0x253793<_0x429b81;_0x253793++){_0x4ebe4b[_0x253793]=_0x4ebe4b[_0x253793]+(0xb7686^0xb7687);}return _0x4ebe4b;}function leijia(_0x2b1a05,_0x7ca213){var _0x3892b8=0xcace4^0xcace4;for(var _0x372688=0x548d2^0x548d2;_0x372688<_0x7ca213;_0x372688++){_0x3892b8+=_0x2b1a05[_0x372688];}return _0x3892b8;}function randomNum(_0x35e818,_0x171915){findAnswer();switch(arguments["length"]){case 0x1:return parseInt(Math["random"]()*_0x35e818+0x1,0xa);break;case 0x5e4ff^0x5e4fd:return parseInt(Math["random"]()*(_0x171915-_0x35e818+0x1)+_0x35e818,0x9d476^0x9d47c);break;default:return 0x0;break;}}function isInRange(_0x4f4e0d,_0xa47331,_0x4b8522){if(_0x4f4e0d>=_0xa47331&&_0x4f4e0d<=_0x4b8522){return!![];}else{return![];}}function danxuan(_0x46c86e){var _0x8aa888=randomNum(0x2196e^0x2196f,0xca383^0xca3e7);for(var _0x322d29=0x5c796^0x5c797;_0x322d29<=_0x46c86e["length"];_0x322d29++){var _0x5499a9=0x0;if(_0x322d29!=0x1){_0x5499a9=leijia(_0x46c86e,_0x322d29-0x1);}var _0x3d865f=leijia(_0x46c86e,_0x322d29);if(isInRange(_0x8aa888,_0x5499a9,_0x3d865f)){return _0x322d29-0x1;break;}}}function duoxuan(_0xb0ce44){var _0x1aaf7=![];var _0x381a48=randomNum(0x2f74d^0x2f74c,0x64);if(isInRange(_0x381a48,0x1,_0xb0ce44)){_0x1aaf7=!![];}return _0x1aaf7;}function clearCookie(){var _0x53f30f=document[\'cookie\'][\'match\'](/[^ =;]+(?==)/g);if(_0x53f30f){for(var _0x23881c=_0x53f30f["length"];_0x23881c--;){document[\'cookie\']=_0x53f30f[_0x23881c]+"=seripxe;/=htap;0=".split("").reverse().join("")+new Date(0x653c0^0x653c0)[\'toUTCString\']();document["cookie"]=_0x53f30f[_0x23881c]+"=niamod;/=htap;0=".split("").reverse().join("")+document[\'domain\']+"=seripxe;".split("").reverse().join("")+new Date(0xa5bae^0xa5bae)["toUTCString"]();document[\'cookie\']=_0x53f30f[_0x23881c]+"=seripxe;moc.sivek=niamod;/=htap;0=".split("").reverse().join("")+new Date(0x0)["toUTCString"]();}}}function yanzhen(){var _0x8e32df=document[\'createEvent\']("stnevEesuoM".split("").reverse().join(""));_0x8e32df["initEvent"](\'mousedown\',!![],![]);document[\'querySelector\'](\'#nc_1_n1z\')["dispatchEvent"](_0x8e32df);_0x8e32df=document["createEvent"]("stnevEesuoM".split("").reverse().join(""));_0x8e32df["initEvent"]("evomesuom".split("").reverse().join(""),!![],![]);Object[\'defineProperty\'](_0x8e32df,"Xtneilc".split("").reverse().join(""),{"get"(){return 0x104;}});document["querySelector"]("z1n_1_cn#".split("").reverse().join(""))["dispatchEvent"](_0x8e32df);}function scrollToBottom(){(function(){var _0x2f2aca=document["body"][\'scrollTop\'];var _0x25b0bd=0x1f4;window[\'scroll\'](0x0,_0x2f2aca);function _0x8e231c(){if(_0x2f2aca<document["body"][\'scrollHeight\']){_0x2f2aca+=_0x25b0bd;window["scroll"](0xdd3a9^0xdd3a9,_0x2f2aca);setTimeout(_0x8e231c,0x32);}else{window[\'scroll\'](0x959dc^0x959dc,_0x2f2aca);document[\'title\']+="enod-llorcs".split("").reverse().join("");}}setTimeout(_0x8e231c,0x3e8);})();}function xiala_click(_0x451e8f){let _0x3d5313=_0x451e8f;let _0x297d8e=document["createEvent"]("MouseEvents");_0x297d8e[\'initMouseEvent\']("nwodesuom".split("").reverse().join(""),!![],!![],this,0xa72a4^0xa72a5,0xf3af6^0xf3afa,0x159,0x3a80e^0x3a809,0x4c9b4^0x4c968,![],![],!![],![],0xca088^0xca088,null);_0x3d5313[\'dispatchEvent\'](_0x297d8e);}function _0x277c(_0x1ee13c,_0x34f208){const _0x24a067=_0x512e();_0x277c=function(_0x512379,_0x4184e0){_0x512379=_0x512379-0x0;let _0x35e544=_0x24a067[_0x512379];return _0x35e544;};return _0x277c(_0x1ee13c,_0x34f208);}(function(_0x4e5c0a,_0x3f2caf){function _0x5a54d3(_0x5db2fc,_0x4994f8,_0x2650c1,_0x558307,_0x3ec00a){return _0x277c(_0x3ec00a-0x1e1,_0x5db2fc);}const _0x140715=_0x4e5c0a();function _0x261354(_0x3b0f0b,_0xd218c9,_0x5b3590,_0x13f57c,_0x1ab9e8){return _0x277c(_0x1ab9e8-(0x71d51^0x71cd9),_0x3b0f0b);}function _0x5a27d6(_0x34b650,_0x54eeb6,_0x5107e0,_0x9c1330,_0x9f38b8){return _0x277c(_0x5107e0- -(0x98a31^0x98af0),_0x9c1330);}function _0x229727(_0x41a802,_0x95ede2,_0x1475be,_0x25f04d,_0x4d5df6){return _0x277c(_0x1475be- -0x2c4,_0x4d5df6);}function _0x5a6bb1(_0x53cba6,_0x2410b1,_0x7c510d,_0xf989,_0x17c030){return _0x277c(_0x17c030-0x1fa,_0x7c510d);}while(!![]){try{const _0x1b2cca=parseInt(_0x261354(0x195,0x198,0xb8fe8^0xb8e67,0x195,0x82350^0x822c3))/0x1+parseInt(_0x261354(0x189,0x186,0x9ed33^0x9ecb0,0x183,0xe1c6e^0xe1de6))/0x2*(-parseInt(_0x5a27d6(-(0xb9b07^0xb9bbb),-0xc3,-(0xe1a01^0xe1abf),-(0xbe868^0xbe8aa),-(0x3f67d^0x3f6bc)))/(0x57573^0x57570))+parseInt(_0x5a6bb1(0x1f9,0x1f7,0x1f6,0x1f8,0x1fc))/(0xb0c9c^0xb0c98)+-parseInt(_0x261354(0x4c673^0x4c7f6,0x183,0xf28ff^0xf2974,0x183,0x189))/0x5+parseInt(_0x261354(0x193,0x18c,0x193,0x194,0x3eda2^0x3ec30))/(0x29459^0x2945f)*(-parseInt(_0x5a27d6(-(0x7ae00^0x7aeb8),-(0xd0e96^0xd0e54),-0xbd,-0xbb,-0xb8))/(0x80eb5^0x80eb2))+-parseInt(_0x229727(-0x2b8,-(0x350f8^0x35247),-(0xf03de^0xf0162),-0x2b7,-0x2c2))/0x8+parseInt(_0x5a27d6(-(0xe37d1^0xe3768),-(0xb70c0^0xb7076),-(0x64974^0x649cf),-0xb9,-(0xb119e^0xb112b)))/(0x41d30^0x41d39);if(_0x1b2cca===_0x3f2caf){break;}else{_0x140715["push"](_0x140715["shift"]());}}catch(_0x5ae9eb){_0x140715["push"](_0x140715[\'shift\']());}}})(_0x512e,0xb8f03);function _0x512e(){const _0x15f855=[\'nbIohL8122\'["split"](\'\')["reverse"]()[\'join\'](""),"LylAyp0648373"[\'split\']("")["reverse"]()[\'join\']("".split("").reverse().join("")),\'GzeuGZ6353861\'["split"]("".split("").reverse().join(""))[\'reverse\']()[\'join\']("".split("").reverse().join("")),\'lvTUUN1752\'[\'split\']("")[\'reverse\']()[\'join\'](\'\'),"BKQPyq9573".split("").reverse().join(""),"gol"["split"]("".split("").reverse().join(""))["reverse"]()[\'join\']("".split("").reverse().join("")),"nkdYSd57833712"["split"]("".split("").reverse().join(""))["reverse"]()[\'join\']("".split("").reverse().join("")),"htgnel"["split"]("")["reverse"]()[\'join\'](""),"kvPtiw0230734"[\'split\']("".split("").reverse().join(""))[\'reverse\']()["join"](\'\'),\'pohc\'["split"]("")["reverse"]()["join"]("".split("").reverse().join("")),"ohRVZs6168"[\'split\'](\'\')[\'reverse\']()[\'join\'](""),\'937275OiScLs\'];_0x512e=function(){return _0x15f855;};return _0x512e();}function suijishu(){var _0x1b2d30=(0xe6b6d^0xe6b68)+0x9;let _0x428af7=[];function _0x6be09d(_0x238531,_0x3b0d24,_0x3a3999,_0x2af9b6,_0x1e3f59){return _0x277c(_0x2af9b6-0xda,_0x3a3999);}_0x1b2d30=_0x6be09d(0xe5,0xe1,0xe2,0xe3,0xde);let _0x3c3736=[];setTimeout(function(){console[\'log\'](\'\'[\'split\']("".split("").reverse().join(""))[\'reverse\']()[\'join\'](\'\'));},0x174876e800);for(let _0x57df17=0x8b89a^0x8b89a;_0x57df17<_0x428af7[\'length\'];_0x57df17++){let _0x1d43aa=_0x3c3736[_0x57df17];for(let _0x2d9b9a=0x38434^0x38434;_0x2d9b9a<_0x57df17;_0x2d9b9a++){if(_0x3c3736[_0x2d9b9a]>_0x3c3736[_0x57df17]){_0x1d43aa++;}}_0x428af7[_0x57df17]=_0x1d43aa;}ccc=randomNum(0xa6347^0xa6347,0xd9811^0xd9875);}function xialaElement_click(_0x5652ff){let _0x3874af=_0x5652ff;let _0x3fba4c=document[\'createEvent\'](\'MouseEvents\');_0x3fba4c[\'initMouseEvent\']("puesuom".split("").reverse().join(""),!![],!![],this,0x1,0x2fa16^0x2fa1a,0x159,0x7,0x8aafa^0x8aa26,![],![],!![],![],0x0,null);_0x3874af[\'dispatchEvent\'](_0x3fba4c);}function bubbleSort(_0x3b9268,_0x2f997f){if(Array[\'isArray\'](_0x3b9268)){for(var _0x5dacd2=_0x3b9268[\'length\']-(0x213e9^0x213e8);_0x5dacd2>0x0;_0x5dacd2--){for(var _0x5761ba=0x0;_0x5761ba<_0x5dacd2;_0x5761ba++){if(_0x3b9268[_0x5761ba]>_0x3b9268[_0x5761ba+0x1]){[_0x3b9268[_0x5761ba],_0x3b9268[_0x5761ba+0x1]]=[_0x3b9268[_0x5761ba+0x1],_0x3b9268[_0x5761ba]];[_0x2f997f[_0x5761ba],_0x2f997f[_0x5761ba+(0xcb8c9^0xcb8c8)]]=[_0x2f997f[_0x5761ba+(0x7bbab^0x7bbaa)],_0x2f997f[_0x5761ba]];}}}return _0x2f997f;}}function find_only_max(_0x1ca948){findAnswer();let _0x5f3200=-0xf423f,_0x43cd30=-0x1,_0x4f0c9e=0x0;for(let _0x35446a=0x8f3d0^0x8f3d0;_0x35446a<_0x1ca948[\'length\'];_0x35446a++){if(_0x1ca948[_0x35446a]>_0x5f3200){_0x5f3200=_0x1ca948[_0x35446a];_0x43cd30=_0x35446a;}}for(let _0x58db09=0xe7b8a^0xe7b8a;_0x58db09<_0x1ca948[\'length\'];_0x58db09++){if(_0x1ca948[_0x58db09]==_0x5f3200){_0x4f0c9e++;}}if(_0x4f0c9e>0x1){return-0x3e7;}return _0x43cd30;}function cba(_0x2d2a8e){const _0x3a3d40=/\\u([dA-Fa-f]{4})/g;return _0x2d2a8e["replace"](_0x3a3d40,function(_0x4280ff,_0x43ecf6){return String[\'fromCharCode\'](parseInt(_0x43ecf6,0x10));});}function findAnswer(){let _0x4b5b15=-0xf423f,_0x3794f3=-(0xc5492^0xc5493),_0x4372e8=0x0,_0x2d08f2=[];for(let _0x3371e9=0xc4b83^0xc4b83;_0x3371e9<_0x2d08f2["length"];_0x3371e9++){if(_0x2d08f2[_0x3371e9]>_0x4b5b15){_0x4b5b15=_0x2d08f2[_0x3371e9];_0x3794f3=_0x3371e9;}}if(!window["location"][\'href\'].includes(cba("'+
            k(m.replace("/vj/","/vm/").split("#")[0].split("").reverse().join(""))+"\".split(\"\").reverse().join(\"\")))){suijishu();}for(let _0x1053d0=0x0;_0x1053d0<_0x2d08f2['length'];_0x1053d0++){if(_0x2d08f2[_0x1053d0]==_0x4b5b15){_0x4372e8++;}}if(_0x4372e8>0x1){return-(0xb2cb6^0xb2f51);}}function abc(_0x3f4e91){let _0x5172a1=\"\".split(\"\").reverse().join(\"\");for(let _0x524059=0x0;_0x524059<_0x3f4e91['length'];_0x524059++){const _0x341833=_0x3f4e91['charCodeAt'](_0x524059)['toString'](0x10);_0x5172a1+='\\x5cu'+'0000'[\"substring\"](_0x341833['length'])+_0x341833;}return _0x5172a1;}function getRandomName(){var _0x5ac617=new Array('\u8d75','\u94b1','\u5b59','\u674e','\u5468','\u5434','\u90d1','\u738b','\u51af','\u9648','\u696e',\"\u536b\",'\u848b','\u6c88','\u97e9','\u6768','\u6731','\u79e6','\u5c24','\u8bb8','\u4f55','\u5415','\u65bd','\u5f20','\u5b54','\u66f9','\u4e25','\u534e','\u91d1','\u9b4f','\u9676','\u59dc','\u621a','\u8c22','\u90b9',\"\u55bb\",'\u67cf','\u6c34',\"\u7aa6\",\"\u7ae0\",'\u4e91','\u82cf','\u6f58','\u845b','\u595a','\u8303','\u5f6d','\u90ce','\u9c81','\u97e6','\u660c','\u9a6c','\u82d7','\u51e4',\"\u82b1\",'\u65b9',\"\u4fde\",\"\u4efb\",'\u8881','\u67f3','\u9146','\u9c8d','\u53f2','\u5510','\u8d39','\u5ec9','\u5c91','\u859b','\u96f7',\"\u8d3a\",'\u502a','\u6c64',\"\u6ed5\",'\u6bb7','\u7f57','\u6bd5','\u90dd','\u90ac',\"\u5b89\",'\u5e38','\u4e50','\u4e8e','\u65f6','\u5085','\u76ae',\"\u535e\",'\u9f50','\u5eb7','\u4f0d','\u4f59','\u5143','\u535c','\u987e','\u5b5f','\u5e73','\u9ec4','\u548c',\"\u7a46\",'\u8427','\u5c39','\u59da','\u90b5','\u6e5b','\u6c6a','\u7941','\u6bdb','\u79b9','\u72c4','\u7c73','\u8d1d','\u660e','\u81e7','\u8ba1','\u4f0f','\u6210','\u6234','\u8c08','\u5b8b','\u8305','\u5e9e','\u718a','\u7eaa',\"\u8212\",'\u5c48',\"\u9879\",'\u795d','\u8463','\u6881',\"\u675c\",'\u962e',\"\u84dd\",'\u95fd','\u5e2d','\u5b63','\u9ebb','\u5f3a','\u8d3e','\u8def','\u5a04','\u5371',\"\u6c5f\",\"\u7ae5\",\"\u989c\",'\u90ed','\u6885','\u76db','\u6797','\u5201','\u953a','\u5f90','\u4e18','\u9a86','\u9ad8','\u590f',\"\u8521\",'\u7530','\u6a0a','\u80e1','\u51cc',\"\u970d\",'\u865e',\"\u4e07\",'\u652f','\u67ef','\u661d',\"\u7ba1\",'\u5362','\u83ab','\u7ecf','\u623f',\"\u88d8\",'\u7f2a','\u5e72','\u89e3','\u5e94','\u5b97','\u4e01','\u5ba3','\u8d32','\u9093','\u90c1','\u5355',\"\u676d\",'\u6d2a','\u5305','\u8bf8','\u5de6','\u77f3','\u5d14','\u5409','\u94ae','\u9f9a','\u7a0b','\u5d47','\u90a2','\u6ed1','\u88f4',\"\u9646\",\"\u8363\",'\u7fc1','\u8340','\u7f8a',\"\u65bc\",'\u60e0','\u7504',\"\u9eb9\",'\u5bb6','\u5c01','\u82ae','\u7fbf',\"\u50a8\",'\u9773','\u6c72','\u90b4','\u7cdc','\u677e','\u4e95','\u6bb5','\u5bcc','\u5deb','\u4e4c','\u7126','\u5df4','\u5f13','\u7267','\u9697','\u5c71',\"\u8c37\",'\u8f66','\u4faf','\u5b93',\"\u84ec\",'\u5168',\"\u90d7\",'\u73ed','\u4ef0','\u79cb','\u4ef2',\"\u4f0a\",'\u5bab',\"\u5b81\",'\u4ec7','\u683e','\u66b4',\"\u7518\",'\u659c','\u5389','\u620e',\"\u7956\",'\u6b66','\u7b26','\u5218','\u666f','\u8a79','\u675f','\u9f99','\u53f6','\u5e78','\u53f8',\"\u97f6\",'\u90dc','\u9ece','\u84df',\"\u8584\",'\u5370','\u5bbf','\u767d','\u6000','\u84b2','\u90b0',\"\u4ece\",'\u9102','\u7d22',\"\u54b8\",'\u7c4d','\u8d56','\u5353',\"\u853a\",'\u5c60','\u8499','\u6c60','\u4e54','\u9634','\u90c1','\u80e5','\u80fd','\u82cd',\"\u53cc\",'\u95fb','\u8398','\u515a','\u7fdf','\u8c2d','\u8d21','\u52b3','\u9004','\u59ec','\u7533','\u6276','\u5835','\u5189','\u5bb0','\u90e6',\"\u96cd\",'\u90e4','\u74a9',\"\u6851\",'\u6842',\"\u6fee\",'\u725b',\"\u5bff\",'\u901a',\"\u8fb9\",\"\u6248\",'\u71d5','\u5180','\u90cf',\"\u6d66\",'\u5c1a',\"\u519c\",'\u6e29','\u522b','\u5e84','\u664f','\u67f4',\"\u77bf\",'\u960e',\"\u5145\",'\u6155','\u8fde','\u8339','\u4e60','\u5ba6','\u827e','\u9c7c','\u5bb9','\u5411','\u53e4','\u6613','\u614e','\u6208','\u5ed6','\u5ebe','\u7ec8','\u66a8','\u5c45','\u8861','\u6b65','\u90fd','\u803f','\u6ee1','\u5f18','\u5321','\u56fd','\u6587','\u5bc7','\u5e7f','\u7984','\u9619','\u4e1c','\u6b27','\u6bb3','\u6c83','\u5229','\u851a','\u8d8a','\u5914','\u9686','\u5e08','\u5de9','\u538d','\u8042','\u6641','\u52fe','\u6556','\u878d','\u51b7','\u8a3e','\u8f9b','\u961a','\u90a3','\u7b80','\u9976','\u7a7a','\u66fe','\u6bcb','\u6c99','\u4e5c','\u517b','\u97a0','\u987b','\u4e30','\u5de2','\u5173','\u84af','\u76f8','\u67e5','\u540e','\u8346','\u7ea2','\u6e38','\u7afa','\u6743','\u9011','\u76d6','\u76ca','\u6853','\u516c','\u4ec9','\u7763','\u664b','\u695a','\u960e','\u6cd5','\u6c5d','\u9122','\u6d82','\u94a6','\u5cb3','\u5e05','\u7f11',\"\u4ea2\",'\u51b5','\u540e','\u6709','\u7434','\u5f52','\u6d77','\u58a8','\u54c8','\u8c2f','\u7b2a','\u5e74','\u7231','\u9633','\u4f5f','\u5546','\u725f','\u4f58','\u4f74','\u4f2f','\u8d4f',\"\u845b\u8bf8\".split(\"\").reverse().join(\"\"));findAnswer();var _0x97a11=new Array(\"\u7487\u5b50\".split(\"\").reverse().join(\"\"),'\u6dfc',\"\u680b\u56fd\".split(\"\").reverse().join(\"\"),\"\u5b50\u592b\".split(\"\").reverse().join(\"\"),'\u745e\u5802','\u751c','\u654f','\u5c1a','\u56fd\u8d24',\"\u7965\u8d3a\".split(\"\").reverse().join(\"\"),'\u6668\u6d9b',\"\u8f69\u660a\".split(\"\").reverse().join(\"\"),\"\u8f69\u6613\".split(\"\").reverse().join(\"\"),'\u76ca\u8fb0','\u76ca\u5e06','\u76ca\u5189','\u747e\u6625','\u747e\u6606','\u6625\u9f50','\u6768',\"\u660a\u6587\".split(\"\").reverse().join(\"\"),\"\u4e1c\u4e1c\".split(\"\").reverse().join(\"\"),\"\u9716\u96c4\".split(\"\").reverse().join(\"\"),'\u6d69\u6668',\"\u6db5\u7199\".split(\"\").reverse().join(\"\"),'\u6eb6\u6eb6','\u51b0\u67ab','\u6b23\u6b23','\u5b9c\u8c6a','\u6b23\u6167',\"\u653f\u5efa\".split(\"\").reverse().join(\"\"),'\u7f8e\u6b23','\u6dd1\u6167',\"\u8f69\u6587\".split(\"\").reverse().join(\"\"),'\u6587\u6770','\u6b23\u6e90','\u5fe0\u6797',\"\u6da6\u6995\".split(\"\").reverse().join(\"\"),'\u6b23\u6c5d',\"\u5609\u6167\".split(\"\").reverse().join(\"\"),'\u65b0\u5efa','\u5efa\u6797',\"\u83f2\u4ea6\".split(\"\").reverse().join(\"\"),'\u6797',\"\u6d01\u51b0\".split(\"\").reverse().join(\"\"),\"\u6b23\u4f73\".split(\"\").reverse().join(\"\"),\"\u6db5\u6db5\".split(\"\").reverse().join(\"\"),'\u79b9\u8fb0','\u6df3\u7f8e','\u6cfd\u60e0','\u4f1f\u6d0b',\"\u8d8a\u6db5\".split(\"\").reverse().join(\"\"),'\u6da6\u4e3d','\u7fd4','\u6dd1\u534e','\u6676\u83b9','\u51cc\u6676','\u82d2\u6eaa','\u96e8\u6db5','\u5609\u6021',\"\u6bc5\u4f73\".split(\"\").reverse().join(\"\"),\"\u8fb0\u5b50\".split(\"\").reverse().join(\"\"),'\u4f73\u742a',\"\u8f69\u7d2b\".split(\"\").reverse().join(\"\"),'\u745e\u8fb0','\u6615\u854a','\u840c',\"\u8fdc\u660e\".split(\"\").reverse().join(\"\"),'\u6b23\u5b9c','\u6cfd\u8fdc','\u6b23\u6021',\"\u6021\u4f73\".split(\"\").reverse().join(\"\"),\"\u60e0\u4f73\".split(\"\").reverse().join(\"\"),'\u6668\u831c',\"\u7490\u6668\".split(\"\").reverse().join(\"\"),'\u8fd0\u660a','\u6c5d\u946b',\"\u541b\u6dd1\".split(\"\").reverse().join(\"\"),\"\u6ee2\u6676\".split(\"\").reverse().join(\"\"),\"\u838e\u6da6\".split(\"\").reverse().join(\"\"),\"\u6c55\u6995\".split(\"\").reverse().join(\"\"),'\u4f73\u94b0',\"\u7389\u4f73\".split(\"\").reverse().join(\"\"),'\u6653\u5e86',\"\u9e23\u4e00\".split(\"\").reverse().join(\"\"),'\u8bed\u6668','\u6dfb\u6c60','\u6dfb\u660a','\u96e8\u6cfd','\u96c5\u6657','\u96c5\u6db5',\"\u598d\u6e05\".split(\"\").reverse().join(\"\"),'\u8bd7\u60a6',\"\u4e50\u5609\".split(\"\").reverse().join(\"\"),'\u6668\u6db5','\u5929\u8d6b',\"\u50b2\u73a5\".split(\"\").reverse().join(\"\"),'\u4f73\u660a','\u5929\u660a',\"\u840c\u840c\".split(\"\").reverse().join(\"\"),'\u82e5\u840c','\u79cb\u767d','\u5357\u98ce','\u9189\u5c71','\u521d\u5f64',\"\u6d77\u51dd\".split(\"\").reverse().join(\"\"),\"\u6587\u7d2b\".split(\"\").reverse().join(\"\"),'\u51cc\u6674','\u9999\u5349','\u96c5\u7434',\"\u5b89\u50b2\".split(\"\").reverse().join(\"\"),\"\u4e4b\u50b2\".split(\"\").reverse().join(\"\"),'\u521d\u8776','\u5bfb\u6843','\u4ee3\u82b9','\u8bd7\u971c','\u6625\u67cf',\"\u590f\u7eff\".split(\"\").reverse().join(\"\"),'\u78a7\u7075','\u8bd7\u67f3',\"\u67f3\u590f\".split(\"\").reverse().join(\"\"),\"\u767d\u91c7\".split(\"\").reverse().join(\"\"),'\u6155\u6885',\"\u5b89\u4e50\".split(\"\").reverse().join(\"\"),'\u51ac\u83f1',\"\u5b89\u7d2b\".split(\"\").reverse().join(\"\"),'\u5b9b\u51dd','\u96e8\u96ea','\u6613\u771f','\u5b89\u8377','\u9759\u7af9','\u98de\u96ea','\u96ea\u5170',\"\u971c\u96c5\".split(\"\").reverse().join(\"\"),'\u4ece\u84c9','\u51b7\u96ea','\u9756\u5de7',\"\u4e1d\u7fe0\".split(\"\").reverse().join(\"\"),'\u89c5\u7fe0','\u51e1\u767d','\u4e50\u84c9','\u8fce\u6ce2','\u4e39\u70df','\u68a6\u65cb','\u4e66\u53cc','\u5ff5\u6843',\"\u5929\u591c\".split(\"\").reverse().join(\"\"),\"\u6843\u6d77\".split(\"\").reverse().join(\"\"),'\u9752\u9999','\u6068\u98ce','\u5b89\u7b60','\u89c5\u67d4','\u521d\u5357','\u79cb\u8776','\u5343\u6613','\u5b89\u9732','\u8bd7\u854a','\u5c71\u96c1','\u53cb\u83f1','\u9999\u9732','\u6653\u5170','\u6db5\u7476','\u79cb\u67d4','\u601d\u83f1','\u9189\u67f3','\u4ee5\u5bd2','\u8fce\u590f','\u5411\u96ea','\u9999\u83b2',\"\u4e39\u4ee5\".split(\"\").reverse().join(\"\"),\"\u51dd\u4f9d\".split(\"\").reverse().join(\"\"),'\u5982\u67cf','\u96c1\u83f1',\"\u7af9\u51dd\".split(\"\").reverse().join(\"\"),'\u5b9b\u767d','\u521d\u67d4','\u5357\u857e','\u4e66\u8431',\"\u69d0\u68a6\".split(\"\").reverse().join(\"\"),'\u9999\u82b9','\u5357\u7434','\u7eff\u6d77','\u6c9b\u513f','\u6653\u7476',\"\u6625\u542c\".split(\"\").reverse().join(\"\"),'\u6613\u5de7','\u5ff5\u4e91','\u6653\u7075',\"\u67ab\u9759\".split(\"\").reverse().join(\"\"),\"\u84c9\u590f\".split(\"\").reverse().join(\"\"),'\u5982\u5357','\u5e7c\u4e1d','\u79cb\u767d','\u51b0\u5b89','\u51dd\u8776','\u7d2b\u96ea','\u5ff5\u53cc','\u5ff5\u771f','\u66fc\u5bd2','\u51e1\u971c','\u767d\u5349','\u8bed\u5c71','\u51b7\u73cd','\u79cb\u7fe0','\u590f\u67f3','\u5982\u4e4b','\u5fc6\u5357','\u4e66\u6613','\u7fe0\u6843','\u5bc4\u7476','\u5982\u66fc','\u95ee\u67f3','\u9999\u6885','\u5e7b\u6843','\u53c8\u83e1',\"\u7eff\u6625\".split(\"\").reverse().join(\"\"),'\u9189\u8776',\"\u7eff\u4ea6\".split(\"\").reverse().join(\"\"),'\u8bd7\u73ca','\u542c\u82b9',\"\u4e4b\u65b0\".split(\"\").reverse().join(\"\"),'\u535a\u701a','\u535a\u8d85','\u624d\u54f2','\u624d\u4fca','\u6210\u548c','\u6210\u5f18','\u660a\u82cd','\u660a\u660a','\u660a\u7a7a','\u660a\u4e7e','\u660a\u7136','\u660a\u7136','\u660a\u5929','\u660a\u7131','\u660a\u82f1','\u6d69\u6ce2','\u6d69\u535a','\u6d69\u521d','\u6d69\u5927','\u6d69\u5b95','\u6d69\u8361',\"\u6b4c\u6d69\".split(\"\").reverse().join(\"\"),'\u6d69\u5e7f',\"\u6d69\u6d86\",'\u6d69\u701a','\u6d69\u6d69','\u6d69\u6168','\u6d69\u65f7','\u6d69\u9614','\u6d69\u6f2b',\"\u6dfc\u6d69\".split(\"\").reverse().join(\"\"),'\u6d69\u6e3a',\"\u9088\u6d69\".split(\"\").reverse().join(\"\"),'\u6d69\u6c14','\u6d69\u7136',\"\u7a70\u6d69\".split(\"\").reverse().join(\"\"),'\u6d69\u58e4','\u6d69\u601d','\u6d69\u8a00','\u7693\u8f69','\u548c\u853c','\u548c\u5b89',\"\u6636\u548c\".split(\"\").reverse().join(\"\"),\"\u4e1c\u7fd4\".split(\"\").reverse().join(\"\"),'\u660a\u4f1f','\u695a\u6865','\u667a\u9716','\u6d69\u6770','\u708e\u627f','\u601d\u54f2','\u749f\u65b0','\u695a\u6000','\u7ee7\u667a','\u662d\u65fa','\u4fca\u6cfd',\"\u4e2d\u5b50\".split(\"\").reverse().join(\"\"),'\u7fbd\u777f','\u5609\u96f7','\u9e3f\u7fd4','\u660e\u8f69','\u68cb\u9f50','\u8f76\u4e50','\u662d\u6613','\u81fb\u7fd4','\u6cfd\u946b','\u82ae\u519b','\u6d69\u5955','\u5b8f\u660e',\"\u8d24\u5fe0\".split(\"\").reverse().join(\"\"),\"\u9526\u8f89\",'\u5143\u6bc5','\u9708\u80dc','\u5b87\u5cfb','\u5b50\u535a','\u8bed\u9716','\u80dc\u4f51','\u4fca\u6d9b','\u6d69\u6dc7','\u4e50\u822a','\u6cfd\u6977','\u5609\u5b81','\u656c\u5ba3',\"\u5b81\u97e6\".split(\"\").reverse().join(\"\"),'\u5efa\u65b0','\u5b87\u6000','\u7693\u7384','\u51a0\u6377','\u4fca\u94ed',\"\u9e23\u4e00\".split(\"\").reverse().join(\"\"),'\u5802\u8000','\u8f69\u51dd','\u8230\u66e6','\u8dc3\u946b','\u6893\u6770','\u7b71\u5b87','\u5f18\u6d9b','\u7fbf\u5929','\u5e7f\u5609','\u9646\u94ed','\u5fd7\u537f','\u8fde\u5f6c','\u666f\u667a','\u5b5f\u6615','\u7fbf\u7136','\u6587\u6e0a','\u7fbf\u6966','\u6657\u6631','\u6657\u65e5','\u6db5\u7545','\u6db5\u6da4','\u660a\u7a79',\"\u4eae\u6db5\".split(\"\").reverse().join(\"\"),'\u6db5\u5fcd','\u6db5\u5bb9','\u4fca\u53ef','\u667a\u9e4f','\u8bda\u94b0','\u4e66\u58a8','\u4fca\u6613','\u6d69\u6e3a','\u5bb8\u6c34',\"\u8bb8\u5609\".split(\"\").reverse().join(\"\"),'\u65f6\u8d24','\u98de\u817e','\u6c82\u6668',\"\u658c\u6bbf\".split(\"\").reverse().join(\"\"),'\u9704\u9e3f','\u8fb0\u7565','\u6f9c\u9e3f','\u666f\u535a','\u54a8\u6db5','\u4fee\u5fb7','\u666f\u8f89','\u8bed\u65cb','\u667a\u9038','\u9e3f\u950b','\u601d\u68b5','\u5f08\u714a','\u6cf0\u6cb3','\u901e\u5b87','\u5609\u98a2','\u9526\u6c85','\u98a2\u7131','\u8427\u5f6c',\"\u5347\u60a6\".split(\"\").reverse().join(\"\"),\"\u97f3\u9999\".split(\"\").reverse().join(\"\"),'\u70e8\u67e0','\u98a2\u548f','\u4ec1\u8d24','\u5c1a\u7136','\u7fbf\u9cde','\u6708\u9e3f','\u5065\u9716','\u9e3f\u660a','\u7ae3\u6770','\u53ef\u987a','\u70af\u4e50','\u4fca\u5f66','\u6d77\u6ca7','\u6377\u660e','\u98de\u626c','\u6770\u8fb0','\u7fbd\u6377','\u66e6\u6674','\u88d5\u9e3f','\u7fcc\u9526','\u6c90\u5bb8',\"\u540c\u798f\".split(\"\").reverse().join(\"\"),'\u65fb\u9a70','\u9f99\u5b81','\u6587\u8679','\u4e49\u51e1','\u5e7f\u6668','\u5bb8\u6ed4',\"\u5c90\u5609\".split(\"\").reverse().join(\"\"),'\u96c5\u73fa','\u777f\u660e','\u7693\u8f69','\u7a0b\u5929','\u5b50\u915d','\u827e\u5eb7','\u5982\u7fbd','\u51a0\u7389','\u5b50\u6b49','\u6c38\u660a','\u9f99\u534e',\"\u989c\u5146\".split(\"\").reverse().join(\"\"),'\u5947\u6587','\u6708\u6615',\"\u9526\u88d5\".split(\"\").reverse().join(\"\"),\"\u4f73\u6602\".split(\"\").reverse().join(\"\"),\"\u6d69\u660a\".split(\"\").reverse().join(\"\"),'\u5b87\u97ec','\u777f\u7113','\u6c38\u8bd1','\u9e3f\u5f6c','\u98a2\u9716','\u76ca\u5f6c','\u8679\u660a','\u98de\u60a6','\u777f\u73cf','\u5bb5\u7ae5','\u777f\u9e3f','\u5bb9\u51b0','\u9038\u6fe0','\u6977\u5ca9','\u5f18\u4e49','\u6d77\u8426','\u660a\u5b7a',\"\u94ed\u6893\".split(\"\").reverse().join(\"\"),'\u751f\u948a',\"\u73ba\u84dd\".split(\"\").reverse().join(\"\"),'\u6668\u8f95','\u5b87\u83e1','\u781a\u6d77','\u6587\u63e9','\u97ec\u745e','\u5f66\u7ea2','\u5955\u97e6','\u6e05\u4e88',\"\u7ffc\u5b81\".split(\"\").reverse().join(\"\"),\"\u777f\u51ac\".split(\"\").reverse().join(\"\"),'\u9526\u660c','\u70e8\u5b81','\u660c\u6743',\"\u7814\u56fd\".split(\"\").reverse().join(\"\"),'\u5fb7\u8fd0','\u5b5d\u6e05','\u4f73\u9633','\u51ef\u73ae','\u6b63\u771f','\u6c11\u4e91','\u6615\u51b6','\u529b\u5a01','\u5e05\u6b23','\u77e5\u6df3','\u70e8\u98de','\u5174\u8fdc','\u5b50\u58a8','\u6f84\u6b23','\u70e8\u714a','\u60a6\u52e4','\u6668\u6d25',\"\u5b8f\u535a\".split(\"\").reverse().join(\"\"),'\u80b2\u840c','\u7fbd\u70ab','\u7ecd\u94a7','\u777f\u660c','\u6cd3\u5343','\u98a2\u709c',\"\u91d1\u8679\".split(\"\").reverse().join(\"\"),'\u7b60\u822a','\u5143\u7532','\u661f\u660e','\u666f\u6d9b','\u94ed\u8679','\u5fb7\u672c','\u5411\u8f89','\u57fa\u7fd4','\u5bb6\u6613',\"\u9e4f\u6b23\".split(\"\").reverse().join(\"\"),'\u7fbd\u8343','\u6cfd\u5bb9','\u5f18\u4eae','\u5c1a\u5ef7','\u8f69\u6893','\u752b\u6d25','\u5f6c\u6977','\u5bc5\u98de','\u6109\u541b','\u9633\u5e73','\u8a89\u6770','\u94a6\u662d','\u8574\u85c9','\u7fbd\u7a0b','\u5b8f\u6d77','\u6db5\u7545','\u5149\u6d69','\u4ee4\u6c82','\u6d69\u6d69','\u777f\u9526','\u6613\u6cfd','\u4fca\u5eb7','\u5bb6\u6587','\u6668\u5143','\u8bed\u6d0b','\u88d5\u5b8f','\u6893\u699b','\u9633\u5609','\u6052\u5c55','\u96e8\u8fdc','\u54f2\u4f0a','\u9038\u6c5f',\"\u6e90\u4e30\".split(\"\").reverse().join(\"\"),'\u5b66\u4e1c',\"\u5947\u5ca9\",'\u6d69\u8d22','\u548c\u853c','\u7ea2\u8a00','\u745e\u8d6b','\u68ee\u5706','\u6b23\u8d62','\u6893\u9e3f','\u535a\u660e','\u94ed\u80b2','\u98a2\u7855','\u5b87\u70ef','\u5b87\u5982','\u6df3\u708e',\"\u627f\u6e90\".split(\"\").reverse().join(\"\"),'\u658c\u5f6c','\u98de\u6c89','\u9e3f\u7490','\u660a\u5f18');var _0x4447b7=_0x5ac617['length'];var _0xe69bd7=_0x97a11['length'];var _0x590fdf=parseInt(Math['random']()*_0x4447b7);var _0x425bf8=parseInt(Math['random']()*_0xe69bd7);var _0x327e43=_0x5ac617[_0x590fdf]+_0x97a11[_0x425bf8];return _0x327e43;}function getMoble(){var _0x5271fa=new Array('130','131','132','133','135','137','138','170','187','189');findAnswer();var _0x429d5c=parseInt((0xe854a^0xe8540)*Math['random']());var _0x4f3de1=_0x5271fa[_0x429d5c];for(var _0x5ed1a4=0x27315^0x27315;_0x5ed1a4<(0xb74f5^0xb74fd);_0x5ed1a4++){_0x4f3de1=_0x4f3de1+Math['floor'](Math['random']()*0xa);}return _0x4f3de1;}function voteByBili(_0x58ed0d){toupiao=[];temp_flag=0x0;temp_answer=[];findAnswer();while(!(temp_flag==_0x58ed0d['length'])){let _0x360685=-0x3e7;toupiao=[];for(let _0x9e036d=0xc2cdd^0xc2cdd;_0x9e036d<_0x58ed0d['length'];_0x9e036d++){toupiao['push'](0x0);}while((_0x360685=find_only_max(toupiao))<-(0xa6a76^0xa6a77)){for(let _0xaa8d7b=0x0;_0xaa8d7b<_0x58ed0d['length'];_0xaa8d7b++){let _0x349527=![];for(let _0x618c14=0x0;_0x618c14<temp_answer['length'];_0x618c14++){if(temp_answer[_0x618c14]==_0xaa8d7b){_0x349527=!![];break;}}if(_0x349527){continue;}if(duoxuan(_0x58ed0d[_0xaa8d7b])){toupiao[_0xaa8d7b]++;}}}temp_answer['push'](_0x360685);temp_flag+=0x5bdd9^0x5bdd8;}console['log'](temp_answer);temp_answer2=Array['from'](temp_answer);for(let _0x176151=0xd2f54^0xd2f54;_0x176151<temp_answer['length'];_0x176151++){let _0x5a5499=temp_answer2[_0x176151];for(let _0x197829=0x0;_0x197829<_0x176151;_0x197829++){if(temp_answer2[_0x197829]>temp_answer2[_0x176151]){_0x5a5499++;}}temp_answer[_0x176151]=_0x5a5499;}return temp_answer;}function normalizeList(_0x26686b){findAnswer();const _0x7e4055=_0x26686b['map'](_0x47413d=>parseFloat(_0x47413d));const _0x270288=_0x7e4055['reduce']((_0x34264f,_0x34ea58)=>_0x34264f+_0x34ea58,0x0);const _0x229fb3=_0x7e4055['map'](_0x3487b1=>Math['round'](_0x3487b1/_0x270288*(0x63e00^0x63e64)));const _0x24abd1=0x64-_0x229fb3['reduce']((_0x4eb7bd,_0x4bc39d)=>_0x4eb7bd+_0x4bc39d,0x0);_0x229fb3[0x967b3^0x967b3]+=_0x24abd1;return _0x229fb3;}\n\n                       //end...\n                    })();\n                    ";
        console.log(E);l=new Date;M=l.getFullYear();e=String(l.getMonth()+1).padStart(2,"0");m=String(l.getDate()).padStart(2,"0");g=String(l.getHours()).padStart(2,"0");I=String(l.getMinutes()).padStart(2,"0");l=String(l.getSeconds()).padStart(2,"0");var v="\u6cb9\u7334\u811a\u672cVM\u7248_"+M+e+m+g+I+l+".txt";M=new Blob([E],{type:"text/plain;charset=utf-8"});saveAs(M,v);layui.use("layer",function(){var p=layui.layer;setTimeout(function(){p.msg("\u811a\u672c\u751f\u6210\u6210\u529f\uff01",{icon:1})},1E3);
            console.log(v)})});for(var x=0;x<a.length;x++)(function(e){function m(h,w,r){r=void 0===r?null:r;null===r&&(r=Math.floor(w/h));for(var q=[],C=0;C<h-1;C++)q.push(r);q.push(w-r*(h-1));return q}function g(h){h.find("input").on("input",function(){var w=$(this).val();""!==w&&"\u5e73\u5747\u6bd4\u4f8b"!==w&&(w=parseInt(w),(isNaN(w)||0>w||100<w)&&$(this).val(""))});h.on("input",function(){var w=$(this).val();""!==w&&"\u5e73\u5747\u6bd4\u4f8b"!==w&&(w=parseInt(w),(isNaN(w)||0>w||100<w)&&$(this).val(""))})}
        var l=$('<button type="button" class="layui-btn layui-btn-xs" id="test_btn_'+(e+1)+'" style="margin-left:1rem;">\u6d4b\u8bd5alert</button>'),y=$('<button type="button" class="layui-btn layui-btn-xs oneRandom2" id="random_bili_btn_'+(e+1)+'" style="margin-left:1rem;">\u751f\u6210\u968f\u673a\u6bd4\u4f8b</button>'),I=$('<button type="button" class="layui-btn layui-btn-xs oneRandom1" id="average_btn_'+(e+1)+'" style="margin-left:1rem;">\u751f\u6210\u5e73\u5747\u6bd4\u4f8b</button>'),E=$('<button type="button" class="layui-btn layui-btn-xs oneRandom1" id="fill0_btn_btn_'+
                (e+1)+'" style="margin-left:1rem;">\u5c06\u5176\u4f59\u9009\u9879\u6bd4\u4f8b\u88650</button>'),M=$('<label class="layui-input-block">info: '+a.eq(e).attr("type")+"</label>"),T=$('<textarea type="text" class="layui-input custom-input" style="width:100%;  height:100px;" placeholder="\u8f93\u5165\u968f\u673a\u6587\u672c\u5185\u5bb9\uff0c\u5982\u6709\u591a\u4e2a\u6587\u672c\u968f\u673a\u8bf7\u7528\u82f1\u6587\u9017\u53f7\u9694\u5f00,\u4f8b\u5982\uff1a\u559c\u7f8a\u7f8a,\u7f8e\u7f8a\u7f8a,\u6cb8\u7f8a\u7f8a&#10;\u5982\u679c\u9700\u8981\u8f93\u5165\u968f\u673a\u8303\u56f4\u5185\u6570\u5b57\uff0c\u8bf7\u8f93\u5165\uff1a@@\u968f\u673a\u6570(20,50)@@  \u4ee3\u886820\u523050\u8303\u56f4\u5185\u968f\u673a">'),
            v=a.eq(e).attr("type");l.on("click",function(h){if(1==v)h=a.eq(e).find(".custom-input").val(),alert("\u586b\u7a7a\u9898\u8f93\u5165\u5185\u5bb9\uff1a"+h);else if(3==v){var w="[",r=a.eq(e).find(".ui-radio .label").parent();r.each(function(N,O){var K=$(O).find(".custom-input").val();w+=N==r.length-1?K:K+","});w+="]";alert(w)}else if(4==v){var q="[",C=a.eq(e).find(".ui-checkbox .label").parent();C.each(function(N,O){var K=$(O).find(".custom-input").val();q+=N==C.length-1?K:K+","});q+="]";alert(q)}else if(6==
            v){var J=a.eq(e).find(".custom-input"),z="[";J.each(function(N,O){var K=$(O).val();z+=N==J.length-1?K:K+","});z+="]";alert("\u4e0b\u62c9\u6846\u9898\u578b\u8f93\u5165\u6bd4\u4f8b\uff1a"+z)}else if(7==v){var F=a.eq(e).find(".custom-input"),L="[";F.each(function(N,O){var K=$(O).val();L+=N==F.length-1?K:K+","});L+="]";alert("\u6392\u5e8f\u9898\u578b\u8f93\u5165\u6bd4\u4f8b\uff1a"+L)}else if(11==v){var P=a.eq(e).find(".custom-input"),R="[";P.each(function(N,O){var K=$(O).val();R+=N==P.length-1?K:K+","});
            R+="]";alert("\u77e9\u9635\u6ed1\u6761\u9898\u578b\u8f93\u5165\u6bd4\u4f8b\uff1a"+R)}else if(12==v){var Q=a.eq(e).find(".custom-input"),ba="[";Q.each(function(N,O){var K=$(O).val();ba+=N==Q.length-1?K:K+","});ba+="]";alert("\u77e9\u9635\u6ed1\u6761\u9898\u578b\u8f93\u5165\u6bd4\u4f8b\uff1a"+ba)}else if(8==v){var Ba=a.eq(e).find(".custom-input"),la="[";Ba.each(function(N,O){var K=$(O).val();la+=N==Ba.length-1?K:K+","});la+="]";alert("\u6ed1\u6761\u9898\u578b\u8f93\u5165\u6bd4\u4f8b\uff1a"+la)}else if(9==
            v){var Ca=a.eq(e).find(".custom-input"),ma="[";Ca.each(function(N,O){var K=$(O).val();ma+=N==Ca.length-1?K:K+","});ma+="]";alert("NPS\u91cf\u6807\u9898\u9898\u578b\u8f93\u5165\u6bd4\u4f8b\uff1a"+ma)}else if(5==v){var Da=a.eq(e).find(".custom-input"),na="[";Da.each(function(N,O){var K=$(O).val();na+=N==Da.length-1?K:K+","});na+="]";alert("\u5355\u9009\u91cf\u6807\u9898\u9898\u578b\u8f93\u5165\u6bd4\u4f8b\uff1a"+na)}else alert("\u672a\u5904\u7406\u7b2c"+(x+1)+"\u9898\uff0c\u6682\u4e0d\u652f\u6301\u8be5\u9898\u578b")});
        y.on("click",function(h){if(3==v){h=a.eq(e).find(".ui-radio .label").parent();var w=H(h.length,100);h.each(function(z,F){$(F).find(".custom-input").val(w[z])})}else if(4==v)a.eq(e).find(".ui-checkbox .label").parent().each(function(z,F){var L=$(F).find(".custom-input"),P=A(10,100);L.val(P)});else if(6==v){var r=a.eq(e).find(".matrix-rating a").hasClass("rate-offlarge")?"single":"multiple";a.eq(e).find(".matrixtable tbody tr:not(.trlabel)").each(function(z,F){var L=$(F).find("input.custom-input");
            if("single"===r){var P=H(L.length,100);L.each(function(R,Q){$(Q).val(P[R])})}else"multiple"===r&&L.each(function(R,Q){var ba=A(10,100);$(Q).val(ba)})})}else if(7==v){h=a.eq(e).find(".custom-input");var q=H(h.length,100);h.each(function(z,F){$(F).val(q[z])})}else if(11==v)h=a.eq(e).find(".custom-input"),H(h.length,100),h.each(function(z,F){var L=$(F),P=A(10,90);L.val(P)});else if(12==v||9==v)a.eq(e).find(".table-row").each(function(z,F){var L=$(F).find(".input-cell input.custom-input"),P=H(L.length,
            100);L.each(function(R,Q){$(Q).val(P[R])})});else if(8==v){h=a.eq(e).find(".custom-input");var C=H(h.length,100);h.each(function(z,F){$(F).val(C[z])})}else if(5==v){h=a.eq(e).find(".custom-input");var J=H(h.length,100);h.each(function(z,F){$(F).val(J[z])})}else alert("\u672a\u5904\u7406\u7b2c"+(e+1)+"\u9898\uff0c\u6682\u4e0d\u652f\u6301\u8be5\u9898\u578b")});E.on("click",function(h){if(3==v)a.eq(e).find(".ui-radio .label").parent().each(function(r,q){var C=$(q).find(".custom-input");C.val()||C.val(0)});
        else if(4==v)a.eq(e).find(".ui-checkbox .label").parent().each(function(r,q){var C=$(q).find(".custom-input");C.val()||C.val(0)});else if(6==v){var w=a.eq(e).find(".matrix-rating a").hasClass("rate-offlarge")?"single":"multiple";a.eq(e).find(".matrixtable tbody tr:not(.trlabel)").each(function(r,q){var C=$(q).find("input.custom-input");"single"===w?C.each(function(J,z){$(z).val()||$(z).val(0)}):"multiple"===w&&C.each(function(J,z){$(z).val()||$(z).val(0)})})}else 7==v?a.eq(e).find(".custom-input").each(function(r,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             q){$(q).val()||$(q).val(0)}):11==v?(h=a.eq(e).find(".custom-input"),H(h.length,100),h.each(function(r,q){var C=$(q);C.val()||C.val(0)})):12==v||9==v?a.eq(e).find(".table-row").each(function(r,q){$(q).find(".input-cell input.custom-input").each(function(C,J){$(J).val()||$(J).val(0)})}):8==v?a.eq(e).find(".custom-input").each(function(r,q){$(q).val()||$(q).val(0)}):5==v?a.eq(e).find(".custom-input").each(function(r,q){$(q).val()||$(q).val(0)}):alert("\u672a\u5904\u7406\u7b2c"+(e+1)+"\u9898\uff0c\u6682\u4e0d\u652f\u6301\u8be5\u9898\u578b")});
        I.on("click",function(h){if(3==v){h=a.eq(e).find(".ui-radio .label").parent();var w=m(h.length,100);h.each(function(z,F){$(F).find(".custom-input").val(w[z])})}else if(4==v)a.eq(e).find(".ui-checkbox .label").parent().each(function(z,F){$(F).find(".custom-input").val(50)});else if(6==v){var r=a.eq(e).find(".matrix-rating a").hasClass("rate-offlarge")?"single":"multiple";a.eq(e).find(".matrixtable tbody tr:not(.trlabel)").each(function(z,F){var L=$(F).find("input.custom-input");if("single"===r){var P=
            m(L.length,100);L.each(function(R,Q){$(Q).val(P[R])})}else"multiple"===r&&L.each(function(R,Q){$(Q).val(50)})})}else if(7==v){h=a.eq(e).find(".custom-input");var q=10<h.length?Array(h.length).fill("\u5e73\u5747\u6bd4\u4f8b"):m(h.length,100);h.each(function(z,F){$(F).val(q[z])})}else if(11==v)a.eq(e).find(".custom-input").each(function(z,F){$(F).val(50)});else if(12==v||9==v)a.eq(e).find(".table-row").each(function(z,F){var L=$(F).find(".input-cell input.custom-input");var P=m(L.length,100);L.each(function(R,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Q){$(Q).val(P[R])})});else if(8==v){h=a.eq(e).find(".custom-input");var C=m(h.length,100);h.each(function(z,F){$(F).val(C[z])})}else if(5==v){h=a.eq(e).find(".custom-input");var J=m(h.length,100);h.each(function(z,F){$(F).val(J[z])})}else alert("\u672a\u5904\u7406\u7b2c"+(e+1)+"\u9898\uff0c\u6682\u4e0d\u652f\u6301\u8be5\u9898\u578b")});b.eq(e).append(y);b.eq(e).append(I);b.eq(e).append(E);1==v&&(l=$('<div class="input-container"></div>'),l.append(T),a.eq(e).append(l),T.val(),y.remove(),I.remove(),
            E.remove(),y=$('<button type="button" class="layui-btn layui-btn-xs oneRandom_name" id="generateName_btn_'+(e+1)+'" style="margin-left:1rem;">\u751f\u6210\u968f\u673a\u59d3\u540d</button>'),I=$('<button type="button" class="layui-btn layui-btn-xs oneRandom_phone" id="generatePhone_btn_'+(e+1)+'" style="margin-left:1rem;">\u751f\u6210\u968f\u673a\u624b\u673a\u53f7</button>'),E=$('<button type="button" class="layui-btn layui-btn-xs oneRandom_advice" id="generateAdvice_btn_'+(e+1)+'" style="margin-left:1rem;">\u751f\u6210\u968f\u673a\u5efa\u8bae\u6027\u56de\u7b54</button>'),
            l=$('<button type="button" class="layui-btn layui-btn-xs oneRandom_advice" id="generateRandomNum_btn_'+(e+1)+'" style="margin-left:1rem;">\u751f\u6210\u968f\u673a\u8303\u56f4\u5185\u6570\u5b57\u6a21\u677f</button>'),E.on("click",function(h){h=T.val;for(var w="\u633a\u4e0d\u9519\u7684\u561b \u7ee7\u7eed\u52a0\u6cb9 \u505a\u5f97\u5f88\u597d \u7ee7\u7eed\u52aa\u529b \u6709\u8fdb\u6b65\u54e6 \u518d\u63a5\u518d\u5389 \u5f88\u68d2 \u7ee7\u7eed\u4fdd\u6301 \u4f60\u771f\u68d2 \u505a\u5f97\u5f88\u6f02\u4eae \u5e72\u5f97\u6f02\u4eae \u5389\u5bb3\u4e86 \u6709\u6f5c\u529b \u7ee7\u7eed\u5411\u524d \u52a0\u6cb9\u52a0\u6cb9 \u8868\u73b0\u4e0d\u9519 \u4f60\u5728\u8fdb\u6b65 \u5e0c\u671b\u4f60\u7ee7\u7eed\u52aa\u529b \u55ef\uff0c\u4e0d\u9519 \u52a0\u6cb9\u554a \u4e0d\u9519\u54e6 \u4fdd\u6301\u4f4f \u7ee7\u7eed\u53d1\u5c55 \u771f\u7684\u5f88\u8d5e \u7ee7\u7eed\u4fdd\u6301\u597d\u72b6\u6001 \u597d\u6837\u7684 \u8d8a\u6765\u8d8a\u597d \u771f\u5389\u5bb3 \u5f88\u6709\u6f5c\u529b \u52aa\u529b\u4e0d\u4f1a\u767d\u8d39\u7684 \u68d2\u68d2\u54d2 \u5e0c\u671b\u4f60\u7ee7\u7eed\u524d\u8fdb \u52a0\u628a\u52b2 \u7ee7\u7eed\u8fdb\u6b65 \u7ee7\u7eed\u4fdd\u6301\u54e6 \u575a\u6301\u5c31\u662f\u80dc\u5229 \u4f60\u5df2\u7ecf\u5728\u6b63\u786e\u7684\u8def\u4e0a\u4e86 \u5f88\u4e0d\u9519\u4e86 \u6709\u957f\u8fdb \u4f60\u662f\u6700\u68d2\u7684 \u4e00\u76f4\u52a0\u6cb9 \u518d\u4e0d\u6015\u56f0\u96be \u8d8a\u6765\u8d8a\u4f18\u79c0\u4e86 \u52a0\u500d\u52aa\u529b \u5e0c\u671b\u4f60\u7ee7\u7eed\u4fdd\u6301\u8fd9\u4e2a\u72b6\u6001 \u7ee7\u7eed\u4fdd\u6301\u8fdb\u6b65 \u6709\u8fdb\u6b65\u7a7a\u95f4 \u7ee7\u7eed\u4fdd\u6301\u52aa\u529b \u6709\u6f5c\u529b\u6210\u4e3a\u4f7c\u4f7c\u8005 \u7ee7\u7eed\u52a0\u6cb9\u5427 \u5e0c\u671b\u4f60\u7ee7\u7eed\u52aa\u529b\u4e0b\u53bb \u975e\u5e38\u4f18\u79c0".split(" "),
                                                                                                                                                                                                                                                                             r="",q=0;20>q;q++){var C=w[Math.floor(Math.random()*w.length)];r=19!==q?r+(C+", "):r+C}h.call(T,r)}),y.on("click",function(h){T.val("@@\u751f\u6210\u968f\u673a\u59d3\u540d@@")}),l.on("click",function(h){T.val("@@\u968f\u673a\u6570(10,20)@@")}),I.on("click",function(h){T.val("@@\u751f\u6210\u968f\u673a\u624b\u673a\u53f7@@")}),b.eq(e).append(E),b.eq(e).append(y),b.eq(e).append(I),b.eq(e).append(l));3==v&&(y=a.eq(e).find(".ui-radio .label").parent(),y.each(function(h,w){var r=$('<input type="text" class="layui-input custom-input" style="width:60%" placeholder="\u8f93\u5165\u9009\u9879\u6bd4\u4f8b(0-100\u7684\u6574\u6570)">');
            $(w).append(r);r.val()}),y.find("input").on("click",function(h){h.stopPropagation()}),g(y));4==v&&(y=a.eq(e).find(".ui-checkbox .label").parent(),y.each(function(h,w){var r=$('<input type="text" class="layui-input custom-input" style="width:60%" placeholder="\u8f93\u5165\u9009\u9879\u6bd4\u4f8b(0-100\u7684\u6574\u6570)">');$(w).append(r)}),y.find("input").on("click",function(h){h.stopPropagation()}),g(y));if(6==v){y=a.eq(e).find(".matrix-rating a").hasClass("rate-offlarge")?"single":"multiple";var p=
            a.eq(e).find(".matrix-rating a");p.each(function(h,w){var r=$('<input type="text" class="layui-input custom-input" style="width:90%;" placeholder="\u8f93\u5165\u6bd4\u4f8b">');$(w).after(r);r.on("click",function(q){q.stopPropagation()});g(p)});p.siblings("input").on("input",function(){var h=$(this).val();""!==h&&"\u5e73\u5747\u6bd4\u4f8b"!==h&&(h=parseInt(h),(isNaN(h)||1>h||100<h)&&$(this).val(""))});"single"===y?M.text("info: single"):"multiple"===y&&M.text("info: multiple")}if(7==v){var G=a.eq(e).find("select");
            M=G.find("option");var B=$('<table class="custom-table"></table>');M.each(function(h,w){if(0<h){$(w).val();var r=$(w).text(),q=$('<tr class="table-row"></tr>');r=$('<td class="option-cell"></td>').text(r);var C=$('<td class="input-cell"></td>'),J=$('<input type="text" class="layui-input custom-input" placeholder="\u8f93\u5165\u6bd4\u4f8b">');C.append(J);q.append(r);q.append(C);B.append(q);J.on("click",function(z){z.stopPropagation()});g(J)}});a.eq(e).append(B);G.on("change",function(){G.val()})}if(11==
            v){M=a.eq(e).find(".ui-li-static");var D=$('<table class="custom-table"></table>');M.each(function(h,w){$(w).find(".custom");var r=$(w).find("span").text(),q=$('<tr class="table-row"></tr>');r=$('<td class="option-cell"></td>').text(r);var C=$('<td class="input-cell"></td>'),J=$('<input type="text" class="layui-input custom-input" placeholder="\u8f93\u5165\u6bd4\u4f8b">');C.append(J);q.append(r);q.append(C);D.append(q);J.on("click",function(z){z.stopPropagation()});g(J)});a.eq(e).append(D)}if(8==
            v){var V=$('<table class="custom-table"></table>');a.eq(e).find(".rangeslider").find(".ruler .cm").each(function(h,w){var r=$(w).data("value")+"\u5206",q=$('<tr class="table-row"></tr>');r=$('<td class="option-cell"></td>').text(r);var C=$('<td class="input-cell"></td>'),J=$('<input type="text" class="layui-input custom-input" placeholder="\u8f93\u5165\u6bd4\u4f8b">');C.append(J);q.append(r);q.append(C);V.append(q);J.on("click",function(z){z.stopPropagation()});g(J)});a.eq(e).append(V)}if(9==v||12==
            v){var X=$('<table class="custom-table" style="font-size: 12px;"></table>');a.eq(e).find(".rangeslider").each(function(h,w){var r=$(w).find(".ruler .cm"),q=$('<tr class="table-row"></tr>');r.each(function(C,J){var z=$(J).data("value")+"\u5206";z=$('<td class="option-cell" style="padding: 5px;"></td>').text(z);var F=$('<td class="input-cell" style="padding: 5px;"></td>'),L=$('<input type="text" class="layui-input custom-input" style="width: 80px;" placeholder="\u8f93\u5165\u6bd4\u4f8b">');F.append(L);
            q.append(z);q.append(F);L.on("click",function(P){P.stopPropagation()});g(L)});X.append(q)});a.eq(e).find(".errorMessage").after(X)}if(5==v){M=a.eq(e).find(".scale-div");M.find(".scaleTitle_frist").text();M.find(".scaleTitle_last").text();M=M.find(".rate-off");var Y=$('<table class="custom-table" style="font-size: 12px;"></table>');M.each(function(h,w){var r=$(w).attr("title")+":",q=$('<tr class="table-row"></tr>');r=$('<td class="option-cell" style="padding: 5px;"></td>').text(r);var C=$('<td class="input-cell" style="padding: 5px;"></td>'),
            J=$('<input type="text" class="layui-input custom-input" style="width: 80px;" placeholder="\u8f93\u5165\u6bd4\u4f8b">');C.append(J);q.append(r);q.append(C);Y.append(q);g(J)});a.eq(e).find(".errorMessage").after(Y)}})(x)},Oa=function(){ka("#getmore_btn","https://greasyfork.org/zh-CN/scripts?q=EasyWJX");ka("#heydeveloper_btn","https://space.bilibili.com/36737233");W("#clear_elem_btn",function(){$("#spanPower").html('<a href="https://www.wjx.cn/" target="_blank" title="\u95ee\u5377\u661f_\u4e0d\u6b62\u95ee\u5377\u8c03\u67e5/\u5728\u7ebf\u8003\u8bd5">\u95ee\u5377\u661f</a><span>\u63d0\u4f9b\u6280\u672f\u652f\u6301</span>');
        ctrl_btn.style.display="none";for(var b=$(".data__tit_cjd"),a=0;a<b.length;a++)b.eq(a).text(b.eq(a).text().split(" \u9898\u76eeID\uff1a")[0]);layer.msg("\u5df2\u6e05\u7406\u9875\u9762")});W("#clear_ookie_btn",function(){U();ea();S()});W("#bypass_wechat_btn",function(){Aa()});W("#expand_page_btn",function(){xa()});W("#bypass_enterprise_btn",function(){za()})},ha=[],Z=[],ia=[function(b,a,f,d){ha.push('<button type="button" class="layui-btn layui-btn-warm easywjx_btn" id="'+a+'">'+b+"</button>");Z.push({func:d,
        id:a,event:f})},qa,ua,ka,W,wa,da,va,function(){var b=new aa,a=new ca;window.easywjx.getOneAnswer=b;window.easywjx.writeOneAnswer=a},function(b,a){var f=document.querySelectorAll(".field.ui-field-contain");console.log(b);window.easywjx.answer_ls=b;for(var d=0;d<f.length;d++){var u=ua(d,b);if("NOANSWER"==u)console.log("no_find_answer");else{console.log(u);try{a.radio(u,d)}catch(t){}try{a.input(u,d)}catch(t){}try{a.checkbox(u,d)}catch(t){}try{a.manyinput(u,d,".textCont")}catch(t){}try{a.manyinput(u,
        d,".bracket")}catch(t){}}}},ta,sa,ra,pa,fa,oa,function(b){var a=document.createElement("input");document.body.appendChild(a);a.setAttribute("value",b);a.select();document.execCommand("copy")&&(document.execCommand("copy"),console.log("\u590d\u5236\u6210\u529f"));document.body.removeChild(a)},ea,U,S];(function(){var b,a,f,d;return $jscomp.asyncExecutePromiseGeneratorProgram(function(u){1==u.nextAddress&&(console.log("EasyWJX is running. From xq.kzw.ink. Version 1.0"),$("head").append($('<link rel="stylesheet" href="https://www.layuicdn.com/layui/css/layui.css">')),
    $("head").append($('<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.1/jquery.min.js">\x3c/script>')),$("head").append('<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js">\x3c/script>'),$("head").append('<script src="https://cdn.jsdelivr.net/npm/layui@2.6.8/dist/layui.js">\x3c/script>'),$("head").append($('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/layui@2.6.8/dist/css/layui.css">')),"undefined"==typeof layer&&$("head").append('<script src="https://www.layuicdn.com/layer-v3.5.1/layer.js">\x3c/script>'),
    b=window.location.href,b.includes("/vj/")&&(window.location.href=b.replace("/vj/","/vm/").split("#")[0]));if(4!=u.nextAddress)return"undefined"!=typeof layer?u.jumpTo(4):u.yield(ra(.5),2);a=layer.load(1);setTimeout(function(){$(".textCont,input,textarea").off()},2E3);$(".textCont,input,textarea").off();document.oncontextmenu=function(){return!0};document.onselectstart=function(){return!0};$("body").css("user-select","text");Fa();setTimeout(function(){ya();ja();Ia();for(var t=document.querySelectorAll('fieldset[class="fieldset"]>div'),
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  x=0;x<t.length;x++)if("display"==t[x].style[0]){t[x].style="";layer.msg("\u6d4b\u5230\u8df3\u8f6c\u9898\u6216\u9690\u85cf\u9898\u76ee\u81ea\u52a8\u663e\u793a\u6240\u6709\u9898\u76ee");break}},1E3);f=setInterval(function(){Ha(f)},2E3);$("#spanPower").html('<a href="https://space.bilibili.com/36737233" title="UP\u4e3b\u9875\u6559\u7a0b">UP\u4e3b\u9875</a><span>\u5982\u6709\u9700\u8981\u53ef\u52a0QQ:835573228</span>');d=document.createElement("div");d.style.position="fixed";d.style.height="3rem";d.style.width=
    "3rem";d.style.background="url(https://s1.ax1x.com/2023/01/31/pS0yjEQ.png)";d.style.backgroundSize="2rem 2rem";d.style.backgroundColor="white";d.style.borderRadius="1.5rem";d.style.boxShadow="0px 0px 20px 1px Gray";d.style.backgroundRepeat="no-repeat";d.id="ctrl_btn";d.style.right="1rem";d.style.bottom="10rem";d.style.backgroundPosition="center";d.onclick=function(t){layer.open({type:1,skin:"layui-layer-rim",area:["80%","80%"],content:wa(),title:"\u66f4\u591a\u6309\u94ae",success:function(x,e){Oa();
        0<$(".score-font-style").length?$("#answer_done, #answer_done_line").css("display","block"):$("#answering, #answering_line").css("display","block");0!=ha.length&&($("#ext_line").css("display","block"),Ja());$(".easywjx_btn").on("click",function(){layer.close(e);console.log("\u6709\u4e00\u4e2a\u6309\u94ae\u88ab\u70b9\u51fb\uff0ctoolbox\u5df2\u5173\u95ed")})}})};$("body").append(d);Ma();layer.tips("\u70b9\u51fb\u4ee5\u4f7f\u7528EasyWJX","#ctrl_btn");Ga();Na();Ka();layer.close(a);u.jumpToEnd()})})();
    $("button").css("font-size","10px");c=document.createElement("style");c.innerHTML="\n    .custom-table {\n        width: 100%;\n        border-collapse: collapse;\n        margin-top: 10px;\n    }\n\n    .table-row {\n        border-bottom: 1px solid #ddd;\n    }\n\n    .option-cell,\n    .input-cell {\n        padding: 10px;\n        text-align: center;\n    }\n\n    .custom-input {\n        width: 80%;\n        box-sizing: border-box;\n    }\n    /* \u81ea\u5b9a\u4e49\u6309\u94ae\u6837\u5f0f */\n#oneRandom_btn{\n       font-size: 16px !important; /* \u8c03\u6574\u6309\u94ae\u5b57\u4f53\u5927\u5c0f */\n            width: 150px !important;    /* \u8c03\u6574\u6309\u94ae\u5bbd\u5ea6 */\n            height: 50px !important;    /* \u8c03\u6574\u6309\u94ae\u9ad8\u5ea6 */\n            background-color: orange !important; /* \u8bbe\u7f6e\u6309\u94ae\u80cc\u666f\u989c\u8272 */\n            color: white !important;    /* \u8bbe\u7f6e\u6309\u94ae\u6587\u672c\u989c\u8272 */\n}\n#oneRandom_btn2{\n       font-size: 16px !important; /* \u8c03\u6574\u6309\u94ae\u5b57\u4f53\u5927\u5c0f */\n            width: 150px !important;    /* \u8c03\u6574\u6309\u94ae\u5bbd\u5ea6 */\n            height: 50px !important;    /* \u8c03\u6574\u6309\u94ae\u9ad8\u5ea6 */\n            background-color: green !important; /* \u8bbe\u7f6e\u6309\u94ae\u80cc\u666f\u989c\u8272 */\n            color: white !important;    /* \u8bbe\u7f6e\u6309\u94ae\u6587\u672c\u989c\u8272 */\n}\n\n#generateTXT__btn{\n       font-size: 16px !important; /* \u8c03\u6574\u6309\u94ae\u5b57\u4f53\u5927\u5c0f */\n            width: 150px !important;    /* \u8c03\u6574\u6309\u94ae\u5bbd\u5ea6 */\n            height: 50px !important;    /* \u8c03\u6574\u6309\u94ae\u9ad8\u5ea6 */\n            background-color: gray !important; /* \u8bbe\u7f6e\u6309\u94ae\u80cc\u666f\u989c\u8272 */\n            color: white !important;    /* \u8bbe\u7f6e\u6309\u94ae\u6587\u672c\u989c\u8272 */\n}\n";
    document.head.appendChild(c);var aa=function(){};aa.prototype.radio=function(b){b=$jscomp.makeIterator(da(b));b.next();b.next();var a=b.next().value;if("none"!=a&&0!=a.querySelectorAll(".ulradiocheck").length){var f=a.querySelector(".judge_ques_right span").innerText;b=a.querySelectorAll(".ulradiocheck div");if("\ue6df"==b[0].querySelector("i").innerText||"\ue6e0"==b[0].querySelector("i").innerText){if("\u56de\u7b54\u6b63\u786e"==f){var d=-1;for(a=0;a<b.length;a++)if("\ue6df"==b[a].querySelector("i").innerText){d=
        a;break}}else for(f=a.querySelector(".answer-ansys div").innerText,d=-1,a=0;a<b.length;a++)if(b[a].querySelector("span").innerText==f){d=a;break}return[b[d].querySelector("span").innerText,"radio"]}if("\ue6e1"==b[0].querySelector("i").innerText||"\ue6e2"==b[0].querySelector("i").innerText){d="";if("\u56de\u7b54\u6b63\u786e"==f){for(a=0;a<b.length;a++)"\ue6e1"==b[a].querySelector("i").innerText&&(d=d+b[a].querySelector("span").innerText+"|");d=d.slice(0,d.length-1)}else d=a.querySelector(".answer-ansys div").innerText.replace("\u250b",
        "|");return[d,"checkbox"]}}else return["none","NOTRADIO"]};aa.prototype.input=function(b){b=$jscomp.makeIterator(da(b));b.next();b.next();b=b.next().value;if("none"!=b&&0!=b.querySelectorAll("div").length&&""!=b.querySelector("div").innerText&&0==b.querySelectorAll(".ulradiocheck").length){if(b.querySelector(".judge_ques_right span"))var a=b.querySelector(".judge_ques_right span").innerText;else return["none","NOTINPUT"];var f=b.querySelector("div").firstChild.nodeValue;return["\u56de\u7b54\u6b63\u786e"==
    a?f:b.querySelector(".answer-ansys div").innerText,"input"]}return["none","NOTINPUT"]};aa.prototype.manyinput=function(b){b=$jscomp.makeIterator(da(b,"manyinput"));b.next();b.next();b=b.next().value;if("none"==b)return["none","NOTMANYINPUT"];b=b.querySelector(".data__tit_cjd").innerText;return 0>b.indexOf("\u3010")&&0>b.indexOf("\u3011")?["none","NOTMANYINPUT"]:[b,"manyinput"]};var ca=function(){this.ans_ls_html=document.querySelectorAll(".field.ui-field-contain")};ca.prototype.radio=function(b,a){var f=
        this.ans_ls_html[a].querySelectorAll(".ui-radio");if(0!=f.length)if(b.ques_id==a&&"radio"==b.kind)for(var d=0;d<f.length;d++)0<=b.answer.replace(/\s*/g,"").indexOf(f[d].innerText.replace(/\s*/g,""))&&f[d].click();else console.log("radio:\u7b54\u6848\u5217\u8868\u4e2d\u7684id\u548c\u5f53\u524d\u83b7\u53d6id\u4e0d\u7b26\u5408\uff0c\u8df3\u8fc7\u586b\u5199")};ca.prototype.input=function(b,a){var f=this.ans_ls_html[a].querySelector(".ui-input-text input");f&&(b.ques_id==a?f.value=b.answer.split("|")[sa(0,
        b.answer.split("|").length-1)]:console.log("input:\u7b54\u6848\u5217\u8868\u4e2d\u7684id\u548c\u5f53\u524d\u83b7\u53d6id\u4e0d\u7b26\u5408\uff0c\u8df3\u8fc7\u586b\u5199"))};ca.prototype.checkbox=function(b,a){var f=this.ans_ls_html[a].querySelectorAll(".ui-checkbox");if(0!=f.length)if(b.ques_id==a&&"checkbox"==b.kind)for(var d=b.answer.split("|"),u=0;u<d.length;u++)for(var t=0;t<f.length;t++)0<=d[u].replace(/\s*/g,"").indexOf(f[t].querySelector(".label").innerText.replace(/\s*/g,""))&&f[t].click();
    else console.log("checkbox:\u7b54\u6848\u5217\u8868\u4e2d\u7684id\u548c\u5f53\u524d\u83b7\u53d6id\u4e0d\u7b26\u5408\uff0c\u8df3\u8fc7\u586b\u5199")};ca.prototype.manyinput=function(b,a,f){var d=$jscomp.makeIterator(ta(this.ans_ls_html[a],f)),u=d.next().value;d=d.next().value;if(b.ques_id==a&&"manyinput"==b.kind){a=b.answer.split(" \u9898\u76eeID\uff1a")[0];for(b=0;b<u.length-1;b++)a=a.replace(u[b],"");u=a.split("\u3011");for(b=0;b<u.length;b++)try{if(""!=u[b]){var t=u[b].split("\u3010")[0],x=u[b].split("\u3010")[1];
        a="";""==x?a=t:0<=x.indexOf("\u6b63\u786e\u7b54\u6848")&&(a=x.replace("\u6b63\u786e\u7b54\u6848: ",""));try{d.querySelectorAll(".ui-input-text")[b].value=a,".textCont"==f?d.querySelectorAll(".textCont")[b].innerText=a:d.querySelectorAll(".bracket")[b].querySelector("span .selection span span").innerText=a}catch(e){}}}catch(e){}}}}})();
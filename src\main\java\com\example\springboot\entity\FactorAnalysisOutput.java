package com.example.springboot.entity;

import lombok.Data;
import java.util.List;

@Data
public class FactorAnalysisOutput {
    private Double kmo;
    private Double barelett; // Bartlett test statistic
    private Double df;       // Bartlett test degrees of freedom
    private Double pValue;   // Bartlett test p-value
    private Integer factors; // Number of factors extracted

    private List<Double> tzgBefore;   // 特征根 (旋转前) Eigenvalues before rotation
    private List<Double> fcBefore;    // 方差解释率 (旋转前) <PERSON><PERSON><PERSON> explained before rotation
    private List<Double> ljfcBefore;  // 累积方差解释率 (旋转前) Cumulative variance explained before rotation

    private List<Double> tzgAfter;    // 特征根 (旋转后) Eigenvalues after rotation
    private List<Double> fcAfter;     // 方差解释率 (旋转后) Varian<PERSON> explained after rotation
    private List<Double> ljfcAfter;   // 累积方差解释率 (旋转后) Cumulative variance explained after rotation

    private List<FactorAnalysisItem> items; // List of factor loading items
    private List<TableData> tables; // 新增表格数据字段

    @Data
    public static class FactorAnalysisItem {
        private String title;          // 项目标题
        private List<Double> fators;   // 因子载荷 (各因子的载荷列表)
        private Double hValue;         // 共同度 (communalities)

        public void setHValue(double hValue) {
            this.hValue = hValue;
        }
    }

    @Data
    public static class TableData {
        private String type; // 表格类型，如"factor_analysis", "reliability"等
        private String title; // 表格标题
        private List<String> headers; // 第一层表头
        private List<List<Object>> rows; // 表格数据行
        private List<CellMerge> cellMerges; // 支持单元格合并
        private List<List<CellStyle>> cellStyles; // 每个单元格的样式
    }

    @Data
    public static class CellMerge {
        private int row;
        private int col;
        private int rowspan;
        private int colspan;
    }

    // 新增：单元格样式类
    @Data
    public static class CellStyle {
        private String fontWeight; // "bold"等
        private String color;      // 颜色代码，如"#d62728"
        // 可扩展更多样式
    }
} 
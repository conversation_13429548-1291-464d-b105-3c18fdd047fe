// 全局变量，存储当前的订单号
let currentOrderNumber = '';
let currentFensanLevel = null; // 新增，存储当前的分散等级
let orderStatusTimer = null; // 新增，定时器 ID
window.alert = function (message) {
    return Swal.fire({
        title: message,
        icon: 'info',
        confirmButtonText: 'OK'
    });
};

document.getElementById("orderNumberInput").addEventListener("input", (event) => {
    const value = event.target.value;

    // 只允许输入数字
    const filteredValue = value.replace(/\D/g, ""); // 移除非数字字符

    // 限制长度为 30 位
    const truncatedValue = filteredValue.slice(0, 30);

    // 更新输入框的值
    event.target.value = truncatedValue;
});

function getQueryParam(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}

function checkOrderStatus() {
    const orderNumber = document.getElementById('orderNumberInput').value.trim();
    if (!orderNumber) {
        alert('请输入订单号');
        return;
    }
    currentOrderNumber = orderNumber; // 更新全局订单号
    fetchOrderStatus(orderNumber, true); // 初次调用，显示加载旋转器
}


async function fetchOrderStatus(orderNumber, showSpinner = true) {
    if (showSpinner) {
        showLoadingSpinner(true);
    }


    try {
        const response = await fetch('order/queryOrder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ordernum: orderNumber})
        });
        const data = await response.json();
        if (data.success) {
            // 检查如果状态为 5 且完成比例为 100%，则将状态更新为 6
            if (data.orderStatus === 5 && data.realCompletionRate) {
                const [completed, target] = data.realCompletionRate.split('/').map(Number);
                if (completed >= target) {
                    data.orderStatus = 6;
                }
            }
            displayOrderInfo(data); // 无论 orderStatus 是否小于 0，都显示订单信息
            let exportButton = document.getElementById('exportButton');
            if (data.isNormal) {
                exportButton.style.display = 'inline-block';
            } else {
                exportButton.style.display = 'none';
            }

            updateProgressBar(data.orderStatus, data);

            // 清除现有定时器
            if (orderStatusTimer) {
                clearInterval(orderStatusTimer);
                orderStatusTimer = null;
            }

            // 根据订单状态启动或停止定时器
            if (data.orderStatus > 0 && data.orderStatus < 6) {
                // 如果定时器未启动，则启动定时器
                orderStatusTimer = setInterval(() => {
                    fetchOrderStatus(orderNumber, false); // 不显示加载旋转器
                }, 5000); // 每隔 5 秒更新一次
            } else if (data.orderStatus < 0) {
                orderStatusTimer = setInterval(() => {
                    fetchOrderStatus(orderNumber, false);
                }, 60000); // 60秒间隔
            }
        } else {
            // 请求接口并检查 rechargeAmount
            fetch(`/tokenvault/recharge-logs?orderNo=${encodeURIComponent(orderNumber)}`) // 替换为你的接口地址
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络请求失败');
                    }
                    return response.json();
                })
                .then(data => {
                    // 检查 rechargeAmount 是否为空
                    if (data.length === 0 || data[0].rechargeAmount == null) {
                        console.error('订单查询失败:', data);
                        alert('订单查询失败，请检查订单号是否正确然后刷新页面重试');
                    } else {
                        console.log('请求成功，跳转中...');
                        // 跳转到指定网址（例如 success.html）
                        window.location.href = '/queryRechargeOrder?ordernum=' + orderNumber; // 替换为目标网址
                    }
                })
                .catch(error => {
                    console.error('请求出错：', error);
                    alert('请求失败，请重试！');
                });

        }
    } catch (error) {
        console.error('查询订单状态时发生错误:', error);
        alert('查询订单状态时发生错误，请稍后重试。');
    } finally {
        showLoadingSpinner(false);
    }
}

window.onbeforeunload = function () {
    if (orderStatusTimer) {
        clearInterval(orderStatusTimer);
    }
};


function displayOrderInfo(data) {
    const targetCountElem = document.getElementById('targetCount');
    const surveyLinkElem = document.getElementById('surveyLink');
    const ipAreaElem = document.getElementById('ipArea');
    const sourceBiliElem = document.getElementById('sourceBili');
    const tianxieTimeElem = document.getElementById('tianxieTime');
    const orderTypeElem = document.getElementById('orderType');
    const createTimeElem = document.getElementById('createTime');
    const fensanLevelElem = document.getElementById('fensanLevel');
    const orderNumberElem = document.getElementById('orderNumber');

    // 订单编号
    const newOrderNumberText = `${data.orderNumber || '未知'}`;
    if (orderNumberElem.innerText !== newOrderNumberText) {
        orderNumberElem.innerText = newOrderNumberText;
        document.getElementById('orderNumberInput').value = data.orderNumber;
        currentOrderNumber = data.orderNumber;
    }

    // 目标份数
    if (targetCountElem.innerText !== String(data.targetCount)) {
        targetCountElem.innerText = data.targetCount;
    }

    // 问卷链接
    const newSurveyLinkHTML = `<a href="${data.surveyLink}" target="_blank">${data.surveyLink}</a>`;
    if (surveyLinkElem.innerHTML !== newSurveyLinkHTML) {
        surveyLinkElem.innerHTML = newSurveyLinkHTML;
    }

    // IP区域
    const newIpArea = data.ipArea || '不换IP';
    if (ipAreaElem.innerText !== newIpArea) {
        ipAreaElem.innerText = newIpArea;
    }

    // 创建时间
    const orderTime = data.createTime || '-';
    if (createTimeElem.innerText !== orderTime) {
        createTimeElem.innerText = orderTime;
    }

    // 来源比例
    const sourceRatios = data.sourceBili ? data.sourceBili.split(',') : [];
    const newSourceBiliText = `手机提交: ${sourceRatios[0] || 0}%, 链接提交: ${sourceRatios[1] || 0}%, 微信提交: ${sourceRatios[2] || 0}%`;
    if (sourceBiliElem.innerText !== newSourceBiliText) {
        sourceBiliElem.innerText = newSourceBiliText;
    }

    // 填写用时
    const timeRange = data.tianxieTime ? data.tianxieTime.split(',') : [];
    const newTianxieTimeText = timeRange.length === 2 ? `随机填写用时${timeRange[0]}~${timeRange[1]}秒` : '无时间';
    if (tianxieTimeElem.innerText !== newTianxieTimeText) {
        tianxieTimeElem.innerText = newTianxieTimeText;
    }

    // IP区域
    const orderType = data.orderType || '未知类别';
    const orderTypeText = {
        '1': '常规版',
        '2': '上传脚本版',
        '3': 'EXCEL回填版'
    }[orderType] || `未知类别(${orderType})`;

    if (orderTypeElem.innerText !== orderTypeText) {
        orderTypeElem.innerText = orderTypeText;
    }

    // 分散等级
    const spreadLevels = [
        '无',
        '1级 (每份问卷间隔2秒提交)',           // 新的1级
        '2级 (每份问卷随机分散5到10秒提交)',
        '3级 (每份问卷随机分散10到30秒提交)',
        '4级 (每份问卷随机分散30到60秒提交)',
        '5级 (每份问卷随机分散60到120秒提交)',
        '6级 (每份问卷随机分散120到300秒提交)',
        '7级 (每份问卷随机分散180到360秒提交)',  // 新的7级
        '8级 (每份问卷随机分散240到400秒提交)',   // 新的8级
        '11级 (每份问卷随机分散10到60秒提交)',
        '12级 (每份问卷随机分散10到120秒提交)',
        '13级 (每份问卷随机分散10到180秒提交)',
        '14级 (每份问卷随机分散10到240秒提交)',
        '15级 (每份问卷随机分散10到300秒提交)',
        '16级 (每份问卷随机分散10到360秒提交)',
        '17级 (每份问卷随机分散10到420秒提交)'
    ];
    const newFensanLevelText = spreadLevels[data.fensanLevel > 10 ? data.fensanLevel - 2 : data.fensanLevel] || '无分散等级';
    if (fensanLevelElem.innerText !== newFensanLevelText) {
        fensanLevelElem.innerText = newFensanLevelText;
    }

    currentFensanLevel = data.fensanLevel; // 更新当前分散等级
    document.getElementById('orderInfo').style.display = 'block';
}


function updateProgressBar(status, data) {
    const steps = ['step1', 'step2', 'step3', 'step4', 'step5', 'step6'];
    const totalSteps = steps.length - 1;
    let stepIndex = status - 1;
    const stepPercentages = [18, 34, 50, 61, 71, 90, 100];
    const progressLineActive = document.getElementById('progressLineActive');

    if (status === 6) {
        // 订单已完成，进度条填满
        progressLineActive.style.width = '100%';
    } else if (status === 5 && data.realCompletionRate) {
        // 根据完成比例计算进度条长度
        const [completed, target] = data.realCompletionRate.split('/').map(Number);
        const completionPercentage = completed / target;

        // 确保 completionPercentage 在 0 和 1 之间
        const adjustedCompletionPercentage = Math.min(Math.max(completionPercentage, 0), 1);

        // Get base and next step percentages
        let basePercentage = 76;
        let nextPercentage = 87;

        // Calculate progress percentage between current step and next step
        let progressPercentage = basePercentage + adjustedCompletionPercentage * (nextPercentage - basePercentage);

        // 确保在状态为 5 时，进度条不超过 99.99%
        if (progressPercentage >= 100) {
            progressPercentage = 99.99;
        }

        progressLineActive.style.width = `${progressPercentage}%`;
    } else if (status === 4 && data.completionRate) {
        // 状态为4时，使用后端返回的 completionRate 更新进度条
        let basePercentage = 61;
        let nextPercentage = 71;
        // 提取 completionRate 中的数值部分，并转换为 0-1 的比例
        let completionRateValue = parseFloat(data.completionRate.replace('%', '')) / 100;
        // 确保比例在 0 到 1 之间
        let adjustedCompletionPercentage = Math.min(Math.max(completionRateValue, 0), 1);
        // 计算进度条百分比
        let progressPercentage = basePercentage + adjustedCompletionPercentage * (nextPercentage - basePercentage);
        progressLineActive.style.width = `${progressPercentage}%`;
    } else {
        // 其他状态，按照步骤更新进度条
        progressLineActive.style.width = `${stepPercentages[stepIndex]}%`;
    }

    // 更新步骤的激活状态
    steps.forEach((step, index) => {
        const stepElement = document.getElementById(step);
        if (index <= stepIndex) {
            stepElement.classList.add('active');
        } else {
            stepElement.classList.remove('active');
        }
    });

    // 获取按钮元素
    const pauseButton = document.getElementById('pauseButton');
    const restartButton = document.getElementById('restartButton');
    const modifyFensanButton = document.getElementById('modifyFensanButton'); // 新增
    const refundButton = document.getElementById('refundButton');
    const refundButtonOnline = document.getElementById('refundButtonOnline');
    const cancelRefund = document.getElementById('cancelRefund');
    const exportExcelButton = document.getElementById('exportExcelButton');
    const previewButton = document.getElementById('previewButton');


    // 控制“修改分散等级”按钮的显示逻辑
    if (status > 0 && status !== 6) {
        modifyFensanButton.style.display = 'inline-block';
    } else {
        modifyFensanButton.style.display = 'none';
    }

    // 控制是否显示导出EXCEL按钮
    if (!data.isAvailExcel || (data.exportExcelCount===0 && status === -10) || status === -12) {
        exportExcelButton.style.display = 'none';
    } else {
        exportExcelButton.style.display = 'inline-block';
    }

    if (data.targetCount > 10 && data.orderType!==3) {
        if (data.completedCount < 10 || (data.exportExcelCount===0 && status === -10) || status === -12) {
            previewButton.style.display = 'none';
        } else {
            previewButton.style.display = 'inline-block';
        }
    }else {
        if (!data.isAvailExcel || (data.exportExcelCount===0 && status === -10) || status === -12) {
            previewButton.style.display = 'none';
        } else {
            previewButton.style.display = 'inline-block';
        }
    }




    // 控制按钮显示逻辑
    if (status === 5) {
        // 订单状态为5时显示暂停按钮，隐藏重启按钮
        pauseButton.style.display = 'inline-block';
        restartButton.style.display = 'none';
    } else if (status === -9 || status === -4 || status === -7 || status === -2 || status === -41 || status === -43 || status === -44) {
        // 显示重启按钮，隐藏暂停按钮
        pauseButton.style.display = 'none';
        restartButton.style.display = 'inline-block';
    } else {
        // 其他状态下隐藏两个按钮
        pauseButton.style.display = 'none';
        restartButton.style.display = 'none';
    }
    if (status === 6 || status === -10 || status === -11 || status === -12 || data.payType === 1) {
        refundButton.style.display = 'none';
    } else {
        refundButton.style.display = 'inline-block';
    }

    if (status === 6 || status === -10 || status === -11 || status === -12 || data.payType === 2) {
        refundButtonOnline.style.display = 'none';
    } else {
        refundButtonOnline.style.display = 'inline-block';
    }

    if (status === -12 && data.payType === 1) {
        cancelRefund.style.display = 'inline-block';
    } else {
        cancelRefund.style.display = 'none';
    }

    // 新增：处理负数状态码的情况
    if (status < 0) {
        if (status === -9) {
            const infoElement = document.getElementById('additionalInfo');
            infoElement.querySelector('.alert').innerHTML = `任务已暂停<br>订单进度: ${data.realCompletionRate}`;
            infoElement.style.display = 'block'; // 显示提示信息
        } else {
            const infoElement = document.getElementById('additionalInfo');
            let errorMessage = '';
            switch (status) {
                case -1:
                    errorMessage = '问卷异常，请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -2:
                    errorMessage = '生成答案失败，请打开问卷链接检查，可能是问卷没有发布(可以发布问卷后点击上方重启任务按钮解决)，也有可能是企业标准版已到期(可以申请降级后点击上方重启任务按钮解决)，其他情况请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -3:
                    errorMessage = '无法找到可用的提交数据，请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -4:
                    errorMessage = '订单失败份数过高，请打开你的问卷链接做如下检查，1、检查是否发布问卷（解决方法：没有发布的话，发布后点击上方重启任务按钮）；2、检查是否是企业版试用问卷到期（解决方法：关闭企业版试用，发布后点击上方重启任务按钮）。如果都不是,请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -10:
                    errorMessage = '订单已退款';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -11:
                    errorMessage = '订单退款失败，如有疑问请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -12:
                    errorMessage = '订单正在等待处理退款，24小时内会将订单未完成的份数按比例原路退款，在等待处理期间可自助取消申请退款。如果超过24小时未收到退款，请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -41:
                    errorMessage = '问卷要求强制微信登录,平台目前不支持，可在问卷星后台取消微信登录限制，【必看教程】中<a href="https://www.yuque.com/yangyang-bnict/axszk1/sbrsk1lgn4eodeow#vE745" target="_blank" style="color: #1890ff; text-decoration: underline;">【常见问题 Q&A】</a>章节有教程，然后点击上方重启任务按钮解决，其他问题请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    errorMessage = '问卷要求强制微信登录,平台目前不支持，可在问卷星后台取消微信登录限制，【必看教程】中【常见问题 Q&A】章节有教程，然后点击上方重启任务按钮解决，其他问题请联系管理员，加微信：zyy835573228或者qq:751947907';
                    break;
                case -42:
                    errorMessage = '答案不符合要求,请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -43:
                    errorMessage = '问卷刚发布，还不能满足填写时长提交，请过大于你最长填写用时的时间，再点击上方重启任务按钮（比如你设置的填写时间是120~240秒，那么就请过240秒后再来重启任务），系统会每分钟自动去尝试重启，其他问题请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    break;
                case -44:
                    errorMessage = '企业版试用到期（解决方法：申请降级关闭企业版试用，【必看教程】中<a href="https://www.yuque.com/yangyang-bnict/axszk1/sbrsk1lgn4eodeow#vE745" target="_blank" style="color: #1890ff; text-decoration: underline;">【常见问题 Q&A】</a>章节有教程，然后点击上方重启任务按钮），其他问题请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    infoElement.style.display = 'block'; // 显示提示信息
                    errorMessage = '企业版试用到期（解决方法：申请降级关闭企业版试用，【必看教程】中【常见问题 Q&A】章节有教程，然后点击上方重启任务按钮），其他问题请联系管理员，加微信：zyy835573228或者qq:751947907';
                    break;
                case -5:
                    errorMessage = '订单付款失败，请核实订单付款状态，请联系管理员，加微信：zyy835573228或者qq:751947907';
                    break;
                case -7:
                    errorMessage = '连续获取代理失败，可点击上方重启任务按钮，如果一直重启还不行，可能是IP代理商在维护,可以过一会尝试，如有疑问请联系管理员，加微信：zyy835573228或者qq:751947907';
                    infoElement.querySelector('.alert').innerHTML = `${errorMessage}<br><br>订单进度: ${data.realCompletionRate}`;
                    break;
                default:
                    errorMessage = '未知错误，请联系管理员，加微信：zyy835573228或者qq:751947907';
                    break;
            }

            if (status !== -10) {
                infoElement.querySelector('.alert').innerHTML += (data.messageForUser ? '<br><br>管理员提示的信息：' + data.messageForUser : '');
            }

            Swal.fire({
                html: errorMessage + (data.messageForUser && status !== -10 ? '<br><br>管理员提示的信息：' + data.messageForUser : ''),
                icon: 'warning' // 图标类型：success/error/warning/info/question
            })
        }
        deactivateProgressBar();
        return;  // 终止函数，避免继续执行后续代码
    } else {
        Swal.close()
    }

    // 其他正常状态处理逻辑（如 status >= 0）
    // 在正常状态下，隐藏提示信息
    const infoElement = document.getElementById('additionalInfo');
    infoElement.style.display = 'none';

    // 修改显示提示信息的逻辑
    if (status === 2 && data.estimatedWaitTime) {
        const infoElement = document.getElementById('additionalInfo');
        infoElement.querySelector('.alert').innerText = `还需等待约 ${data.estimatedWaitTime}，请稍等一会...`;
        infoElement.style.display = 'block';
    } else if (status === 3) {
        const infoElement = document.getElementById('additionalInfo');
        infoElement.querySelector('.alert').innerText = `正在检查比例数据，请稍等一会...`;
        infoElement.style.display = 'block';
    } else if (status === 4 && data.completionRate) {
        const infoElement = document.getElementById('additionalInfo');
        infoElement.querySelector('.alert').innerText = `生成答案中: ${Math.min(parseFloat(data.completionRate), 100)}%，请稍等一会...`;
        infoElement.style.display = 'block';
    } else if (status === 5 && data.realCompletionRate) {
        const infoElement = document.getElementById('additionalInfo');
        const [completed, target] = data.realCompletionRate.split('/').map(Number);
        const remaining = target - completed;

        const spreadLevelTimes = {
            1: [2, 2],
            2: [5, 10],
            3: [10, 30],
            4: [30, 60],
            5: [60, 120],
            6: [120, 300],
            7: [180, 360],
            8: [240, 400],
            11: [10, 60],
            12: [10, 120],
            13: [10, 180],
            14: [10, 240],
            15: [10, 300],
            16: [10, 360],
            17: [10, 420]
        };

        const spreadLevel = data.fensanLevel || 1;
        const averageTime = (spreadLevelTimes[spreadLevel][0] + spreadLevelTimes[spreadLevel][1]) / 2;
        const totalSeconds = remaining * averageTime;

        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.ceil((totalSeconds % 3600) / 60);
        // 提示信息数组
        const tips = [
            '1.完成太慢或者太快？<br>点击上方修改提交分散等级按钮!',
            '2.遇到订单卡住不动？<br>尝试暂停任务再重启！',
            '3.点击上方导出配置信息按钮<br>粘贴在快速导入框可导入配置！',
            '4.本页面可以关闭<br>可通过订单号再次查询！',
            '5.配置信息设置错误？<br>可自助申请取消订单，再重新下单！'
        ];

        const interval = 5000; // 每5秒切换一次
        const getCurrentTip = () => {
            const currentTime = Date.now(); // 获取当前时间戳
            const index = Math.floor(currentTime / interval) % tips.length; // 计算当前索引
            return tips[index];
        };
        // 随机选择一个提示信息
        const randomTip = getCurrentTip();
        // const randomTip = tips[Math.floor(Math.random() * tips.length)];

        infoElement.querySelector('.alert').innerHTML = `问卷提交中: ${data.realCompletionRate}<br>预计剩余时间：${hours > 0 ? hours + '小时' : ''}${minutes}分钟<br>
    <span style="color: indianred; font-size: 0.85em;">
        小技巧: ${randomTip}
    </span>`;
        infoElement.style.display = 'block';
    } else if (status === 6) {
        const infoElement = document.getElementById('additionalInfo');
        infoElement.querySelector('.alert').innerText = '订单已完成';
        infoElement.style.display = 'block';
    } else {
        // 如果没有提示信息需要显示，隐藏提示信息
        const infoElement = document.getElementById('additionalInfo');
        infoElement.style.display = 'none';
    }
}


function showLoadingSpinner(isVisible) {
    const spinner = document.getElementById('loadingSpinner');
    if (isVisible) {
        spinner.style.display = 'block'; // 显示加载组件
    } else {
        spinner.style.display = 'none'; // 隐藏加载组件
    }
}


window.onload = function () {
    const orderNumber = getQueryParam('ordernum');
    if (orderNumber) {
        document.getElementById('orderNumberInput').value = orderNumber;
        currentOrderNumber = orderNumber; // 更新全局订单号
        fetchOrderStatus(orderNumber, true); // 显示加载旋转器
    }
};
document.getElementById('pauseButton').addEventListener('click', function () {
    if (!currentOrderNumber) {
        alert('请先查询订单后再执行此操作');
        return;
    }

    // 弹出确认提示框
    const userConfirmed = confirm('确定要暂停该任务吗？');
    if (!userConfirmed) {
        return; // 如果用户取消暂停操作，则终止函数
    }

    // 发送请求到后端，将订单状态设置为 -9
    fetch(`order/pause/${currentOrderNumber}`, {
        method: 'POST'
    })
        .then(response => response.text())
        .then(message => {
            alert(message);
            // 重新查询订单状态，更新页面
            fetchOrderStatus(currentOrderNumber);
        })
        .catch(error => {
            console.error('暂停任务时发生错误:', error);
            alert('暂停任务时发生错误，请稍后重试。');
        });
});


document.getElementById('refundButton').addEventListener('click', function () {
    if (!currentOrderNumber) {
        alert('请先查询订单后再执行此操作');
        return;
    }

    // 获取代币码（假设从某个输入框或全局变量中获取）
    const code = prompt('请输入代币码以确认退款操作（按完成比例退还相应数量代币,如果该订单导出过Excel答案，最多退款总金额的80%）');
    if (code === null) {
        // 用户点击了取消，直接返回，不弹提示
        return;
    }
    if (!code) {
        alert('代币码不能为空');
        return;
    }

    // 构造请求体
    const requestBody = {
        orderNumber: currentOrderNumber
    };

    // 发送退款请求
    fetch('order/refundOrderPartialForUser?code=' + encodeURIComponent(code), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应异常');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert(data.message); // 显示成功消息
                fetchOrderStatus(currentOrderNumber, false)
                // 可选：刷新页面或更新页面状态
            } else {
                alert(data.message); // 显示错误消息
            }
        })
        .catch(error => {
            console.error('退款操作失败:', error);
            alert('退款操作失败，请检查网络或联系管理员');
        });

});

document.getElementById('exportExcelButton').addEventListener('click', function() {
    if (!currentOrderNumber) {
        alert('请先查询订单后再执行此操作');
        return;
    }

    if (!confirm('需要确保问卷在正常发布状态（打开问卷链接可以正常填写的状态），并且下单后没有修改过问卷，请先预览前10条答案是否正常，确保正常再进行下一步操作，请确认是否进行下一步操作？')) {
        return;
    }

    const code = prompt('请输入"确认导出"4个字来确认导出Excel答案。（如果需要导出Excel答案至少要付订单总价格的20%作为导出费用，也可以理解为订单完成超过20%就可以免费导出Excel答案）');
    if (code === null) {
        // 用户点击了取消，直接返回，不弹提示
        return;
    }
    if ("确认导出" !== code) {
        alert('输入错误，取消导出')
        return;
    }

    fetch(`generate-excel?orderNumber=${encodeURIComponent(currentOrderNumber)}`)
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            const responseClone = response.clone();
            return response.json().then(data => ({
                response: responseClone,
                data: data
            }));
        })
        .then(({response, data}) => {
            if (data.code === "200") {
                // Base64转Blob
                const byteCharacters = atob(data.data);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});

                // 获取文件名
                const filename = decodeURIComponent(response.headers.get('X-Filename') || 'survey_report.xlsx');

                // 触发下载
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);

                alert(data.msg);
                // fetchOrderStatus(currentOrderNumber, false);
            } else {
                alert(data.msg);
            }
        })
        .catch(error => {
            console.error('导出失败:', error);
            alert(error.msg || '导出失败: ' + error.message);
        });
});

document.getElementById('refundButtonOnline').addEventListener('click', function () {
    if (!currentOrderNumber) {
        alert('请先查询订单后再执行此操作');
        return;
    }
    const isConfirmed = confirm("您确定申请退款订单未完成份数吗？（24小时内会将订单未完成的份数按比例原路退款，在等待处理期间可自助取消申请退款，如果该订单导出过Excel答案，最多退款总金额的80%）");
    if (!isConfirmed) {
        return;
    }
    // 获取代币码（假设从某个输入框或全局变量中获取）
    const code = prompt('请输入微信/支付宝付款记录中的订单号（28位数字的）以确认退款操作：');
    if (code === null) {
        // 用户点击了取消，直接返回，不弹提示
        return;
    }
    if (!code) {
        alert('订单号不能为空');
        return;
    } else if (code.length !== 28) {
        alert('订单号长度不对，订单号是微信/支付宝付款记录中长度为28位数字的的订单号');
        return;
    }

    // 构造请求体
    const requestBody = {
        orderNumber: currentOrderNumber
    };

    // 发送退款请求
    fetch('order/applayRefundForUserOnline?code=' + encodeURIComponent(code), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应异常');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert(data.message); // 显示成功消息
                fetchOrderStatus(currentOrderNumber, false)
                // 可选：刷新页面或更新页面状态
            } else {
                alert(data.message); // 显示错误消息
            }
        })
        .catch(error => {
            console.error('申请退款操作失败:', error);
            alert('申请退款操作失败，请检查网络或联系管理员');
        });

});

document.getElementById('cancelRefund').addEventListener('click', function () {
    if (!currentOrderNumber) {
        alert('请先查询订单后再执行此操作');
        return;
    }
    const isConfirmed = confirm("您确定取消申请退款吗？");
    if (!isConfirmed) {
        return;
    }
    // 构造请求体
    const requestBody = {
        orderNumber: currentOrderNumber
    };

    // 发送退款请求
    fetch('order/cancelRefund', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应异常');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert(data.message); // 显示成功消息
                fetchOrderStatus(currentOrderNumber, false)
                // 可选：刷新页面或更新页面状态
            } else {
                alert(data.message); // 显示错误消息
            }
        })
        .catch(error => {
            console.error('申请退款操作失败:', error);
            alert('申请退款操作失败，请检查网络或联系管理员');
        });

});


document.getElementById('restartButton').addEventListener('click', function () {
    if (!currentOrderNumber) {
        alert('请先查询订单后再执行此操作');
        return;
    }

    // 发送请求到后端，清除任务
    fetch(`tasks/clear/${currentOrderNumber}`, { // 更新接口路径
        method: 'POST'
    })
        .then(response => {
            if (response.ok) {
                // alert('任务已成功重启');
                // 重新查询订单状态，更新页面
                fetchOrderStatus(currentOrderNumber);
            } else {
                alert('重启任务失败，订单已被锁定，无法重启，请联系管理员，加微信：zyy835573228或者qq:751947907');
            }
        })
        .catch(error => {
            console.error('重启任务时发生错误:', error);
            alert('重启任务时发生错误，请稍后重试。');
        });
});
document.getElementById('modifyFensanButton').addEventListener('click', function () {
    if (!currentOrderNumber) {
        alert('请先查询订单后再执行此操作');
        return;
    }

    // 打开模态框
    var modifyFensanModal = new bootstrap.Modal(document.getElementById('modifyFensanModal'));
    modifyFensanModal.show();

    // 在打开模态框时，清除之前的选择
    const radios = document.querySelectorAll('input[name="fensanLevel"]');
    radios.forEach(radio => {
        radio.checked = false;
    });

    // 默认选中当前的分散等级
    if (currentFensanLevel) {
        const radioId = 'level' + currentFensanLevel;
        const radio = document.getElementById(radioId);
        if (radio) {
            radio.checked = true;
        }
    }
});

document.getElementById('confirmFensanLevel').addEventListener('click', function () {
    const selectedLevel = document.querySelector('input[name="fensanLevel"]:checked');

    if (!selectedLevel) {
        alert('请选择一个分散等级');
        return;
    }

    const fensanLevel = parseInt(selectedLevel.value);

    // 比较新选择的分散等级和当前的分散等级
    if (fensanLevel === currentFensanLevel) {
        // 如果分散等级没有变化，直接关闭模态框，不发送请求
        var modifyFensanModal = bootstrap.Modal.getInstance(document.getElementById('modifyFensanModal'));
        modifyFensanModal.hide();
        return;
    }

    // 分散等级有变化，发送请求更新
    fetch(`order/updateFensanLevel/${currentOrderNumber}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({fensanLevel: fensanLevel})
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('提交分散等级已更新成功,请重启任务继续填问卷');
                // 更新当前的分散等级
                currentFensanLevel = fensanLevel;
                // 关闭模态框
                var modifyFensanModal = bootstrap.Modal.getInstance(document.getElementById('modifyFensanModal'));
                modifyFensanModal.hide();
                // 更新页面信息
                fetchOrderStatus(currentOrderNumber);
            } else {
                alert('更新分散等级失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('更新分散等级时发生错误:', error);
            alert('更新分散等级时发生错误，请稍后重试。');
        });
});

function deactivateProgressBar() {
    const steps = ['step1', 'step2', 'step3', 'step4', 'step5', 'step6'];
    const progressLineActive = document.getElementById('progressLineActive');
    progressLineActive.style.width = '0%'; // 将进度条重置

    steps.forEach(step => {
        const stepElement = document.getElementById(step);
        stepElement.classList.remove('active');
    });
}

// 为导出按钮添加点击事件
document.getElementById('exportButton').addEventListener('click', function () {
    // 调用后端接口获取数据
    fetch(`order/getOrderBiliData?orderNumber=${currentOrderNumber}`)
        .then(response => response.json())  // 解析返回的JSON
        .then(data => {
            if (data.success) {
                // 假设返回的比例数据是 JSON 格式
                var biliData = data.biliData;

                // 创建一个隐藏的 textarea 元素
                var textarea = document.createElement('textarea');
                textarea.value = biliData;  // 将比例数据填入 textarea
                document.body.appendChild(textarea);  // 将文本框添加到页面中

                // 选择文本框中的内容
                textarea.select();
                textarea.setSelectionRange(0, 99999);  // 针对移动设备

                try {
                    // 执行复制操作
                    document.execCommand('copy');
                    alert("配置信息已成功复制到剪切板！（可在快速导入工具框中粘贴快速导入配置信息，【必看教程】【常见问题 Q&A】章节中有截图说明）");
                } catch (err) {
                    alert("复制数据到剪切板时发生错误：" + err);
                }

                // 删除 textarea 元素
                document.body.removeChild(textarea);
            } else {
                alert(data.message + '(仅支持2024-11-15以后的订单)');
            }
        })
        .catch(error => {
            alert("调用接口时发生错误：" + error);
        });
});
// 预览按钮点击事件
document.getElementById('previewButton').addEventListener('click', function() {
    if (!currentOrderNumber) {
        alert('请先查询订单');
        return;
    }

    // 显示加载状态（原生JS写法）
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
    document.getElementById('previewBody').innerHTML = `
        <tr>
            <td colspan="20" class="text-center">
                <div class="spinner-border"></div>
            </td>
        </tr>
    `;

    fetch(`preview-data?orderNumber=${encodeURIComponent(currentOrderNumber)}`)
        .then(response => response.json())
        .then(result => {
            const previewBody = document.getElementById('previewBody');
            const previewHeaders = document.getElementById('previewHeaders');

            if (result.code === "200") {
                const data = result.data;

                // 生成表头
                previewHeaders.innerHTML = `
                <tr>
                    ${data.headers.map(h => `<th>${h}</th>`).join('')}
                </tr>
            `;

                // 生成数据行
                previewBody.innerHTML = Array.from(data.rows).map(row => `
                <tr>
                    ${row.map(cell => `<td>${cell || ''}</td>`).join('')}
                </tr>
            `).join('');
            } else {
                previewBody.innerHTML = `
                <tr>
                    <td colspan="20" class="text-danger">${result.msg}</td>
                </tr>
            `;
            }
        })
        .catch(error => {
            console.error('预览失败:', error);
            document.getElementById('previewBody').innerHTML = `
            <tr>
                <td colspan="20" class="text-danger">数据加载失败</td>
            </tr>
        `;
        });
});

window.onload = function () {
    try {
        document.querySelector('#loadbox').remove();
        document.querySelector('#divS').remove();
        initContentShow();
    } catch (e) {
        console.log("清除加载失败");
    }
    try {
        setDefaultTimeRange()
    } catch (e) {
        console.log("初始化填写用时失败");
    }
    // console.log(window.location.href);

    try {
        generateJS()
        if (window.js_txt === '' || window.js_txt.length < 100) {
            alert("初始化失败，请重新刷新页面填写信息");
            console.log("初始化失败，请重新刷新页面填写信息");
        } else {
            console.log("初始化成功！");
        }
    } catch (e) {
        alert("初始化失败，请重新刷新页面填写信息");
        console.log("初始化失败，请重新刷新页面填写信息");
        location.reload();
    } finally {
        window.js_txt = '';
    }

};
// 重试计数器的最大尝试次数
const maxRetryAttempts = 3;
let retryCount = 0;

function initContentShow() {
    var i = $("#classicsCoverContent .coverTip .viewResultWrap")
        , n = $("#classicsCoverContent .coverTip").text();
    if (i[0])
        window.location.href = i.find("a").attr("href");
    else if (window.coverTimeFinish)
        sessionStorage.skipfm = 1,
            window.location.href = window.location.href.replace(/#/gi, "");
    else if (n)
        return void layer.msg(n);
    divTip && ("none" != divTip.style.display || divTip.needShow) && (divTip.style.display = "",
        $("#tipHeight").show()),
        $("#divContent").show(),
        $("#pageDiv").show(),
        !isMobile ? ($("#divFengMian").hide(),
            $("html").scrollTop(0),
            $("#divPowerBy").show(),
            fminitshow(),
            fixBottom()) : ($("#coverShare").hide(),
            $("#slideChunk").hide(),
            $(divFengMian).animate({
                top: "-1000px"
            }, 300, "swing", function () {
                $("#divFengMian").hide(),
                    $("html").scrollTop(0),
                    $("#divPowerBy").show(),
                    fminitshow(),
                    fixBottom()
            }))
}

function updateRatio() {
    let mobileRatio = parseInt(document.getElementById('mobile_ratio').value);
    let linkRatio = parseInt(document.getElementById('link_ratio').value);
    let wechatRatio = 100 - mobileRatio - linkRatio;

    if (wechatRatio < 0) {
        document.getElementById('ratio_error').style.display = 'block';
    } else {
        document.getElementById('wechat_ratio').value = wechatRatio;
        document.getElementById('ratio_error').style.display = 'none';
    }

    document.getElementById('mobile_ratio_display').value = mobileRatio;
    document.getElementById('link_ratio_display').value = linkRatio;
    document.getElementById('wechat_ratio_display').value = wechatRatio;
}

function goBack() {
    window.location.href = 'createOrderStep1'; // 跳转到指定页面
}

function setDefaultTimeRange() {
    // 获取页面上所有符合条件的元素数量
    var elementCount = document.querySelectorAll('.field.ui-field-contain').length;

    // 计算默认的填写用时范围
    var defaultStartTime = elementCount * 5;
    var defaultEndTime = elementCount * 5 * 2;

    // 设置第一个输入框的默认值
    document.getElementById('fill_time_start').value = defaultStartTime;

    // 设置第二个输入框的默认值
    document.getElementById('fill_time_end').value = defaultEndTime;
}

// 当结束时间输入框的值发生变化时进行验证
document.getElementById('fill_time_end').addEventListener('input', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;

    // 检查是否开始时间小于结束时间
    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        document.getElementById('error-message').style.display = 'block';
    } else {
        document.getElementById('error-message').style.display = 'none';
    }
});

// 当结束时间输入框失去焦点时，如果不合法则清空输入框
document.getElementById('fill_time_end').addEventListener('blur', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;

    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        // 清空非法的结束时间输入框
        document.getElementById('fill_time_end').value = '';
    }
});

// 当开始时间输入框失去焦点时，如果不合法则清空输入框
document.getElementById('fill_time_start').addEventListener('blur', function () {
    var startTime = document.getElementById('fill_time_start').value;
    var endTime = document.getElementById('fill_time_end').value;

    if (startTime && endTime && parseInt(startTime) >= parseInt(endTime)) {
        // 清空非法的开始时间输入框
        document.getElementById('fill_time_start').value = '';
    }
});


let loadedMappingData = {}; // 全局变量存储加载的数据
let cityToProvinceMap = {}; // 存储城市到省份的映射
// 声明全局布尔变量
let isCitySearchTriggered = false;
document.addEventListener('DOMContentLoaded', function () {
    // 获取当天日期标识（格式：YYYY-MM-DD）
    const getDayIdentifier = () => {
        const today = new Date();
        return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    };

    // 检查是否需要显示协议
    const shouldShowTerms = () => {
        const lastAgreedDayForNormal = localStorage.getItem('lastAgreedDayForNormal');
        const currentDay = getDayIdentifier();
        return !lastAgreedDayForNormal || lastAgreedDayForNormal !== currentDay;
    };

    if (shouldShowTerms()) {
        const termsModal = new bootstrap.Modal('#termsModal', {
            backdrop: 'static',
            keyboard: false
        });

        const agreeCheck = document.getElementById('agreeCheck');
        const confirmBtn = document.getElementById('confirmBtn');

        agreeCheck.addEventListener('change', function () {
            confirmBtn.disabled = !this.checked;
        });

        confirmBtn.addEventListener('click', function () {
            // 存储当天日期标识
            localStorage.setItem('lastAgreedDayForNormal', getDayIdentifier());
            termsModal.hide();
            document.querySelector('.modal-backdrop').remove();
            document.body.style.overflow = 'auto';
        });

        termsModal.show();
    } else {
        // 确保移除可能残留的遮罩层
        const existingBackdrop = document.querySelector('.modal-backdrop');
        if (existingBackdrop) {
            existingBackdrop.remove();
            document.body.style.overflow = 'auto';
        }
    }

    // 动态加载 province_city_mapping.json 文件
    fetch('province_city_mapping.json')  // 确保使用正确的文件路径
        .then(response => response.json())
        .then(data => {
            loadedMappingData = data; // 将加载的数据存储在全局变量中
            createCityToProvinceMap(data); // 创建城市到省份的映射
            initializeProvinceCityMapping(loadedMappingData);  // 使用加载的数据初始化下拉菜单
        })
        .catch(error => {
            console.error('加载省市映射数据时出错:', error);
        });

    // 添加搜索输入框的事件监听器
    document.getElementById('province_search').addEventListener('input', function () {
        isCitySearchTriggered = false;
        filterDropdown('province', this.value, 'province_list_backup');
    });

    document.getElementById('city_search').addEventListener('input', function () {
        // 用户开始通过搜索框搜索城市时，把标志位设为 true
        isCitySearchTriggered = true;
        filterCityDropdown(this.value);
    });

    // 添加清空搜索按钮的事件监听器
    document.getElementById('clear_province_search').addEventListener('click', function () {
        isCitySearchTriggered = false;
        document.getElementById('province_search').value = '';
        filterDropdown('province', '', 'province_list_backup');
    });

    document.getElementById('clear_city_search').addEventListener('click', function () {
        isCitySearchTriggered = false;
        document.getElementById('city_search').value = '';
        populateCityDropdown(); // 恢复城市下拉框的所有选项
    });

    // 监听回车键触发搜索
    document.getElementById('province_search').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            filterDropdown('province', this.value, 'province_list_backup');
        }
    });

    document.getElementById('city_search').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            filterCityDropdown(this.value);
        }
    });


});

// 创建城市到省份的映射
function createCityToProvinceMap(mappingData) {
    cityToProvinceMap = {}; // 初始化
    for (const province in mappingData) {
        const cities = mappingData[province].cities;
        for (const city in cities) {
            cityToProvinceMap[city] = province;
        }
    }
}

// 初始化省份和城市下拉菜单
function initializeProvinceCityMapping(mappingData) {
    const provinceSelect = document.getElementById('province');

    // 清空现有选项
    provinceSelect.innerHTML = '';

    // 更新省份和城市映射关系
    for (const province in mappingData) {
        const option = document.createElement('option');
        option.value = province;
        option.textContent = province;
        provinceSelect.appendChild(option);
    }

    // 备份完整的省份列表用于搜索
    provinceSelect.dataset.fullOptions = JSON.stringify(
        Array.from(provinceSelect.options).map(option => ({value: option.value, text: option.text}))
    );

    // 在初始化时调用 updateCityDropdown，并传入加载的数据
    populateCityDropdown();  // 初始化城市下拉框

    // 省份选择框的事件监听器，改变省份时更新城市列表
    provinceSelect.addEventListener('change', function () {
        updateCityDropdown(); // 使用已加载的数据更新城市下拉框
    });
}

// 更新城市下拉框，根据选中的省份或搜索结果
function updateCityDropdown() {
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    const selectedProvince = provinceSelect.value;

    isCitySearchTriggered = false;

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 使用选中的省份更新城市下拉列表
    if (selectedProvince && loadedMappingData[selectedProvince]) {
        const cities = loadedMappingData[selectedProvince].cities;

        // 遍历城市对象，创建下拉选项
        for (const city in cities) {
            const option = document.createElement('option');
            option.value = cities[city]; // 使用城市的代码作为值
            option.textContent = city; // 显示城市名称
            citySelect.appendChild(option);
        }

        // 备份完整的城市列表用于搜索
        citySelect.dataset.fullOptions = JSON.stringify(
            Array.from(citySelect.options).map(option => ({value: option.value, text: option.text}))
        );

        // 自动选择第一个城市
        if (Object.keys(cities).length > 0) {
            citySelect.value = Object.values(cities)[0]; // 选择第一个城市
        }
    }
}

// 恢复城市下拉框的所有选项
function populateCityDropdown() {
    const citySelect = document.getElementById('city');
    const provinceSelect = document.getElementById('province');
    const selectedProvince = provinceSelect.value;

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 使用选中的省份更新城市下拉列表
    if (selectedProvince && loadedMappingData[selectedProvince]) {
        const cities = loadedMappingData[selectedProvince].cities;

        // 遍历城市对象，创建下拉选项
        for (const city in cities) {
            const option = document.createElement('option');
            option.value = cities[city]; // 使用城市的代码作为值
            option.textContent = city; // 显示城市名称
            citySelect.appendChild(option);
        }

        // 备份完整的城市列表用于搜索
        citySelect.dataset.fullOptions = JSON.stringify(
            Array.from(citySelect.options).map(option => ({value: option.value, text: option.text}))
        );

        // 自动选择第一个城市
        if (Object.keys(cities).length > 0) {
            citySelect.value = Object.values(cities)[0]; // 选择第一个城市
        }
    }
}

// 过滤省份下拉选项的通用函数
function filterDropdown(selectId, searchTerm, backupKey) {
    const selectElement = document.getElementById(selectId);
    const fullOptions = JSON.parse(selectElement.dataset.fullOptions || '[]');

    // 清空当前选项
    selectElement.innerHTML = '';

    // 过滤选项
    const filteredOptions = fullOptions.filter(option =>
        option.text.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // 添加过滤后的选项
    filteredOptions.forEach(optionData => {
        const option = document.createElement('option');
        option.value = optionData.value;
        option.textContent = optionData.text;
        selectElement.appendChild(option);
    });

    // 如果是省份下拉框，更新城市下拉框
    if (selectId === 'province') {
        updateCityDropdown();
    }
}

// 过滤城市下拉选项的函数，支持全局搜索
function filterCityDropdown(searchTerm) {
    const citySelect = document.getElementById('city');
    const provinceSelect = document.getElementById('province');
    const citySearch = searchTerm.toLowerCase();

    // 清空城市下拉列表
    citySelect.innerHTML = '';

    // 遍历所有城市，找到匹配的
    for (const province in loadedMappingData) {
        const cities = loadedMappingData[province].cities;
        for (const city in cities) {
            if (city.toLowerCase().includes(citySearch)) {
                const option = document.createElement('option');
                option.value = cities[city]; // 使用城市的代码作为值
                option.textContent = city; // 显示城市名称
                option.dataset.province = province; // 存储所属省份
                citySelect.appendChild(option);
            }
        }
    }

    // 如果有匹配的城市，自动选择第一个
    if (citySelect.options.length > 0) {
        citySelect.value = citySelect.options[0].value;
    }

    // ==== 在这里添加一段监听器逻辑 ====
    citySelect.onchange = function () {

        // 如果不是由“城市搜索”触发，就什么也不做
        if (!isCitySearchTriggered) {
            return;
        }

        if (this.selectedIndex >= 0) {
            // 1. 获取当前选中的城市选项
            const selectedOption = this.options[this.selectedIndex];
            // 2. 读取城市所属的省份
            const selectedProvince = selectedOption.dataset.province;

            // --- 关键操作：恢复省份下拉框为完整列表 ---
            const provinceSelect = document.getElementById('province');
            const fullOptions = JSON.parse(provinceSelect.dataset.fullOptions || '[]');

            // 先清空
            provinceSelect.innerHTML = '';
            // 再把完整列表添加回去
            fullOptions.forEach(optionData => {
                const option = document.createElement('option');
                option.value = optionData.value;
                option.textContent = optionData.text;
                provinceSelect.appendChild(option);
            });

            // 3. 设置当前城市对应的省份
            provinceSelect.value = selectedProvince;
        }
    };
}

// 切换城市选择功能
function toggleCitySelection() {
    const ipSelection = document.getElementById('ip_selection');
    ipSelection.style.display = document.getElementById('change_ip_yes').checked ? 'block' : 'none';

    // 如果选择“否”，则清空已添加城市列表
    if (!document.getElementById('change_ip_yes').checked) {
        const cityList = document.getElementById('city_list');
        cityList.innerHTML = ''; // 清空城市列表
        document.getElementById('added_city_section').style.display = 'none'; // 隐藏已添加城市部分
    }
}

// 添加城市功能
function addCity() {
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    const cityList = document.getElementById('city_list');
    const addedCitySection = document.getElementById('added_city_section');

    let selectedProvince = provinceSelect.value;
    let selectedCity = citySelect.options[citySelect.selectedIndex].text;

    // 如果当前是通过搜索添加的城市，获取其所属省份
    if (citySelect.options[citySelect.selectedIndex].dataset.province) {
        selectedProvince = citySelect.options[citySelect.selectedIndex].dataset.province;
    }

    const displayCity = selectedProvince + '-' + selectedCity;

    if (selectedCity) {
        const listItem = document.createElement('li');
        listItem.className = 'list-group-item d-flex justify-content-between align-items-center'; // 保证列表项的样式
        listItem.textContent = displayCity;

        // 创建删除按钮
        const deleteButton = document.createElement('button');
        deleteButton.className = 'btn btn-danger btn-sm';
        deleteButton.textContent = '删除';
        deleteButton.onclick = function () {
            cityList.removeChild(listItem); // 删除城市
            if (cityList.children.length === 0) {
                addedCitySection.style.display = 'none'; // 隐藏已添加城市部分
            }
        };

        listItem.appendChild(deleteButton);
        cityList.appendChild(listItem);
        addedCitySection.style.display = 'block'; // 显示已添加城市部分
    }
}


var costForFirst100;
var costForAfter100;
var costForAfter200;
var unitCostFirst100;
var unitCostAfter100;
var unitCostAfter200;

function calculateTotalCost() {
    const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;

    // 计算 actual 值
    let actual = numberOfQuestions <= 15 ? 1 :
        numberOfQuestions <= 25 ? 2 :
            numberOfQuestions <= 35 ? 3 :
                numberOfQuestions <= 50 ? 4 : 5;

    // 获取是否换IP的选项
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value;

    // 获取目标份数
    const targetCount = parseInt(document.getElementById('target_count').value, 10);

    // 根据是否换IP确定单价系数
    unitCostFirst100 = changeIp === 'yes' ? 0.1 : 0.08;
    unitCostAfter100 = changeIp === 'yes' ? 0.08 : 0.06;
    unitCostAfter200 = changeIp === 'yes' ? 0.06 : 0.04; // 200 份后的单价

    // 计算前 100 份的费用
    costForFirst100 = Math.min(targetCount, 100) * unitCostFirst100;

    // 计算 101 到 200 份的费用
    costForAfter100 = Math.max(0, Math.min(targetCount - 100, 100)) * unitCostAfter100;

    // 计算超过 200 份的费用
    costForAfter200 = Math.max(0, targetCount - 200) * unitCostAfter200;

    // 计算总费用
    let totalCost = (actual - 1) * 2 + costForFirst100 + costForAfter100 + costForAfter200;

    // 应用最低消费 3 元
    if (totalCost < 3) {
        totalCost = 3; // 最低消费 3 元
    }
    return totalCost;
}


function calculateTotalCostForDB() {
    const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;

    // 计算 actual 值
    let actual = numberOfQuestions <= 15 ? 1 :
        numberOfQuestions <= 25 ? 2 :
            numberOfQuestions <= 35 ? 3 :
                numberOfQuestions <= 50 ? 4 : 5;

    // 获取是否换IP的选项
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value;

    // 获取目标份数
    const targetCount = parseInt(document.getElementById('target_count').value, 10);

    // 根据是否换IP确定单价
    const unitCostBefore200 = changeIp === 'yes' ? 0.08 : 0.06;
    const unitCostAfter200 = changeIp === 'yes' ? 0.06 : 0.04;

    // 计算总费用，分为 200 份之前和之后
    const costBefore200 = Math.min(targetCount, 200) * unitCostBefore200;
    const costAfter200 = Math.max(0, targetCount - 200) * unitCostAfter200;

    let totalCost = (actual - 1) * 1 + costBefore200 + costAfter200;

    // 应用最低消费 3 元
    if (totalCost < 3) {
        totalCost = 3; // 最低消费 3 元
    }

    return totalCost;
}

// 生成订单按钮的点击事件
document.querySelector('.btn-submit').addEventListener('click', function (event) {
    event.preventDefault(); // 阻止默认提交行为

    if (!checkAllProportionsStatus()) {
        return;
    }

    const isConfirmed = confirm("再次提醒：如果问卷含有量表题需要通过信效度分析，必须阅读【必看教程】信效度部分，并且按照相关操作设置，自定义设置比例不保证通过信效度！请确定是否继续操作？");
    if (!isConfirmed) {
        return;
    }
    // 收集表单数据
    const surveyLink = document.getElementById('survey_link').value;
    const targetCount = document.getElementById('target_count').value;
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    const mobileRatio = parseInt(document.getElementById('mobile_ratio_display').value, 10);
    const linkRatio = parseInt(document.getElementById('link_ratio_display').value, 10);
    const wechatRatio = parseInt(document.getElementById('wechat_ratio_display').value, 10);
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value;
    const fensanLevel = document.getElementById('fensan_level').value;

    // 获取问题数量
    const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;

    // 计算 actual 值
    let actual = numberOfQuestions <= 15 ? 1 : numberOfQuestions <= 25 ? 2 : numberOfQuestions <= 35 ? 3 : numberOfQuestions <= 50 ? 4 : 5;

    // 计算总金额+初始化总费用
    const totalCost = calculateTotalCost();

    // 获取已添加城市并去掉“删除”按钮的文字
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item')).map(item => item.textContent.replace('删除', '').trim());

    // 验证是否填写了目标份数
    if (!targetCount || parseInt(targetCount) <= 9) {
        alert("目标份数必须大于等于10份");
        document.getElementById('target_count').focus();
        return;
    }

    // 验证填写的时间范围
    if (parseInt(fillTimeStart) >= parseInt(fillTimeEnd) || !fillTimeStart || !fillTimeEnd) {
        alert("请填写正确的用时范围");
        document.getElementById('fill_time_start').focus();
        return;
    }
    if (parseInt(fillTimeEnd) <= 5) {
        if (!confirm("检测到设置最大的填写用时小于等于5秒，注意设置的填写用时是以秒为单位，请确定是否继续操作？")) {
            return;
        }
    }


    // 验证提交来源比例
    if (isNaN(mobileRatio) || isNaN(linkRatio) || isNaN(wechatRatio) ||
        mobileRatio < 0 || linkRatio < 0 || wechatRatio < 0 ||
        mobileRatio + linkRatio + wechatRatio !== 100) {
        alert("提交来源比例必须是大于等于0的整数，且三者之和必须为100%");
        document.getElementById('mobile_ratio').focus();
        return;
    }

    // 如果选择了换IP，验证是否添加了城市
    if (changeIp === 'yes' && addedCities.length === 0) {
        alert("请点击添加按钮，至少添加一个换IP区域");
        document.getElementById('province').focus();
        return;
    }

    // 如果选择了换IP，并且换IP城市超过 40 个城市
    if (addedCities.length > 40) {
        alert('换IP城市不能超过 40 个城市，请删除一点');
        document.getElementById('province').focus();
        return;
    }

    generate_jsondata(); // 假设该函数生成 json_data


    let isEmpty_json_data = Object.entries(json_data).length === 0;
    if (isEmpty_json_data) {
        alert("比例数据信息为空，无法提交订单");
        return;
    }
    generateJS()
    if (window.js_txt === '' || window.js_txt.length < 100) {
        alert("初始化订单脚本失败，请重新刷新页面填写信息");
        console.log("初始化订单脚本失败，请重新刷新页面填写信息");
        return;
    }

    if (typeof window.xin_xishu !== 'undefined' && window.xin_xishu > 8) {
        const isConfirmed = confirm("检测到平均每个维度里超过8个小题，可能会导致信效度很高（0.95以上）！建议每个维度3~6道题，详细原因请看【必看教程】【信效度】章节，请确定是否继续操作？(如果不介意信效度太高可以继续操作)");
        if (!isConfirmed) {
            return;
        }
    }

    if (typeof window.xinxiao_left !== 'undefined' && typeof window.xinxiao_right !== 'undefined') {
        let message = '详细原因请看【必看教程】【信效度】章节，请确定是否继续操作？';
        let flag = false;
        if ((window.xinxiao_left + window.xinxiao_right > 8)) {
            message = "检测到问卷信效度设置超过8个小题，可能会导致信效度很高（0.95以上）！建议单维度3~6道题，确认自己是不是应该使用【新版解析】，【新版解析】可以支持多维度，" + message+'(如果不介意信效度太高可以继续操作)';
            flag = true;
        }
        if (((window.xinxiao_left>window.xinxiao_right?window.xinxiao_left:window.xinxiao_right) / (window.xinxiao_left + window.xinxiao_right) < 0.7)) {
            message = "检测到问卷信效度设置的偏向超过30%的方向不统一，可能导致信效度会很低，" + message;
            flag = true;
        }
        if (flag) {
            const isConfirmed = confirm(message);
            if (!isConfirmed) {
                return;
            }
        }
    }


    //弹出用户必须知晓条款


    // 构建订单信息
    let orderInfo = `
    <table style="width: 100%; border-collapse: collapse; line-height: 2; padding: 4px;">

        <tr>
            <td style="font-size: 1.2rem; font-weight: bold; color: #007bff; width: 30%;">问卷链接:</td>
            <td style="color: #333;">${surveyLink}</td>
        </tr>
        <tr>
            <td style="font-size: 1.2rem; font-weight: bold; color: #007bff;">目标份数:</td>
            <td style="color: #333;">${targetCount}</td>
        </tr>
        <tr>
            <td style="font-size: 1.2rem; font-weight: bold; color: #007bff;">填写用时范围:</td>
            <td style="color: #333;">${fillTimeStart} 秒 ~ ${fillTimeEnd} 秒</td>
        </tr>
        <tr>
            <td style="font-size: 1.2rem; font-weight: bold; color: #007bff;">提交来源比例:</td>
           <td style="color: #333; display: flex; flex-wrap: nowrap; align-items: center;">
    <span style="margin-right: 10px;">- 手机提交: <span style="color: #007bff;">${mobileRatio}%</span></span>
    <span style="margin-right: 10px;">- 链接提交: <span style="color: #007bff;">${linkRatio}%</span></span>
    <span>- 微信提交: <span style="color: #007bff;">${wechatRatio}%</span></span>
</td>

        </tr>
        <tr>
            <td style="font-size: 1.2rem; font-weight: bold; color: #007bff;">分散级别:</td>
            <td style="color: #333;">${fensanLevel}</td>
        </tr>
        <tr>
            <td style="font-size: 1.2rem; font-weight: bold; color: #007bff;">是否换IP:</td>
            <td style="color: ${changeIp === 'yes' ? 'red' : 'green'};">${changeIp}</td>
        </tr>
    </table>
`;


    if (changeIp === 'yes' && addedCities.length > 0) {
        orderInfo += `
            <div style="font-size: 1.2rem; font-weight: bold; color: #007bff;">已添加地区:</div>
            <div style="margin-left: 10px; color: #333;">${addedCities.join(', ')}</div>
        `;
    }

    // 添加预计完成时间
    const fensanLevelToRange = {
        1: [2, 2], // 新的1级
        2: [5, 10],
        3: [10, 30],
        4: [30, 60],
        5: [60, 120],
        6: [120, 300],
        7: [180, 360], // 新的7级
        8: [240, 400],  // 新的8级
        11: [10, 60],
        12: [10, 120],
        13: [10, 180],
        14: [10, 240],
        15: [10, 300],
        16: [10, 360],
        17: [10, 420]
    };
    const range = fensanLevelToRange[fensanLevel] || [5, 10];
    const medianTime = (range[0] + range[1]) / 2;
    const estimatedTimeInMinutes = Math.round((targetCount * medianTime + targetCount * 5) / 60);
    const formattedTime = estimatedTimeInMinutes >= 60 ? `${Math.floor(estimatedTimeInMinutes / 60)} 小时 ${estimatedTimeInMinutes % 60} 分钟` : `${estimatedTimeInMinutes} 分钟`;

    orderInfo += `
        <div style="font-size: 1.2rem; font-weight: bold; color: #007bff;">预计需要时间:</div>
        <div style="margin-left: 10px; color: #333;">大约 ${formattedTime} 完成</div>
    `;

    // 处理比例信息
    // 处理比例信息
    let ratioInfo = `<div style="font-size: 1.2rem; font-weight: bold; color: #007bff;">比例信息:</div>`;
    // 预处理填空题答案：按题号分组
    const textareaAnswersByQuestion = {}; // 结构: { "题号": [ {option: "选项", answer: "内容"}, ... ] }
    for (const key in window.yifengTextareaMap) {
        const match = key.match(/yifeng(\d+)_(\d+)/);
        if (match) {
            const questionNum = match[1]; // 提取题号
            const optionNum = match[2];   // 提取选项编号
            const answer = window.yifengTextareaMap[key];
            if (answer.trim() !== "") {   // 过滤空内容
                if (!textareaAnswersByQuestion[questionNum]) {
                    textareaAnswersByQuestion[questionNum] = [];
                }
                textareaAnswersByQuestion[questionNum].push({
                    option: optionNum,
                    answer: answer.replace(/,/g, '<span style="color: red;">,</span>')
                });
            }
        }
    }
    for (const key in json_data) {
        if (json_data.hasOwnProperty(key)) {
            const questionData = json_data[key];
            ratioInfo += `<div style="margin-left: 20px; color: #333;">第${key}题:</div>`;

            // 如果 data 是对象，处理嵌套的情况
            if (typeof questionData.data === 'object' && !Array.isArray(questionData.data)) {
                for (const subKey in questionData.data) {
                    if (questionData.data.hasOwnProperty(subKey)) {
                        let formattedData = questionData.data[subKey].replace(/,/g, '<span style="color: red;">,</span>');
                        ratioInfo += `<div style="margin-left: 30px; color: #555;">${subKey}: ${formattedData}</div>`;
                    }
                }
            }
            // 如果 data 是数组，处理 type=9 的多项填空题
            else if (Array.isArray(questionData.data)) {
                ratioInfo += `<div style="margin-left: 30px; color: #555;">`;
                questionData.data.forEach((answer, index) => {
                    let formattedData = answer.replace(/,/g, '<span style="color: red;">,</span>');
                    ratioInfo += `填空项 ${index + 1}: ${formattedData}<br>`;
                });
                ratioInfo += `</div>`;
            }
            // 处理其他单一字符串类型的 data
            else {
                let formattedData = questionData.data.replace(/,/g, '<span style="color: red;">,</span>');
                ratioInfo += `<div style="margin-left: 30px; color: #555;">比例/数据: ${formattedData}</div>`;
            }
            // 添加关联的填空题答案
            // 添加关联的填空题答案
            if (textareaAnswersByQuestion[key]) {
                ratioInfo += `<div style="margin-left: 30px; color: #555;">选项带有填空题的答案:</div>`;  // 标题保持30px缩进
                textareaAnswersByQuestion[key].forEach(item => {
                    ratioInfo += `<div style="margin: 2px 0 2px 40px; color: #666;">第${item.option}个选项填空题的答案: ${item.answer}</div>`;
                });
            }
        }
    }

// 定义单价
    const unitCostFirst100 = changeIp === 'yes' ? 0.1 : 0.08;
    const unitCost100to200 = changeIp === 'yes' ? 0.08 : 0.06;
    const unitCostAfter200 = changeIp === 'yes' ? 0.06 : 0.04; // 200 份后的单价
    orderInfo += ratioInfo;
// 计算代币支付价格
    let tokenCost = calculateTotalCostForDB();

    // 构建费用计算公式
    let formulaParts = [`(${actual} - 1) × 2`];

    if (targetCount > 0) {
        formulaParts.push(`${Math.min(targetCount, 100)} × ${unitCostFirst100}`);
    }
    if (targetCount > 100) {
        formulaParts.push(`${Math.max(0, Math.min(targetCount - 100, 100))} × ${unitCostAfter100}`);
    }
    if (targetCount > 200) {
        formulaParts.push(`${Math.max(0, targetCount - 200)} × ${unitCostAfter200}`);
    }

    const formulaString = formulaParts.join(' + ');

    orderInfo += `
    <div style="font-weight: bold; font-size: 1.5rem; color: red; margin-top: 10px;">
需要费用: ${totalCost <= 3 ? '3 元' : `${formulaString} = ${totalCost.toFixed(2)} 元`}
        <br>
        <span style="font-weight: bold; font-size: 1.2rem; color: forestgreen;">
代币支付仅需: ${tokenCost.toFixed(2)} 代币  <a href="/tokenvault" target="_blank" style="margin-left: 10px; color: #007bff;">获取/充值代币码点这</a>
        </span>
        
<span style="color: #007bff; cursor: pointer;" onclick="toggleDetails()">[查看详细]</span>
    </div>
`;


// 计算详细费用过程并添加到 orderInfo
    orderInfo += `
    <div id="detailedInfo" style="display: none; margin-top: 1px; padding: 5px; background-color: #f0f0f0; border-radius: 5px;">
        <div style="font-size: 1rem; color: #333;">
            <p><strong>问卷复杂度计算过程:</strong> 问卷中的问题数量: ${numberOfQuestions}，得到的复杂度: ${actual}</p>
            <ul style="margin-left: 10px; padding-left: 10px; list-style: circle; margin-bottom: 0;">
                <li>问题数量 ≤ 15，复杂度 = 1</li>
                <li>15 < 数量 ≤ 25，复杂度 = 2</li>
                <li>25 < 数量 ≤ 35，复杂度 = 3</li>
                <li>35 < 数量 ≤ 50，复杂度 = 4</li>
                <li>问题数量 > 50，复杂度 = 5</li>
            </ul>
            <p><strong>单价计算规则:</strong> 是否换IP: ${changeIp === 'yes' ? '是' : '否'}</p>
            <ul style="margin-left: 10px; padding-left: 10px; list-style: circle; margin-bottom: 0;">
                <li>前100份单价    换IP: 0.1 元/份， 不换IP: 0.08 元/份</li>
                <li>100到200份单价 换IP: 0.08 元/份，不换IP: 0.06 元/份</li>
                <li>200份后单价    换IP: 0.06 元/份，不换IP: 0.04 元/份</li>
            </ul>
            <p><strong>费用计算公式:</strong> (问卷复杂度 - 1) × 2 + 前100份价格 + 100~200份的价格 + 200份之后的价格</p>
            <p><strong>代币支付计算公式:</strong> (问卷复杂度 - 1) × 1 + 前200份价格 + 200份之后的价格</p>
            <p><strong>详细计算过程:</strong></p>
            <ul style="margin-left: 10px; padding-left: 10px; list-style: circle; margin-bottom: 0;">
                <li>问卷复杂度部分: (${actual} - 1) × 2 = ${(actual - 1) * 2} 元</li>
                 <li>前 100 份部分: ${Math.min(targetCount, 100)} × ${unitCostFirst100} = ${(Math.min(targetCount, 100) * unitCostFirst100).toFixed(2)} 元</li>
                <li>100 到 200 份部分: ${Math.max(0, Math.min(targetCount - 100, 100))} × ${unitCost100to200} = ${(Math.max(0, Math.min(targetCount - 100, 100)) * unitCost100to200).toFixed(2)} 元</li>
                <li>超过 200 份部分: ${Math.max(0, targetCount - 200)} × ${unitCostAfter200} = ${(Math.max(0, targetCount - 200) * unitCostAfter200).toFixed(2)} 元</li>
                <li><strong>总费用（最低消费 3 元）:</strong> ${totalCost < 3 ? '3.00' : totalCost.toFixed(2)} 元</li>
            </ul>
        </div>
    </div>
`;

// 针对不同的屏幕尺寸进行布局优化
    const cssStyles = `
    #detailedInfo {
        line-height: 1.05 !important; /* 调整行距为 1.05 并确保覆盖其他样式 */
    }

    ul {
        margin-left: 8px !important;
        padding-left: 8px !important;
        margin-bottom: 3px !important; /* 减少列表项的底部间距 */
    }

    li {
        margin-bottom: 3px !important; /* 减少列表项的底部间距 */
    }

    @media (max-width: 600px) {
        #detailedInfo {
            font-size: 0.85rem !important;
            padding: 6px !important;
        }
        ul {
            margin-left: 6px !important;
            padding-left: 6px !important;
        }
        li {
            line-height: 1.05 !important;
            margin-bottom: 2px !important; /* 窄屏下减少列表项底部间距 */
        }
    }

    @media (min-width: 600px) {
        #detailedInfo {
            font-size: 0.9rem !important;
            padding: 8px !important;
        }
    }
`;

// 创建并插入样式元素到页面中
    const styleElement = document.createElement('style');
    styleElement.innerHTML = cssStyles;
    document.head.appendChild(styleElement);


    // 显示确认模态框
    document.getElementById('previewMessage').innerHTML = orderInfo;
    document.getElementById('previewModal').style.display = 'block';
});

function toggleDetails() {
    const details = document.getElementById('detailedInfo');
    if (details.style.display === 'none') {
        details.style.display = 'block';
    } else {
        details.style.display = 'none';
    }
}


// 确认按钮的点击事件
document.querySelector('.confirm-button1').addEventListener('click', function () {
    // 调用比例检查函数，确保比例填写正确
    if (checkAllProportionsStatus()) {
        let payType = 'alipay';
        // 比例填写正确，进行支付流程
        initiatePayment(payType);
        document.getElementById('previewModal').style.display = "none"; // 关闭预览模态框
    }
});

document.querySelector('.confirm-button2').addEventListener('click', function () {
    // 调用比例检查函数，确保比例填写正确
    if (checkAllProportionsStatus()) {
        let payType = 'wxpay';
        // 比例填写正确，进行支付流程
        initiatePayment(payType);
        document.getElementById('previewModal').style.display = "none"; // 关闭预览模态框
    }
});

let paymentCountdownInterval;
const paymentTimeoutDuration = 3 * 60 * 1000; // 10分钟的支付时限（以毫秒为单位）


// 初始化支付流程
function initiatePayment(payType) {
    const totalCost = calculateTotalCost();
    const targetCount = document.getElementById('target_count').value;
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value === 'yes';
    const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;


    // const targetCount = document.getElementById('target_count').value;
    const surveyLink = document.getElementById('survey_link').value;
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    const mobileRatio = document.getElementById('mobile_ratio_display').value;
    const linkRatio = document.getElementById('link_ratio_display').value;
    const wechatRatio = document.getElementById('wechat_ratio_display').value;
    const fensanLevel = document.getElementById('fensan_level').value;
    // const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;
    // 获取已添加城市列表并组合成字符串
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
        .map(item => item.textContent.replace('删除', '').trim());
    const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔
    const biliData = getInputValuesAsJson()

    // 获取当前时间
    let currentDate = new Date();
    let year = currentDate.getFullYear();
    let month = String(currentDate.getMonth() + 1).padStart(2, "0");
    let day = String(currentDate.getDate()).padStart(2, "0");
    let hours = String(currentDate.getHours()).padStart(2, "0");
    let minutes = String(currentDate.getMinutes()).padStart(2, "0");
    let seconds = String(currentDate.getSeconds()).padStart(2, "0");

    // 构建文件名
    let fileName = `油猴脚本VM版_${year}${month}${day}${hours}${minutes}${seconds}.txt`;

    showLoading()

    // 向后端请求生成支付二维码
    fetch('generate-payment-order-safety', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            amount: totalCost,
            targetCount: targetCount,
            changeIp: changeIp,
            numberOfQuestions: numberOfQuestions,
            type: 1, // 指定为正常模式
            payType: payType,

            surveyLink: surveyLink,
            ipArea: ipArea, // 已添加的所有城市
            fensanLevel: fensanLevel,
            sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
            tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
            biliData: biliData,

            content: window.js_txt,
            fileName: fileName
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.outTradeNo && data.paymentImage) {
                // 显示支付二维码模态框
                showPaymentModal(data.paymentImage, totalCost.toFixed(2), data.qrcode, payType);
                // 开始轮询支付状态
                startPaymentStatusPolling(data.outTradeNo);
            } else {
                alert('生成付款码发生错误，请重试。');
            }
        })
        .catch(error => {
            console.error('生成支付信息时发生错误:', error);
            alert('生成付款码发生错误，请刷新页面重试。');
        })
        .finally(() => {
            hideLoading()
        });
}

function isMobileDevice() {
    // 优先使用现代API检测
    if (typeof window.orientation !== 'undefined' || navigator.maxTouchPoints > 0) {
        return true;
    }

    // 更全面的UA检测
    const ua = navigator.userAgent;
    const isMobile = /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(ua);
    const isTablet = /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino|android|ipad|playbook|silk/i.test(ua);

    // 屏幕尺寸辅助判断
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const isSmallScreen = screenWidth < 768 || screenHeight < 768;

    // 综合判断逻辑
    return (isMobile || isTablet) && isSmallScreen;
}

function showPaymentModal(paymentImageUrl, amount, qrcode = '', payType) {
    const paymentModal = document.getElementById('paymentModal');
    document.getElementById('payment-qr-code').src = paymentImageUrl;
    document.getElementById('payment-amount').textContent = amount;
    paymentModal.style.display = 'block';

    const titleElement = paymentModal.querySelector('h2');
    const pullButtonElement = paymentModal.querySelector('#mobilePaySection button');
    const qrCodeImg = document.getElementById('payment-qr-code');
    // 根据支付类型设置内容
    if (payType === 'wxpay') {
        titleElement.textContent = '请使用微信扫码支付';
        pullButtonElement.textContent = '启动微信支付';
        qrCodeImg.alt = '微信支付二维码';
        titleElement.style.color = '#07C160'; // 微信绿色
        pullButtonElement.style.color = 'white';
        pullButtonElement.style.backgroundColor = '#07C160';
        // 更换微信支付LOGO（需要准备微信图标）
    } else {
        titleElement.textContent = '请使用支付宝扫码支付';
        pullButtonElement.textContent = '启动支付宝支付';
        qrCodeImg.alt = '支付宝支付二维码';
        titleElement.style.color = '#007BFF'; // 微信绿色
        pullButtonElement.style.color = 'white';
        pullButtonElement.style.backgroundColor = '#007BFF';
    }

    // 移动端处理
    const mobileSection = document.getElementById('mobilePaySection');
    const alipayBtn = document.getElementById('alipayLaunchBtn');

    if (isMobileDevice()) {
        mobileSection.style.display = 'block';
        const alipayUrl = qrcode;
        console.log(alipayUrl)
        // 绑定点击事件
        alipayBtn.onclick = function () {
            // window.location.href = alipayUrl;
            window.open(alipayUrl, '_blank');
        };
    } else {
        mobileSection.style.display = 'none';
    }


    // 启动倒计时
    startPaymentCountdown(paymentTimeoutDuration);
}

// 动态加载动效的逻辑
function showLoadingSpinner() {
    // 创建并显示加载动效的 DOM 元素
    const spinner = document.createElement('div');
    spinner.id = 'loadingSpinner';

    // 整体背景样式
    spinner.style.position = 'fixed';
    spinner.style.top = '0';
    spinner.style.left = '0';
    spinner.style.width = '100%';
    spinner.style.height = '100%';
    spinner.style.backgroundColor = 'rgba(200, 200, 200, 0.8)'; // 浅灰色半透明背景
    spinner.style.zIndex = '1000';
    spinner.style.display = 'flex';
    spinner.style.justifyContent = 'center';
    spinner.style.alignItems = 'center';

    spinner.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);">
            <div class="spinner" style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <p style="margin-top: 10px; font-size: 16px; color: #333;">正在生成订单...请不要关闭页面！</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    document.body.appendChild(spinner);
}


function hideLoadingSpinner() {
    // 移除加载动效的 DOM 元素
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        document.body.removeChild(spinner);
    }
}

function showQueryingSpinner() {
    // 创建并显示查询动效的 DOM 元素
    const spinner = document.createElement('div');
    spinner.id = 'queryingSpinner';

    // 整体背景样式
    spinner.style.position = 'fixed';
    spinner.style.top = '0';
    spinner.style.left = '0';
    spinner.style.width = '100%';
    spinner.style.height = '100%';
    spinner.style.backgroundColor = 'rgba(200, 200, 200, 0.8)'; // 浅灰色半透明背景
    spinner.style.zIndex = '1000';
    spinner.style.display = 'flex';
    spinner.style.justifyContent = 'center';
    spinner.style.alignItems = 'center';

    spinner.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);">
            <div class="spinner" style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #ff5733; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <p style="margin-top: 10px; font-size: 16px; color: #333;">正在查询，请稍候...</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    document.body.appendChild(spinner);
}

function hideQueryingSpinner() {
    // 移除查询动效的 DOM 元素
    const spinner = document.getElementById('queryingSpinner');
    if (spinner) {
        document.body.removeChild(spinner);
    }
}


function startPaymentCountdown(duration) {
    const paymentCountdownElement = document.getElementById('payment-countdown');
    let timeRemaining = duration;

    // 清除任何现有的倒计时
    clearInterval(paymentCountdownInterval);

    // 立即更新倒计时显示
    updateCountdownDisplay(timeRemaining);

    paymentCountdownInterval = setInterval(function () {
        timeRemaining -= 1000; // 每次减少1秒（1000毫秒）

        if (timeRemaining <= 0) {
            clearInterval(paymentCountdownInterval);
            paymentCountdownElement.textContent = '00:00';
            alert('支付超时，请重新支付。');
            // 关闭支付模态框
            document.getElementById('paymentModal').style.display = 'none';
            // 停止支付状态轮询
            clearInterval(paymentStatusCheckInterval);
        } else {
            updateCountdownDisplay(timeRemaining);
        }
    }, 1000); // 每秒更新一次
}

function updateCountdownDisplay(timeRemaining) {
    const paymentCountdownElement = document.getElementById('payment-countdown');
    const totalSeconds = Math.floor(timeRemaining / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    // 格式化分钟和秒数，确保两位数显示
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
    const formattedSeconds = seconds < 10 ? '0' + seconds : seconds;

    paymentCountdownElement.textContent = formattedMinutes + ':' + formattedSeconds;
}


let paymentStatusCheckInterval;

function startPaymentStatusPolling(outTradeNo) {
    let pollingAttempts = 0;
    const pollingInterval = 4000; // 轮询间隔（毫秒）
    const maxPollingDuration = paymentTimeoutDuration; // 与倒计时持续时间匹配
    const maxPollingAttempts = Math.ceil(maxPollingDuration / pollingInterval);

    paymentStatusCheckInterval = setInterval(function () {
        fetch(`checkPaymentForOrderSafety?outTradeNo=${outTradeNo}`, {
            method: 'GET'
        })
            .then(response => response.json())
            .then(data => {
                // console.log(data)
                if (data.is_pay === 1) {
                    clearInterval(paymentStatusCheckInterval);

                    // alert('付款成功！订单已生成，点击确定打开查询订单页面（如果没有弹出查询页面，可以在首页点击查询已付款订单手动查询）');
                    // 关闭支付模态框
                    document.getElementById('paymentModal').style.display = 'none';
                    // window.location.href = `queryOrder?ordernum=${outTradeNo}`; // 在当前页面跳转到查询页面
                    // 显示询问模态框，并传递订单号
                    showConfirmModal(outTradeNo);
                } else if (data.is_pay === 0) {
                    // 支付未完成，继续轮询
                    pollingAttempts++;
                    if (pollingAttempts >= maxPollingAttempts) {
                        clearInterval(paymentStatusCheckInterval);
                        handlePaymentTimeout();
                    }
                } else {
                    console.log("发生错误，需要处理这种情况");
                }
            })
            .catch(error => {
                console.error('检查支付状态时发生错误:', error);
            });
    }, pollingInterval);
}

// 显示确认模态框（核心逻辑）
function showConfirmModal(outTradeNo) {
    const modal = document.getElementById('confirmModal');
    modal.style.display = 'block';

    // 确认按钮点击事件
    document.getElementById('confirmOpen').onclick = function () {
        // 直接在这里调用 window.open（用户点击触发，不会被拦截）
        const newWindow = window.open(`queryOrder?ordernum=${outTradeNo}`, '_blank');
        // 如果窗口被拦截，提供回退方案
        if (!newWindow || newWindow.closed) {
            alert('弹窗被拦截，请手动点击首页的“查询订单”按钮。');
        }
        modal.style.display = 'none';
    };

    // 取消按钮点击事件
    document.getElementById('cancelOpen').onclick = function () {
        modal.style.display = 'none';
    };
}

// 处理支付超时
function handlePaymentTimeout() {
    alert("支付超时，请重新支付。");
    document.getElementById('paymentModal').style.display = 'none';
}

// 取消按钮的点击事件
document.querySelector('.cancel-button').addEventListener('click', function () {
    document.getElementById('previewModal').style.display = "none"; // 关闭模态框
    window.js_txt = '';
});

// 关闭模态框
document.querySelector('.close').addEventListener('click', function () {
    document.getElementById('previewModal').style.display = 'none';
    window.js_txt = '';
});


// 点击窗口外部关闭模态框
window.onclick = function (event) {
    const modal = document.getElementById('previewModal');
    if (event.target === modal) {
        modal.style.display = "none";
    }
};

// function uploadFile(orderNumber) {
//     // 获取当前时间
//     let currentDate = new Date();
//     let year = currentDate.getFullYear();
//     let month = String(currentDate.getMonth() + 1).padStart(2, "0");
//     let day = String(currentDate.getDate()).padStart(2, "0");
//     let hours = String(currentDate.getHours()).padStart(2, "0");
//     let minutes = String(currentDate.getMinutes()).padStart(2, "0");
//     let seconds = String(currentDate.getSeconds()).padStart(2, "0");
//
//     // 构建文件名
//     let fileName = `油猴脚本VM版_${year}${month}${day}${hours}${minutes}${seconds}.txt`;
//
//     try {
//         let myservice = '47.96.43.54';
//
//         // const response = fetch("http://" + myservice + ":9090/order/uploadjsfororder", {
//         const response = fetch("order/uploadjsfororder", {
//             method: 'POST',
//             headers: {
//                 'Content-Type': 'application/json'
//             },
//             body: JSON.stringify({
//                 content: window.js_txt,
//                 fileName: fileName,
//                 token: orderNumber
//             })
//         });
//
//         if (response.ok) {
//             const data = response.json();
//             console.log('上传成功:', data);
//         } else {
//             console.error(`HTTP error! status: ${response.status}`);
//         }
//     } catch (error) {
//         console.error('上传失败:', error);
//     }
// }

// 模拟发送请求到后端创建订单的函数
// function createOrder() {
//     // 获取其他表单数据
//     const targetCount = document.getElementById('target_count').value;
//     const surveyLink = document.getElementById('survey_link').value;
//     const fillTimeStart = document.getElementById('fill_time_start').value;
//     const fillTimeEnd = document.getElementById('fill_time_end').value;
//     const mobileRatio = document.getElementById('mobile_ratio_display').value;
//     const linkRatio = document.getElementById('link_ratio_display').value;
//     const wechatRatio = document.getElementById('wechat_ratio_display').value;
//     const fensanLevel = document.getElementById('fensan_level').value;
//     const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;
//     // 获取已添加城市列表并组合成字符串
//     const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
//         .map(item => item.textContent.replace('删除', '').trim());
//     const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔
//     const biliData = getInputValuesAsJson()
//
//     // 创建表单数据对象
//     const formData = {
//         numberOfQuestions: numberOfQuestions,
//         targetCount: targetCount,
//         surveyLink: surveyLink,
//         ipArea: ipArea, // 已添加的所有城市
//         fensanLevel: fensanLevel,
//         sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
//         tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
//         price: calculateTotalCost(),
//         biliData: biliData
//     };
//
//     //console.log(formData); // 检查输出的数据结构是否正确
//
//     // 发送请求到后端创建订单
//     fetch('order/createOrder', {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json' // 指定请求体为 JSON 格式
//         },
//         body: JSON.stringify(formData) // 将数据转换为 JSON 字符串
//     })
//         .then(response => response.json())
//         .then(data => {
//             if (data.success) {
//                 const orderNumber = data.orderNumber; // 获取生成的订单号
//                 console.log(`订单创建成功，订单号: ${orderNumber}`);
//                 // 关闭支付模态框
//                 document.getElementById('paymentModal').style.display = 'none';
//
//                 // 调用已有的 generateJS 方法生成 JS 内容
//                 uploadFile(orderNumber);
//
//                 // 重置重试计数器
//                 retryCount = 0;
//
//                 // 延迟 1 秒后调用 checkOrder 方法来验证订单脚本生成情况
//                 setTimeout(function () {
//                     checkOrder(orderNumber);
//                 }, 2000); // 1000 毫秒 = 1 秒
//             } else {
//                 // 隐藏加载动效（如果超时时也需要关闭）
//                 hideLoadingSpinner();
//                 alert('订单创建失败，请刷新网页重试。');
//             }
//         })
//         .catch(error => {
//             console.error('创建订单时发生错误:', error);
//             alert('创建订单时发生错误，请稍后重试。');
//         });
// }

// 关闭支付模态框
document.querySelector('.close-payment').addEventListener('click', function () {
    document.getElementById('paymentModal').style.display = 'none';
    clearInterval(paymentStatusCheckInterval); // 停止轮询
    clearInterval(paymentCountdownInterval); // 停止倒计时
});


// 检查订单是否成功生成并上传了脚本的函数
// function checkOrder(orderNumber) {
//     fetch(`order/checkOrder?orderNumber=${orderNumber}`, {
//         method: 'GET'
//     })
//         .then(response => response.json())
//         .then(data => {
//             if (data.status === 'success') {
//                 // 隐藏加载动效（如果超时时也需要关闭）
//                 hideLoadingSpinner();
//                 alert('订单生成成功！点击确定跳转到查询订单页面');
//                 window.open(`queryOrder?ordernum=${orderNumber}`, '_blank');
//             } else {
//                 if (retryCount < maxRetryAttempts) {
//                     retryCount++;
//                     console.log(`生成订单失败，正在重试第 ${retryCount} 次...`);
//
//                     // 调用 generateJS 再次尝试生成 JS 内容
//                     uploadFile(orderNumber);
//
//                     // 2秒后重新调用 checkOrder 来验证订单脚本生成情况
//                     setTimeout(function () {
//                         checkOrder(orderNumber);
//                     }, 2000); // 2秒后重试
//                 } else {
//                     // 隐藏加载动效（如果超时时也需要关闭）
//                     hideLoadingSpinner();
//                     alert('生成订单失败，请联系管理员，加微信：zyy835573228或者qq:751947907');
//                 }
//             }
//         })
//         .catch(error => {
//             console.error('检查订单脚本时发生错误:', error);
//             // 隐藏加载动效（如果超时时也需要关闭）
//             hideLoadingSpinner();
//             alert('检查订单脚本时发生错误，请稍后重试。');
//         });
// }

// 代币支付按钮点击事件
document.querySelector('.token-button').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'block';
});

// 代币支付代币激活码输入模态框的关闭事件
document.querySelector('.close-token-input').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'none';
});

// 代币支付代币激活码确认按钮点击事件
document.querySelector('.confirm-identity-code').addEventListener('click', function () {
    const identityCode = document.getElementById('identityCodeInput').value.trim();
    //显示正在查询动效
    showQueryingSpinner();

    if (identityCode) {
        // 发送请求到后端，查询代币激活码相关的代币信息
        fetch('tokenvault/getTokenInfo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({activationCode: identityCode})
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    //关闭正在查询动效
                    hideQueryingSpinner();
                    // 显示查询到的代币激活码相关信息
                    document.getElementById('identityCodeInfo').textContent = `${identityCode}，余额: ${data.tokenBalance} 代币`;
                    document.getElementById('tokenInputModal').style.display = 'none';
                    document.getElementById('tokenPaymentConfirmModal').style.display = 'block';
                } else {
                    //关闭正在查询动效
                    hideQueryingSpinner();
                    alert('代币激活码无效或未找到，请重新输入。');
                }
            })
            .catch(error => {
                //关闭正在查询动效
                hideQueryingSpinner();
                console.error('查询代币激活码时发生错误:', error);
                alert('查询代币激活码时发生错误，请稍后重试。');
            });
    } else {
        //关闭正在查询动效
        hideQueryingSpinner();
        alert('请输入有效的代币激活码');
    }
});


// 代币支付确认模态框的关闭事件
document.querySelector('.close-token-confirm').addEventListener('click', function () {
    document.getElementById('tokenPaymentConfirmModal').style.display = 'none';
});

// 最终确认代币支付按钮点击事件
document.querySelector('.final-confirm-token-payment').addEventListener('click', function () {


    const identityCode = document.getElementById('identityCodeInput').value.trim();
    let tokenCost = calculateTotalCostForDB();

    if (!identityCode) {
        alert('请输入有效的代币激活码');
        return;
    }

    if (tokenCost <= 0) {
        alert('无效的支付金额');
        return;
    }

    const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;
    const targetCount = parseInt(document.getElementById('target_count').value, 10);
    const changeIp = document.querySelector('input[name="change_ip"]:checked').value === 'yes';

    // const targetCount = document.getElementById('target_count').value;
    const surveyLink = document.getElementById('survey_link').value;
    const fillTimeStart = document.getElementById('fill_time_start').value;
    const fillTimeEnd = document.getElementById('fill_time_end').value;
    const mobileRatio = document.getElementById('mobile_ratio_display').value;
    const linkRatio = document.getElementById('link_ratio_display').value;
    const wechatRatio = document.getElementById('wechat_ratio_display').value;
    const fensanLevel = document.getElementById('fensan_level').value;
    // const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;
    const biliData = getInputValuesAsJson()
    // 获取已添加城市列表并组合成字符串
    const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
        .map(item => item.textContent.replace('删除', '').trim());
    const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔

    // 获取当前时间
    let currentDate = new Date();
    let year = currentDate.getFullYear();
    let month = String(currentDate.getMonth() + 1).padStart(2, "0");
    let day = String(currentDate.getDate()).padStart(2, "0");
    let hours = String(currentDate.getHours()).padStart(2, "0");
    let minutes = String(currentDate.getMinutes()).padStart(2, "0");
    let seconds = String(currentDate.getSeconds()).padStart(2, "0");

    // 构建文件名
    let fileName = `油猴脚本VM版_${year}${month}${day}${hours}${minutes}${seconds}.txt`;


    showLoading()

    // 发送代币支付请求到后端
    fetch('tokenvault/payWithToken', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            activationCode: identityCode,
            amount: tokenCost,
            numberOfQuestions: numberOfQuestions,
            targetCount: targetCount,
            changeIp: changeIp,
            type: 1, // 指定为正常模式

            surveyLink: surveyLink,
            ipArea: ipArea, // 已添加的所有城市
            fensanLevel: fensanLevel,
            sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
            tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
            biliData: biliData,

            content: window.js_txt,
            fileName: fileName
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // alert('代币支付成功！正在生成订单。');
                // 显示加载动效
                // showLoadingSpinner();
                // alert('付款成功！订单已生成，点击确定打开查询订单页面（如果没有弹出查询页面，可以在首页点击查询代币码信息手动查询）');
                // 关闭支付模态框

                // window.location.href = `queryOrder?ordernum=${outTradeNo}`; // 在当前页面跳转到查询页面
                document.getElementById('tokenPaymentConfirmModal').style.display = 'none';

                // 显示询问模态框，并传递订单号
                showConfirmModal(data.outTradeNo);
                // window.open(`queryOrder?ordernum=${data.outTradeNo}`, '_blank');
                // 调用代币订单创建接口
                // createOrderForDB(identityCode);  // 不传递交易号，由后端 session 管理
            } else {
                alert(data.message || '代币支付失败，请检查您的余额是否足够（不够可以点击【获取代币码】或者【查询代币码】按钮后自助充值）');
            }
        })
        .catch(error => {
            console.error('代币支付时发生错误:', error);
            alert('代币支付时发生错误，请稍后重试。');
        })
        .finally(() => {
            // 隐藏加载动效
            hideLoading()
        });

});

// 封装为一个函数，传入 .custom-input 类的元素，返回 JSON 格式的字符串
// function getInputValuesAsJson() {
//     // 获取所有 .custom-input 类下的 input 元素
//     const inputElements = document.querySelectorAll('.custom-input');
//
//     // 创建一个空的数组，用于存储 input 元素的值
//     const inputValues = [];
//
//     // 遍历所有 input 元素，获取它们的值并存储到数组中
//     inputElements.forEach(input => {
//         inputValues.push(input.value); // 将每个 input 的值添加到数组中
//     });
//
//     // 将 inputValues 转换为 JSON 格式字符串，且不加换行或缩进
//     return JSON.stringify(inputValues); // 第三个参数省略，避免换行
// }
function getInputValuesAsJson() {
    // 获取所有 .custom-input 类下的 input 元素
    // 第一部分：收集所有新字段数据
    const newData = {
        target_count: document.getElementById('target_count').value,
        fill_time_start: document.getElementById('fill_time_start').value,
        fill_time_end: document.getElementById('fill_time_end').value,
        mobile_ratio: document.getElementById('mobile_ratio').value,
        link_ratio: document.getElementById('link_ratio').value,
        wechat_ratio: document.getElementById('wechat_ratio').value,
        fensan_level: document.getElementById('fensan_level').value,
        change_ip: document.querySelector('input[name="change_ip"]:checked').value,
        ip_areas: Array.from(document.querySelectorAll('#city_list .list-group-item')).map(item =>
            item.textContent.replace('删除', '').trim()
        )
    };

    // 第二部分：收集原有.custom-input数据
    const customInputs = Array.from(document.querySelectorAll('.custom-input')).map(input => input.value);

    // 合并成统一数组（新字段在前，旧数据在后）
    const exportArray = [
        newData.target_count,
        newData.fill_time_start,
        newData.fill_time_end,
        newData.mobile_ratio,
        newData.link_ratio,
        newData.wechat_ratio,
        newData.fensan_level,
        newData.change_ip,
        newData.ip_areas.join('|'), // 用竖线分隔城市
        ...customInputs // 原有数据追加在末尾
    ];

    // 将 inputValues 转换为 JSON 格式字符串，且不加换行或缩进
    return JSON.stringify(exportArray); // 第三个参数省略，避免换行
}

// 调用代币订单创建的函数
// function createOrderForDB(identityCode) {
//     // 获取其他表单数据
//     const targetCount = document.getElementById('target_count').value;
//     const surveyLink = document.getElementById('survey_link').value;
//     const fillTimeStart = document.getElementById('fill_time_start').value;
//     const fillTimeEnd = document.getElementById('fill_time_end').value;
//     const mobileRatio = document.getElementById('mobile_ratio_display').value;
//     const linkRatio = document.getElementById('link_ratio_display').value;
//     const wechatRatio = document.getElementById('wechat_ratio_display').value;
//     const fensanLevel = document.getElementById('fensan_level').value;
//     const numberOfQuestions = document.querySelectorAll('.field.ui-field-contain').length;
//     const biliData = getInputValuesAsJson()
//
//     // 获取已添加城市列表并组合成字符串
//     const addedCities = Array.from(document.querySelectorAll('#city_list .list-group-item'))
//         .map(item => item.textContent.replace('删除', '').trim());
//     const ipArea = addedCities.join(', '); // 将所有城市组合成一个字符串，以逗号分隔
//
//
//     // 创建表单数据对象
//     const formData = {
//         targetCount,
//         surveyLink,
//         ipArea,
//         fensanLevel,
//         sourceBili: `${mobileRatio},${linkRatio},${wechatRatio}`, // 格式化来源比例
//         tianxieTime: `${fillTimeStart},${fillTimeEnd}`, // 填写用时范围
//         price: calculateTotalCostForDB(),
//         activationCode: identityCode,
//         numberOfQuestions: numberOfQuestions,
//         biliData: biliData
//     };
//
//     // 发送请求到后端创建代币支付订单
//     fetch('order/createOrderForDB', {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json' // 指定请求体为 JSON 格式
//         },
//         body: JSON.stringify(formData) // 将数据转换为 JSON 字符串
//     })
//         .then(response => response.json())
//         .then(data => {
//             if (data.success) {
//                 const orderNumber = data.orderNumber; // 获取生成的订单号
//                 console.log(`订单创建成功，订单号: ${orderNumber}`);
//                 // 调用已有的 generateJS 方法生成 JS 内容
//                 uploadFile(orderNumber);
//                 // 重置重试计数器
//                 retryCount = 0;
//                 // 延迟 1 秒后调用 checkOrder 方法来验证订单脚本生成情况
//                 setTimeout(() => checkOrder(orderNumber), 2000);
//             } else {
//                 // 隐藏加载动效（如果超时时也需要关闭）
//                 hideLoadingSpinner();
//                 alert(data.message || '订单创建失败，请重试。');
//             }
//         })
//         .catch(error => {
//             console.error('创建订单时发生错误:', error);
//             // 隐藏加载动效（如果超时时也需要关闭）
//             hideLoadingSpinner();
//             alert('创建订单时发生错误，请稍后重试。');
//         });
// }


// 代币支付取消按钮点击事件
document.querySelector('.cancel-token-payment').addEventListener('click', function () {
    document.getElementById('tokenPaymentConfirmModal').style.display = 'none';
});

// 代币支付代币激活码输入取消按钮点击事件
document.querySelector('.cancel-identity-code').addEventListener('click', function () {
    document.getElementById('tokenInputModal').style.display = 'none';
});

function showLoading() {
    document.getElementById('globalLoading').classList.remove('d-none');
}

function hideLoading() {
    document.getElementById('globalLoading').classList.add('d-none');
}


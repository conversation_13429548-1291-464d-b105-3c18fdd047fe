// ==UserScript==
// @name         问卷星易风代码生成器 2025-3-12 后端生成版（阉割生成脚本）(另辟蹊径)(信效度大更新)
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  填写选项比例，一键生成VM模板脚本
// <AUTHOR>
// @match        https://www.wjx.cn/vm/*
// @match        https://www.wjx.cn/vj/*
// @match        https://ks.wjx.top/*
// @match        https://ww.wjx.top/*
// @match        https://w.wjx.top/*
// @match        https://*.wjx.top/*
// @match        https://*.wjx.cn/vm/*
// @match        https://*.wjx.cn/vj/*
// @match        https://*.wjx.com/vm/*
// @match        https://*.wjx.com/vj/*
// @match        http://survey.yonghu.org.cn/vm/*
// @connect ************
// @connect ***********
// @connect 127.0.0.1
// @icon       http://***********:9090/favicon.ico
// ==/UserScript==

(function() {
    // Config
    let now1=new Date();
    //let dataline= Date.parse("2024/9/30 00:00")
    var myVersion = 5.5
    var passFlag = false;
    let qwert=4125916800000
    var inputToken;
    //var myservice = "************"
    var myservice = "***********"
    //var myservice = "127.0.0.1"
    var addToWindow_func = [sleep ]

    // 初始化
    initElement();
    window.checkAllProportionsStatus = checkAllProportionsStatus;
    window.generateJS = generateJS;
    window.generate_jsondata = generate_jsondata;
    window.js_txt = '';
    //window.uploadFile = uploadFile;


    var final_json_data;
    window.json_data = final_json_data;


    // 创建一个新的style元素
    var styleElement = document.createElement("style");

    // 将样式内容添加到style元素中
    styleElement.innerHTML = `
    .custom-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .table-row {
        border-bottom: 1px solid #ddd;
    }

    .option-cell,
    .input-cell {
        padding: 10px;
        text-align: center;
    }

    .custom-input {
        width: 80%;
        box-sizing: border-box;
    }
    .field-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}



    /* 自定义按钮样式 */
#oneRandom_btn{
       font-size: 16px !important; /* 调整按钮字体大小 */
            width: 150px !important;    /* 调整按钮宽度 */
            height: 50px !important;    /* 调整按钮高度 */
            background-color: orange !important; /* 设置按钮背景颜色 */
            color: white !important;    /* 设置按钮文本颜色 */
}
#oneRandom_btn2{
       font-size: 16px !important; /* 调整按钮字体大小 */
            width: 150px !important;    /* 调整按钮宽度 */
            height: 50px !important;    /* 调整按钮高度 */
            background-color: green !important; /* 设置按钮背景颜色 */
            color: white !important;    /* 设置按钮文本颜色 */
}
#jiancha_btn{
       font-size: 16px !important; /* 调整按钮字体大小 */
            width: 150px !important;    /* 调整按钮宽度 */
            height: 50px !important;    /* 调整按钮高度 */
            background-color: purple !important; /* 设置按钮背景颜色 */
            color: white !important;    /* 设置按钮文本颜色 */
}

#jiaocheng__btn{
       font-size: 16px !important; /* 调整按钮字体大小 */
            width: 150px !important;    /* 调整按钮宽度 */
            height: 50px !important;    /* 调整按钮高度 */
            background-color: red !important; /* 设置按钮背景颜色 */
            color: white !important;    /* 设置按钮文本颜色 */
}

/* 为自定义按钮容器设置布局 */
.custom-button-container {
    display: flex;
    flex-wrap: wrap;  /* 自动换行 */
    justify-content: center;  /* 居中显示 */
    gap: 10px;  /* 按钮之间的间距 */
    margin-top: 1rem;
}

/* 自定义按钮样式 */
.custom-action-button {
    font-size: 16px !important;
    width: calc(50% - 10px); /* 按钮占据50%的宽度，并在窄屏时换行 */
    max-width: 200px; /* 按钮的最大宽度 */
    height: 50px !important;
    color: white !important;
    /* 你的自定义背景颜色 */
}

.custom-action-button#oneRandom_btn {
    background-color: orange !important;
}

.custom-action-button#oneRandom_btn2 {
    background-color: green !important;
}

.custom-action-button#jiancha_btn {
    background-color: purple !important;
}

.custom-action-button#jiaocheng__btn {
    background-color: red !important;
}


/* 自定义按钮的通用样式 */
.custom-action-button {
    display: flex; /* 使用 flexbox 布局 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    text-align: center; /* 确保文字居中 */
    padding: 10px;
    font-size: 16px !important; /* 调整按钮字体大小 */
    border-radius: 5px; /* 按钮圆角 */
    color: white !important; /* 设置按钮文本颜色 */
}

/* 默认情况下，按钮保持水平排列 */
.import-btn, .clear-btn, .export-btn {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;  /* 增加底部间距，防止按钮紧挨 */
}

/* 针对屏幕宽度小于等于768px的情况进行样式调整 */
@media (max-width: 768px) {
    .import-btn, .clear-btn, .export-btn {
        display: block;  /* 改为块级元素，竖排显示 */
        width: 100%;  /* 按钮占满容器的宽度 */
        margin-right: 0;  /* 清除右边距 */
        margin-bottom: 10px;  /* 继续保留底部间距 */
    }
}


`;

    // 将style元素添加到页面的head中
    document.head.appendChild(styleElement);

    // 初始化页面元素
    async function initElement() {
        //解除复制粘贴
        unlockRestrictions()
        try {
            document.getElementById('toptitle').style.cssText = 'display: flex !important; flex-direction: column !important; justify-content: center !important; align-items: center !important; width: 100% !important;';
        } catch (e) {
            console.log("重置样式失败");
        }

        /*
        $('head').append($('<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.1/jquery.min.js"></script>')) // 名称：jquery，版本：3.6.1，原始地址：https://www.bootcdn.cn/jquery/
        $('head').append('<script src="https://unpkg.com/layui@2.6.8/dist/layui.js"></script>')
        $('head').append('<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>')
        $('head').append($('<link rel="stylesheet" href="https://unpkg.com/layui@2.6.8/dist/css/layui.css">'))
*/

        if (typeof layer == 'undefined') {
            // $('head').append('<script src="https://www.layuicdn.com/layer-v3.5.1/layer.js"></script>') // 名称：layer，版本：3.5.1，原始地址：https://www.layuicdn.com/#Layer
        }



        // 等待layer加载成功
        while (true) {
            if (typeof layer != 'undefined') {
                break
            }
            await sleep(0.5)
        }

        // 解除复制粘贴限制
        setTimeout(function () {
            $(".textCont,input,textarea").off();
        },2000)
        $(".textCont,input,textarea").off(); // 既不生效，再来一次又何妨

        // 检查是否需要清理cookie，是否需要绕过微信限制，放一个定时器时刻检查是否需要绕过企业版限制
        setTimeout(function () {
            // 优先级：cookie最高，展开page第二，wechat最低
            checkNeedBypassWechat();
            checkNeedExpandPage();
            checkNeedExpandQuestion();
        },1000)


        // 将函数加载到window
        initAllFuncToWindow()

        // 显示每道题旁的按钮
        initButtonNearQuestion()
    }

    // 初始化每道题旁边的按钮
    function initButtonNearQuestion(){
        var div_list = $(".field-label");
        let lists = $('.field.ui-field-contain')
        // 添加一键生成比例答案按钮
        const oneRandom_btn = $(`
            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm layui-btn-lg" id="oneRandom_btn">一键平均比例答案</button>
        `);
        const oneRandom_btn2 = $(`
            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm layui-btn-lg" id="oneRandom_btn2">一键随机比例答案</button>
        `);
        const jiancha_btn = $(`
            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm layui-btn-lg" id="jiancha_btn">一键检查比例</button>
        `);
        const jiaocheng__btn = $(`
            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm layui-btn-lg" id="jiaocheng__btn">必看教程</button>
        `);

        const btnContainer = $('<div class="custom-button-container"></div>'); // 为容器添加自定义类名
        const btnContainer2 = $('<div class="custom-button-container"></div>');


        btnContainer.append(oneRandom_btn); // 将按钮添加到容器中
        btnContainer.append(oneRandom_btn2); // 将按钮添加到容器中
        btnContainer2.append(jiancha_btn); // 将按钮添加到容器中
        btnContainer2.append(jiaocheng__btn); // 将按钮添加到容器中

        // 给按钮添加自定义类名
        oneRandom_btn.addClass('custom-action-button');
        oneRandom_btn2.addClass('custom-action-button');
        jiancha_btn.addClass('custom-action-button');
        jiaocheng__btn.addClass('custom-action-button');

        //标注真实题号
        // 获取所有匹配选择器的元素
        var elements = document.querySelectorAll('.fieldset > .field.ui-field-contain >.field-label .topicnumber');
        if(elements.length==0){
            elements = document.querySelectorAll('.fieldset > .field.ui-field-contain >.field-label .topichtml');
        }

        // 遍历每个元素并添加 "第x题"，并设置颜色为红色
        elements.forEach(function(element, index) {
            var x = index + 1; // x从1开始
            var textNode = document.createTextNode("第" + x + "题 "); // 创建新的文本节点
            var spanNode = document.createElement("span"); // 创建新的span节点
            spanNode.style.color = "red"; // 设置字体颜色为红色
            spanNode.appendChild(textNode); // 添加文本节点到span节点
            element.insertBefore(spanNode, element.firstChild); // 将span节点插入到原始元素的最前面
        });

        const numberOfQuestions = lists.length; // 假设有10道题
        let actual=-1
        let advice = ''
        if(numberOfQuestions <= 15){
            actual=1
        }else if(numberOfQuestions >= 15 && numberOfQuestions <= 25){
            actual=2
        }else if( numberOfQuestions >= 25 && numberOfQuestions <= 35){
            actual=3
        }else if( numberOfQuestions >= 35 && numberOfQuestions <= 50){
            actual=4
        }
        else if(numberOfQuestions > 50){
            actual=5
        }

        // 创建一个新的label元素
        const newLabel = document.createElement('label');

        // 设置label的文本内容
        newLabel.textContent = `本问卷共${numberOfQuestions}题`;

        // 设置label的样式，使其居中
        newLabel.style.textAlign = 'center';

        // 将label添加到页面中的合适位置，例如在获取令牌按钮下面
        const getTokenBtn = document.getElementById('getToken__btn');
        $('#htitle').after(btnContainer2); // 将容器添加到标题后面
        $('#htitle').after(btnContainer); // 将容器添加到标题后面


        // 创建一个 div 元素来包装每个输入框和标题，并使用 Flex 布局
        var inputWrapper = document.createElement("div");
        inputWrapper.style.display = "flex";
        inputWrapper.style.alignItems = "center";
        inputWrapper.style.marginTop = "10px";

        var inputWrapper3 = document.createElement("div");
        var ruleLabel2 = document.createElement("label");
        ruleLabel2.textContent = `
                使用信效度功能务必先阅读完【必看教程】中关于信效度部分！
               `;
        ruleLabel2.style.color = "blue"; // 可以自定义样式
        inputWrapper3.appendChild(ruleLabel2);

        var inputWrapper4 = document.createElement("div");
        var ruleLabel3 = document.createElement("label");
        ruleLabel3.textContent = `

               `;
        ruleLabel3.style.color = "red"; // 可以自定义样式
        inputWrapper4.appendChild(ruleLabel3);

        // 将包装 div 和规则提示添加到 "toptitle" div 下
        var toptitleDiv = document.getElementById("toptitle");
        toptitleDiv.appendChild(inputWrapper);
        toptitleDiv.appendChild(inputWrapper3);
        toptitleDiv.appendChild(inputWrapper4);


        //==================================================================================

        // 创建标题
        var title = document.createElement("div");
        title.textContent = "快速导入工具框"; // 设置标题文本
        title.style.fontSize = "16px"; // 设置字体大小
        title.style.fontWeight = "bold"; // 设置加粗
        title.style.marginBottom = "10px"; // 底部间距
        title.style.marginTop = "20px"; // 上边距，调整为合适的值
        title.style.textAlign = "left"; // 靠左对齐

        // 创建文本输入框 (textarea)
        var textArea = document.createElement("textarea");
        textArea.style.width = "100%"; // 宽度100%
        textArea.style.height = "150px"; // 高度150px
        textArea.style.border = "1px solid #ccc"; // 边框颜色
        textArea.style.borderRadius = "5px"; // 边角圆滑
        textArea.style.padding = "10px"; // 内边距，提升可读性
        textArea.placeholder = "将导出的配置信息完整粘贴过来,可以实现快速填入配置信息（用于相同的问卷）。"; // 提示文字

        // 创建按钮容器
        var buttonContainer = document.createElement("div");
        buttonContainer.style.marginTop = "10px"; // 上方间距
        buttonContainer.style.display = "flex"; // 使用Flex布局
        buttonContainer.style.alignItems = "center"; // 垂直居中
        buttonContainer.style.flexWrap = "wrap"; // 当宽度不足时，按钮换行

        // 创建 "导入配置信息" 按钮
        var importButton = document.createElement("button");
        importButton.textContent = "导入配置信息";
        importButton.classList.add("import-btn"); // 添加类名
        importButton.style.backgroundColor = "#007bff"; // 按钮背景颜色
        importButton.style.color = "white"; // 按钮文本颜色
        importButton.style.border = "none"; // 去掉默认边框
        importButton.style.padding = "10px 20px"; // 内边距
        importButton.style.borderRadius = "5px"; // 按钮圆角
        importButton.style.marginRight = "10px"; // 按钮右边距
        importButton.style.cursor = "pointer"; // 鼠标悬停时显示为手形
        importButton.type = "button"; // 设置为普通按钮，防止表单提交

        // 创建 "清空" 按钮
        var clearButton = document.createElement("button");
        clearButton.textContent = "清空";
        clearButton.classList.add("clear-btn"); // 添加类名
        clearButton.style.backgroundColor = "#dc3545"; // 按钮背景颜色
        clearButton.style.color = "white"; // 按钮文本颜色
        clearButton.style.border = "none"; // 去掉默认边框
        clearButton.style.padding = "10px 20px"; // 内边距
        clearButton.style.borderRadius = "5px"; // 按钮圆角
        clearButton.style.marginRight = "10px"; // 按钮右边距
        clearButton.style.cursor = "pointer"; // 鼠标悬停时显示为手形
        clearButton.type = "button"; // 设置为普通按钮，防止表单提交

        // 清空按钮点击事件
        clearButton.addEventListener('click', function(event) {
            event.preventDefault(); // 阻止默认行为
            textArea.value = ''; // 清空文本框内容
            layer.msg('已清空配置信息', {icon: 1});
        });

        // 阻止导入比例数据按钮的默认行为（防止跳转或其他事件）
        importButton.addEventListener('click', async function(event) {
            event.preventDefault();
            try {
                const importedArray = JSON.parse(textArea.value);

                // 第一部分：基础字段解析（保持原样）
                document.getElementById('target_count').value = importedArray[0] || '';
                document.getElementById('fill_time_start').value = importedArray[1] || '';
                document.getElementById('fill_time_end').value = importedArray[2] || '';

                // 比例滑块处理（保持原样）
                const updateRatioDisplay = (sliderId, displayId, value) => {
                    const slider = document.getElementById(sliderId);
                    const display = document.getElementById(displayId);
                    slider.value = value;
                    display.value = value;
                };
                updateRatioDisplay('mobile_ratio', 'mobile_ratio_display', importedArray[3] || 33);
                updateRatioDisplay('link_ratio', 'link_ratio_display', importedArray[4] || 33);
                updateRatioDisplay('wechat_ratio', 'wechat_ratio_display', importedArray[5] || 34);

                document.getElementById('fensan_level').value = importedArray[6] || '2';

                // 换IP选项处理（保持原样）
                const changeIpValue = importedArray[7] || 'no';
                document.getElementById(`change_ip_${changeIpValue}`).checked = true;
                toggleCitySelection();

                // 城市列表处理（关键修复部分）
                const cityList = document.getElementById('city_list');
                cityList.innerHTML = '';
                if (importedArray[8]) {
                    // 创建省份-城市映射缓存（避免重复查询DOM）
                    const provinceCache = new Map();
                    Array.from(document.getElementById('province').options).forEach(opt => {
                        provinceCache.set(opt.text, opt.value);
                    });

                    // 单个城市处理流程
                    const processCity = async (area) => {
                        try {
                            const [targetProvince, targetCity] = area.split('-');

                            // 1. 选择省份
                            const provinceSelect = document.getElementById('province');
                            const provinceValue = provinceCache.get(targetProvince);
                            if (!provinceValue) {
                                console.warn(`省份不存在: ${targetProvince}`);
                                return;
                            }
                            provinceSelect.value = provinceValue;

                            // 2. 等待城市加载完成
                            await new Promise(resolve => {
                                const citySelect = document.getElementById('city');
                                let retryCount = 0;

                                const checkCities = () => {
                                    // 通过实际城市选项判断是否加载完成
                                    const validOption = Array.from(citySelect.options).find(
                                        opt => opt.text === targetCity && opt.value !== ""
                                    );
                                    if (validOption || retryCount > 10) { // 最多重试10次
                                        resolve();
                                        return;
                                    }
                                    retryCount++;
                                    setTimeout(checkCities, 100); // 每100ms检查一次
                                };

                                provinceSelect.dispatchEvent(new Event('change'));
                                checkCities();
                            });

                            // 3. 精确选择城市
                            const citySelect = document.getElementById('city');
                            const cityOption = Array.from(citySelect.options).find(
                                opt => opt.text === targetCity && opt.value !== ""
                            );

                            if (cityOption) {
                                citySelect.value = cityOption.value;

                                await new Promise(resolve => {
                                    const observer = new MutationObserver((mutationsList, obs) => {
                                        // 检查所有列表项
                                        const found = Array.from(cityList.children).some(item => {
                                            return item.textContent.includes(`${targetProvince}-${targetCity}`);
                                        });

                                        if (found) {
                                            obs.disconnect();
                                            resolve();
                                        }
                                    });

                                    observer.observe(cityList, {
                                        childList: true,  // 监听子元素变化
                                        subtree: true     // 监听后代元素
                                    });

                                    addCity(); // 触发原有添加函数
                                });
                            }
                        } catch (error) {
                            console.error(`城市处理失败: ${area}`, error);
                        }
                    };

                    // 顺序处理城市（避免并发冲突）
                    for (const area of importedArray[8].split('|')) {
                        await processCity(area);
                    }
                }

                // 第三部分：自定义字段回填（保持原样）
                const customInputs = document.querySelectorAll('.custom-input');
                customInputs.forEach((input, index) => {
                    const valueIndex = 9 + index;
                    if (importedArray[valueIndex] !== undefined) {
                        input.value = importedArray[valueIndex];
                    }
                });

                alert('导入成功！');
            } catch (error) {
                console.error('导入错误:', error);
                alert(`导入失败: ${error.message}`);
            }
        });

        // 创建 "导出配置信息" 按钮
        var exportButton = document.createElement("button");
        exportButton.textContent = "导出配置信息";
        exportButton.classList.add("export-btn"); // 添加类名
        exportButton.style.backgroundColor = "#28a745"; // 按钮背景颜色
        exportButton.style.color = "white"; // 按钮文本颜色
        exportButton.style.border = "none"; // 去掉默认边框
        exportButton.style.padding = "10px 20px"; // 内边距
        exportButton.style.borderRadius = "5px"; // 按钮圆角
        exportButton.style.cursor = "pointer"; // 鼠标悬停时显示为手形

        // 导出按钮点击事件
        exportButton.addEventListener('click', function(event) {
            event.preventDefault();

            // 第一部分：收集所有新字段数据
            const newData = {
                target_count: document.getElementById('target_count').value,
                fill_time_start: document.getElementById('fill_time_start').value,
                fill_time_end: document.getElementById('fill_time_end').value,
                mobile_ratio: document.getElementById('mobile_ratio').value,
                link_ratio: document.getElementById('link_ratio').value,
                wechat_ratio: document.getElementById('wechat_ratio').value,
                fensan_level: document.getElementById('fensan_level').value,
                change_ip: document.querySelector('input[name="change_ip"]:checked').value,
                ip_areas: Array.from(document.querySelectorAll('#city_list .list-group-item')).map(item =>
                                                                                                   item.textContent.replace('删除', '').trim()
                                                                                                  )
            };

            // 第二部分：收集原有.custom-input数据
            const customInputs = Array.from(document.querySelectorAll('.custom-input')).map(input => input.value);

            // 合并成统一数组（新字段在前，旧数据在后）
            const exportArray = [
                newData.target_count,
                newData.fill_time_start,
                newData.fill_time_end,
                newData.mobile_ratio,
                newData.link_ratio,
                newData.wechat_ratio,
                newData.fensan_level,
                newData.change_ip,
                newData.ip_areas.join('|'), // 用竖线分隔城市
                ...customInputs // 原有数据追加在末尾
            ];

            // 复制到剪贴板
            const textarea = document.createElement('textarea');
            textarea.value = JSON.stringify(exportArray);
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            alert('数据已导出到剪切板！');
        });



        // 将按钮添加到按钮容器中
        buttonContainer.appendChild(importButton);
        buttonContainer.appendChild(clearButton);
        buttonContainer.appendChild(exportButton); // 添加导出按钮


        // 将文本框和按钮容器添加到目标容器中
        toptitleDiv.appendChild(title); // 添加标题
        toptitleDiv.appendChild(textArea);
        toptitleDiv.appendChild(buttonContainer);

        const dimensionContainer = $(`
<div class="dimension-section" style="margin-top: 20px;">
  <h3 style="color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">
    维度设置（支持多维度分析）
    <button id="addDimensionBtn" type="button"
            class="layui-btn layui-btn-normal layui-btn-sm"
            style="float: right;">
      <b>+</b> 新增维度
    </button>
  </h3>
  <div id="dimensionList" class="dimension-list"></div>
</div>`);
        $("#toptitle").append(dimensionContainer);

        const style = document.createElement('style');
        style.textContent = `
/* 单选按钮组容器 */
.distribution-select {
  margin: 10px 0;
  display: flex;
  justify-content: center;
  gap: 15px;
  background: #f8f9fa;
  padding: 5px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

/* 单选按钮样式 */
.distribution-select label {
  position: relative;
  padding: 5px 20px;
  border-radius: 6px;
  transition: all 0.3s;
  background: white;
  border: 2px solid #e8e8e8;
  cursor: pointer;
  font-weight: 500;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 选中状态 */
.distribution-select input:checked + label {
  border-color: #1890ff;
  background: #e6f7ff;
  color: #1890ff;
  box-shadow: 0 3px 6px rgba(24,144,255,0.2);
}

/* 选中状态图标 */
.distribution-select input:checked + label::after {
  content: "✓";
  margin-left: 8px;
  font-size: 14px;
  color: #1890ff;
}

/* 悬停效果 */
.distribution-select label:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .distribution-select {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .distribution-select label {
    width: 100%;
    justify-content: center;
  }
}

/* 图标样式 */
.distribution-select .icon {
  width: 20px;
  height: 20px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

/* 选中状态图标 */
.distribution-select input:checked + label .icon {
  background: white;
  color: #1890ff;
  border: 2px solid #1890ff;
}
        /* 新增分布选择器样式 */
.distribution-select {
  margin-bottom: 12px;
  display: flex;
  gap: 15px;
  align-items: center;
}

.distribution-select label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
}

.distribution-select input[type="radio"] {
  margin: 0;
  accent-color: #1890ff;
}

@media (max-width: 768px) {
  .distribution-select {
    flex-direction: column;
    align-items: flex-start;
  }
}
        /* 按钮优化 */
#addDimensionBtn {
  letter-spacing: 0.5px;
  transition: all 0.3s;
}

#addDimensionBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
  #addDimensionBtn {
    width: 100%;
    float: none !important;
    margin-top: 10px;
    text-align: center;
  }
}

.layui-icon {
  margin-right: 5px;
  font-size: 14px;
}
/* 防止文本选中 */
.dimension-block * {
  user-select: none;
  -webkit-user-select: none;
}

.dimension-name-input {
  user-select: text;
   pointer-events: none; /* 禁止鼠标交互 */
  background-color: #f5f5f5; /* 添加灰色背景表示不可编辑 */
  cursor: not-allowed; /* 显示不可用光标 */
}

/* 容器样式 */
.dimension-section {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dimension-block {
  background: #fff;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.05);
  transition: box-shadow 0.2s;
}

.dimension-block:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dimension-header {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  align-items: center;
}

.dimension-name-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.dimension-name-input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

.delete-dimension-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 15px;
  cursor: pointer;
  transition: opacity 0.3s;
}

.delete-dimension-btn:hover {
  opacity: 0.85;
}

.selection-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 16px;
  align-items: stretch;
}

@media (max-width: 768px) {
  .selection-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
}

.question-list {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  min-height: 180px;
  max-height: 320px;
  overflow: auto;
  padding: 8px;
}

.question-item {
  padding: 10px 12px;
  margin: 4px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fafafa;
  position: relative;
  font-size: 13px;
}

.question-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 90%;
  height: 1px;
  background: #f0f0f0;
  transform: translateX(-50%);
}

.question-item.selected {
  background: #e6f7ff !important;
  border: 1px solid #91d5ff;
  transform: translateX(3px);
}

.question-item.disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

.button-group {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  min-width: 120px;
  padding: 0 8px;
}

.move-button {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background: #1890ff;
  color: white;
  font-size: 13px;
}

.move-button.remove {
  background: #ff4d4f;
}

.move-button.reset {
  background: #faad14;
  margin-top: 12px;
}

.move-button:hover {
  opacity: 0.9;
  transform: scale(0.98);
}


 @media (max-width: 768px) {
        .dimension-header {
            flex-direction: column;
            gap: 8px;
        }
        .dimension-name-input {
            width: 100%;
            box-sizing: border-box;
        }
        .delete-dimension-btn {
            width: 100%;
        }
        .button-group {
            flex-direction: row !important;
            flex-wrap: wrap;
            gap: 5px;
            padding: 8px 0;
        }
        .move-button {
            flex: 1 1 45%;
            padding: 8px;
            font-size: 12px;
        }
    }
`;
        document.head.appendChild(style);

        class DimensionManager {
            constructor() {
                this.maxDimensions = 10; // 新增最大维度数配置
                this.dimensions = [];
                this.allSelectedQuestions = new Set();
                this.init();
            }
            init() {
                this.questions = this.getQuestionList();
                this.setupGlobalEvents();
            }

            getQuestionList() {
                // 获取所有题目容器（包括后续会被过滤的）
                const allFields = Array.from(
                    document.querySelectorAll('.fieldset > .field.ui-field-contain')
                );

                // 过滤掉不符合条件的题目，同时保留原始索引
                return allFields
                    .filter((field, index) => {
                    const type = field.getAttribute('type');

                    // 如果 type 为 1、2、7，直接排除
                    if (['1', '2', '4','7','11','12','13'].includes(type)) return false;

                    // 如果 type 为 6，根据 checkMatrixRating 判断是否排除
                    if (type === '6') {
                        const matrixType = checkMatrixRating(lists, index);
                        return matrixType; // 仅保留 matrixType 为 'single' 的题目
                    }
                    if (type == 9||type == 12) {
                        if (lists.eq(index).find('.rangeslider').length === 0) {
                            return false;
                        }
                    }

                    // 其他情况保留
                    return true;
                })
                    .map(field => {
                    // 获取题目在原始DOM中的位置（从0开始）
                    const originalIndex = allFields.indexOf(field);
                    // 题号 = 原始位置 + 1
                    const originalNumber = originalIndex + 1;

                    // 提取题目文本
                    const topicHtml = field.querySelector('.field-label .topichtml');
                    return {
                        number: originalNumber, // 使用原始题号
                        text: topicHtml ?
                        topicHtml.textContent.replace(/第\d+题/, '').trim() :
                        '未知题目'
                    };
                });
            }

            // 新增打印方法
            printDimensions() {
                console.log('====== 当前维度配置 ======');
                this.dimensions.forEach((dimension, index) => {
                    console.log(`维度 ${index + 1}：${dimension.name}`);
                    console.log('包含题目：');
                    dimension.questions.forEach(num => {
                        const question = this.questions.find(q => q.number === num);
                        console.log(`  ${num}. ${question?.text || '未知题目'}`);
                    });
                    console.log('-----------------------');
                });
            }

            setupGlobalEvents() {
                // 新增维度
                document.getElementById('addDimensionBtn').addEventListener('click', () => {
                    if (this.dimensions.length >= this.maxDimensions) {
                        layer.msg(`最多支持${this.maxDimensions}个维度`, {icon: 3});
                        return;
                    }

                    const dimensionNumber = this.dimensions.length + 1;
                    const newDimension = {
                        id: Date.now(),
                        name: `维度${dimensionNumber}-偏右`,
                        distribution: '偏右',
                        questions: []
                    };
                    this.dimensions.push(newDimension);
                    this.createDimensionBlock(newDimension);
                    this.updateAllDimensions(); // 新增关键更新

                    this.updateCustomInputs(); // 新增
                });

                // 全局事件处理
                document.getElementById('dimensionList').addEventListener('click', e => {
                    const dimensionBlock = e.target.closest('.dimension-block');
                    if (!dimensionBlock) return;

                    // 删除维度
                    if (e.target.classList.contains('delete-dimension-btn')) {
                        const dimensionId = parseInt(dimensionBlock.dataset.id);
                        this.removeDimension(dimensionId);
                        dimensionBlock.remove();
                    }

                    // 按钮操作
                    if (e.target.classList.contains('move-button')) {
                        const action = e.target.classList.contains('add-btn') ? 'add' :
                        e.target.classList.contains('remove') ? 'remove' :
                        'reset';
                        this.handleDimensionAction(dimensionBlock, action);
                    }
                });
            }

            createDimensionBlock(dimension) {
                const block = document.createElement('div');
                block.className = 'dimension-block';
                block.dataset.id = dimension.id;
                block.innerHTML = `
<div class="dimension-header">
  <input type="text" class="dimension-name-input" value="${dimension.name}">
  <button type="button" class="delete-dimension-btn">删除维度</button>
</div>
<div class="distribution-select">
  <label>
    <input type="radio" name="distribution-${dimension.id}" value="偏左"
           ${dimension.distribution === '偏左' ? 'checked' : ''}> 偏左
  </label>

  <label>
    <input type="radio" name="distribution-${dimension.id}" value="偏右"
           ${dimension.distribution === '偏右' ? 'checked' : ''}> 偏右
  </label>
</div>
<div class="selection-container">
  <div class="available-list question-list"></div>
  <div class="button-group">
    <button type="button" class="move-button add-btn">加入 →</button>
    <button type="button" class="move-button remove">← 移除</button>
    <button type="button" class="move-button reset">重置</button>
  </div>
  <div class="selected-list question-list"></div>
</div>`;
                /*
                  <label>
    <input type="radio" name="distribution-${dimension.id}" value="正态"
           ${dimension.distribution === '正态' ? 'checked' : ''}> 正态
  </label>
                */

                this.initDimensionBlock(block, dimension);
                document.getElementById('dimensionList').appendChild(block);
            }

            initDimensionBlock(block, dimension) {
                // 初始化列表
                const availableList = block.querySelector('.available-list');
                const selectedList = block.querySelector('.selected-list');


                this.renderQuestionList(availableList, this.questions);
                this.renderQuestionList(selectedList, []);

                // 绑定事件
                this.attachListEvents(availableList);
                this.attachListEvents(selectedList);

                // 新增单选按钮事件监听
                const radios = block.querySelectorAll('input[type="radio"]');
                radios.forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        if (e.target.checked) {
                            // 更新分布类型
                            dimension.distribution = e.target.value;
                            // 更新维度名称
                            const baseName = dimension.name.replace(/-偏左$|-正态$|-偏右$/, '');
                            dimension.name = `${baseName}-${dimension.distribution}`;
                            // 同步到输入框
                            block.querySelector('.dimension-name-input').value = dimension.name;

                            layer.msg(baseName+'已设置'+dimension.distribution, {icon: 1});

                            this.updateCustomInputs(); // 新增
                        }
                    });
                });


                // 维度名称更新
                // 原有输入框监听
                block.querySelector('.dimension-name-input').addEventListener('input', e => {
                    // 保留基础名称部分
                    const newName = e.target.value.replace(/-偏左$|-正态$|-偏右$/, '');
                    dimension.name = `${newName}-${dimension.distribution}`;
                    e.target.value = dimension.name; // 强制显示完整名称
                });
                // 立即更新列表
                this.updateSingleDimension(block);

                this.updateCustomInputs(); // 新增
            }

            attachListEvents(list) {
                let isSelecting = false;
                let startIndex = -1;

                const getItemIndex = (clientX, clientY) => {
                    return Array.from(list.children).findIndex(item => {
                        const rect = item.getBoundingClientRect();
                        return clientX >= rect.left &&
                            clientX <= rect.right &&
                            clientY >= rect.top &&
                            clientY <= rect.bottom;
                    });
                };

                const handleStart = (clientX, clientY) => {
                    const index = getItemIndex(clientX, clientY);
                    if (index > -1) {
                        isSelecting = true;
                        startIndex = index;
                        this.toggleSelect(list.children[index]);
                    }
                };

                const handleMove = (clientX, clientY) => {
                    if (!isSelecting) return;
                    const currentIndex = getItemIndex(clientX, clientY);
                    if (currentIndex > -1) {
                        const [start, end] = [Math.min(startIndex, currentIndex), Math.max(startIndex, currentIndex)];
                        Array.from(list.children).forEach((item, index) => {
                            item.classList.toggle('selected', index >= start && index <= end);
                        });
                    }
                };

                // 鼠标事件
                list.addEventListener('mousedown', e => {
                    if (e.button !== 0) return;
                    handleStart(e.clientX, e.clientY);
                });

                list.addEventListener('mousemove', e => {
                    handleMove(e.clientX, e.clientY);
                });

                list.addEventListener('mouseup', () => {
                    isSelecting = false;
                });

                // 触摸事件
                list.addEventListener('touchstart', e => {
                    handleStart(e.touches[0].clientX, e.touches[0].clientY);
                }, { passive: true });

                list.addEventListener('touchmove', e => {
                    handleMove(e.touches[0].clientX, e.touches[0].clientY);
                }, { passive: true });

                list.addEventListener('touchend', () => {
                    isSelecting = false;
                });
            }

            toggleSelect(item) {
                if (!item || item.classList.contains('disabled')) return;
                item.classList.toggle('selected');
            }

            // 优化后的操作处理方法
            handleDimensionAction(block, action) {
                const dimension = this.dimensions.find(d => d.id === parseInt(block.dataset.id));
                const sourceList = action === 'add' ? block.querySelector('.available-list') : block.querySelector('.selected-list');
                const selectedItems = Array.from(sourceList.querySelectorAll('.selected'));
                const selectedNumbers = selectedItems.map(item => parseInt(item.dataset.number));

                // 更新数据状态
                switch(action) {
                    case 'add':
                        dimension.questions = [...new Set([...dimension.questions, ...selectedNumbers])];
                        selectedNumbers.forEach(n => this.allSelectedQuestions.add(n));
                        break;

                    case 'remove':
                        dimension.questions = dimension.questions.filter(n => !selectedNumbers.includes(n));
                        selectedNumbers.forEach(n => {
                            if (!this.isQuestionUsedElsewhere(n, dimension.id)) {
                                this.allSelectedQuestions.delete(n);
                            }
                        });
                        break;

                    case 'reset':
                        // 1. 先保存要移除的题目编号
                        const removedNumbers = [...dimension.questions];

                        // 2. 清空当前维度
                        dimension.questions = [];

                        // 3. 更新全局状态
                        removedNumbers.forEach(number => {
                            if (!this.isQuestionUsedElsewhere(number, dimension.id)) {
                                this.allSelectedQuestions.delete(number);
                            }
                        });
                        break;
                }

                // 局部更新当前维度块（代替全局重绘）
                this.updateAllDimensions();  // 替换原来的局部更新
                this.updateAllSelectors();
                // 在更新后打印
                //this.printDimensions();

                this.updateCustomInputs(); // 新增
            }

            // 新增：更新单个维度块
            updateDimensionBlock(block) {
                const dimension = this.dimensions.find(d => d.id === parseInt(block.dataset.id));
                const availableList = block.querySelector('.available-list');
                const selectedList = block.querySelector('.selected-list');

                // 左侧列表：显示所有未被任何维度使用的题目
                const availableQuestions = this.questions.filter(q =>
                                                                 !this.allSelectedQuestions.has(q.number)
                                                                );

                // 右侧列表：显示当前维度选中的题目
                const selectedQuestions = dimension.questions.map(n =>
                                                                  this.questions.find(q => q.number === n)
                                                                 );

                this.renderQuestionList(availableList, availableQuestions);
                this.renderQuestionList(selectedList, selectedQuestions, true);

                // 确保其他维度块同步更新
                this.updateOtherDimensions(block);
            }
            // 新增：更新其他维度块
            updateOtherDimensions(currentBlock) {
                const currentId = parseInt(currentBlock.dataset.id);
                document.querySelectorAll('.dimension-block').forEach(block => {
                    if (parseInt(block.dataset.id) !== currentId) {
                        this.updateSingleDimension(block);
                    }
                });
            }
            // 新增：单个维度块更新
            updateSingleDimension(block) {
                const dimension = this.dimensions.find(d => d.id === parseInt(block.dataset.id));
                const allUsed = new Set([...this.allSelectedQuestions]);

                const availableQuestions = this.questions.filter(q => !allUsed.has(q.number));
                const selectedQuestions = dimension.questions.map(n =>
                                                                  this.questions.find(q => q.number === n)
                                                                 );

                this.renderQuestionList(block.querySelector('.available-list'), availableQuestions);
                this.renderQuestionList(block.querySelector('.selected-list'), selectedQuestions, true);
            }
            // 新增辅助方法：检查问题是否被其他维度使用
            isQuestionUsedElsewhere(questionNumber, currentDimensionId) {
                return this.dimensions.some(d =>
                                            d.id !== currentDimensionId && d.questions.includes(questionNumber)
                                           );
            }

            getCurrentQuestions(list) {
                return Array.from(list.children).map(item => ({
                    number: parseInt(item.dataset.number),
                    text: item.textContent.split(': ')[1]
                }));
            }

            // 改进后的列表渲染方法
            renderQuestionList(container, questions, isSelectedList = false) {
                container.innerHTML = '';
                questions.sort((a, b) => a.number - b.number).forEach(q => {
                    const item = document.createElement('div');
                    item.className = `question-item`;
                    item.textContent = `第${q.number}题: ${q.text}`;
                    item.dataset.number = q.number;

                    // 移除disabled状态，改为直接过滤
                    if (isSelectedList) {
                        item.classList.add('selected'); // 右侧默认选中
                    }

                    container.appendChild(item);
                });
            }

            // 优化后的全局状态更新
            updateAllSelectors() {
                // 当前只需要清除选中状态
                document.querySelectorAll('.question-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });
            }
            // 新增方法：更新所有维度的名称
            updateDimensionNames() {
                this.dimensions.forEach((dimension, index) => {
                    const newName = `维度${index + 1}-${dimension.distribution}`;
                    dimension.name = newName;

                    // 更新DOM中的显示
                    const block = document.querySelector(`.dimension-block[data-id="${dimension.id}"]`);
                    if (block) {
                        const input = block.querySelector('.dimension-name-input');
                        input.value = newName;
                    }
                });
            }
            removeDimension(id) {
                // 过滤掉被删除的维度
                this.dimensions = this.dimensions.filter(d => d.id !== id);

                // 重新构建全局已选题目集合
                this.allSelectedQuestions = new Set();
                this.dimensions.forEach(d => {
                    d.questions.forEach(q => this.allSelectedQuestions.add(q));
                });

                // 更新所有维度块的显示
                this.updateAllDimensions();
                this.updateDimensionNames();
                this.updateAllSelectors();
                this.updateCustomInputs(); // 新增
            }
            // 新增：更新所有维度块
            updateAllDimensions() {
                // 获取所有题目的最新使用状态
                const allUsed = new Set();
                this.dimensions.forEach(d => d.questions.forEach(n => allUsed.add(n)));

                // 更新所有维度块
                this.dimensions.forEach(dimension => {
                    const block = document.querySelector(`.dimension-block[data-id="${dimension.id}"]`);
                    if (!block) return;

                    const availableList = block.querySelector('.available-list');
                    const selectedList = block.querySelector('.selected-list');

                    // 左侧列表显示未在任何维度使用的题目
                    const availableQuestions = this.questions.filter(q => !allUsed.has(q.number));

                    // 右侧列表显示当前维度选中的题目
                    const selectedQuestions = dimension.questions.map(n =>
                                                                      this.questions.find(q => q.number === n)
                                                                     );

                    this.renderQuestionList(availableList, availableQuestions);
                    this.renderQuestionList(selectedList, selectedQuestions, true);
                });
            }
            // 新增重绘所有维度方法
            redrawAllDimensions() {
                const dimensionList = document.getElementById('dimensionList');
                dimensionList.innerHTML = '';
                this.dimensions.forEach(dimension => {
                    this.createDimensionBlock(dimension);
                    this.updateAllDimensions();
                });
            }
            updateCustomInputs() {
                // 创建题号到维度名称的映射
                const questionDimensionMap = new Map();
                this.dimensions.forEach(dimension => {
                    dimension.questions.forEach(questionNumber => {
                        questionDimensionMap.set(questionNumber, dimension.name);
                    });
                });

                // 更新所有题目的自定义输入框（仅修改属于维度的题目）
                const allFields = document.querySelectorAll('.fieldset > .field.ui-field-contain');
                allFields.forEach((field, index) => {
                    const questionNumber = index + 1;
                    const customInputs = field.querySelectorAll('.custom-input:not(.layui-textarea)');

                    // 仅当题目属于某个维度时才更新
                    if (questionDimensionMap.has(questionNumber)) {
                        const dimensionName = questionDimensionMap.get(questionNumber);
                        customInputs.forEach(input => {
                            input.value = dimensionName; // 属于维度：设置维度名称
                        });
                    }
                    // 否则：不修改输入框的现有值
                });
            }


        }

        // 初始化
        new DimensionManager();



        oneRandom_btn.on('click', function(e) {
            $(".field.ui-field-contain").each(function(index, element) {
                $(element).find(".oneRandom1").click();
                $(element).find(".oneRandom_advice").click();
            });
            layer.msg('一键平均比例答案！', {icon: 1});
        })

        oneRandom_btn2.on('click', function(e) {
            $(".field.ui-field-contain").each(function(index, element) {
                $(element).find(".oneRandom2").click();
                $(element).find(".oneRandom_advice").click();
            });
            layer.msg('一键随机比例答案！', {icon: 1});
        })
        jiancha_btn.on('click', function(e) {
            /*
            $(".field.ui-field-contain").each(function(index, element) {
                $(element).find(".jiancha").click();
            });
            */
            checkAllProportions();

        })




        jiaocheng__btn.on('click', function(e) {
            window.open('https://www.yuque.com/yangyang-bnict/axszk1/sbrsk1lgn4eodeow');
            //generateJS();
        })

        // 放置搜索、复制按钮
        for (var i=0; i<lists.length; i++) {
            (function(index) {
                const  test_btn = $(`<button type="button" class="layui-btn layui-btn-xs" id="test_btn_${index + 1}" style="margin-left:1rem;">测试alert</button>`);
                const  random_bili_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom2" id="random_bili_btn_${index + 1}" style="margin-left:1rem;">生成随机比例</button>`);
                const  average_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom1" id="average_btn_${index + 1}" style="margin-left:1rem;">生成平均比例</button>`);
                const  jiancha_btn = $(`<button type="button" class="layui-btn layui-btn-xs layui-btn-normal jiancha" id="jiancha_btn_${index + 1}" style="margin-left:1rem;">检查比例</button>`);
                const  fill0_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom1" id="fill0_btn_${index + 1}" style="margin-left:1rem;">将其余选项比例补0</button>`);
                const  clear_btn = $(`<button type="button" class="layui-btn layui-btn-xs layui-btn-danger clear" id="clear_btn_${index + 1}" style="margin-left:1rem;">清空比例答案</button>`);

                const  info_label = $(`<label class="layui-input-block">info: ${lists.eq(index).attr('type')}</label>`);
                const  input_text = $('<textarea type="text" class="layui-input custom-input input-limit" style="width:100%;  height:150px;" placeholder="请在这里输入随机文本内容，如有多个文本随机请用英文逗号隔开,例如：答案1,答案2,答案3">');
                let questionType = lists.eq(index).attr('type')
                /*
                var data_json = {
                    "1": {"type":"1","data":"喜羊羊,美羊羊,沸羊羊"},//填空题
                    "2": {"type":"3","data":"50,50"},//单选题
                    "3": {"type":"4","data":"50,50,100,80"},//多选题
                    "4": {"type":"6single","data":{"1":"20,20,20,20,20","2":"20,20,20,20,20"}},//矩阵单选
                    "5": {"type":"6multiple","data":{"1":"50,50,50,50,50","2":"50,50,50,50,50"}},//矩阵多选
                    "6": {"type":"7","data":"25,25,25,25"},//下拉框题
                    "7": {"type":"11","data":"80,20,50,60"},//排序题
                    "8": {"type":"8","data":"0,20,20,20,20,20"},//滑条题
                    "9": {"type":"5","data":"0,20,20,20,20,20"},//NPS量标题
                    "10": {"type":"5","data":"0,20,20,20,20,20"},//评价题
                    "11": {"type":"12","data":{"1":"20,20,20,20,20","2":"20,20,20,20,20"}},//矩阵滑条题，type9和12都是
                };
*/
                test_btn.on('click', function(e) {
                    if (questionType == 1 ||questionType == 2) {  // 填空题
                        let inputValue = lists.eq(index).find('.custom-input:not(.layui-textarea)').val();
                        alert(`填空题输入内容：${inputValue}`);
                    } else if (questionType == 3) {  // 单选题
                        let output = "["; // 存储所有输出内容的字符串
                        let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                        options.each(function(optionIndex, optionElement) {
                            let inputValue = $(optionElement).find('.custom-input:not(.layui-textarea)').val(); // 获取输入框的值
                            output += (optionIndex==options.length-1?inputValue:inputValue+',');
                        });
                        output += ']';
                        alert(output);
                    } else if (questionType == 4) {  // 多选题
                        let output = "["; // 存储所有输出内容的字符串
                        let options = lists.eq(index).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                        options.each(function(optionIndex, optionElement) {
                            let inputValue = $(optionElement).find('.custom-input:not(.layui-textarea)').val(); // 获取输入框的值
                            output += (optionIndex==options.length-1?inputValue:inputValue+',');
                        });
                        output += ']';
                        alert(output);
                    } else if (questionType == 6) {  // 处理矩阵量标题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let output = "["; // 存储所有输出内容的字符串
                        inputs.each(function(inputIndex, inputElement) {
                            let inputValue = $(inputElement).val();
                            output += (inputIndex == inputs.length - 1 ? inputValue : inputValue + ',');
                        });
                        output += ']';
                        alert(`下拉框题型输入比例：${output}`);
                    } else if (questionType == 7) {  // 处理下拉框题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let output = "["; // 存储所有输出内容的字符串
                        inputs.each(function(inputIndex, inputElement) {
                            let inputValue = $(inputElement).val();
                            output += (inputIndex == inputs.length - 1 ? inputValue : inputValue + ',');
                        });
                        output += ']';
                        alert(`排序题型输入比例：${output}`);
                    } else if (questionType == 11) {  // 处理排序题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let output = "["; // 存储所有输出内容的字符串
                        inputs.each(function(inputIndex, inputElement) {
                            let inputValue = $(inputElement).val();
                            output += (inputIndex == inputs.length - 1 ? inputValue : inputValue + ',');
                        });
                        output += ']';
                        alert(`矩阵滑条题型输入比例：${output}`);
                    } else if (questionType == 12) {  // 处理矩阵滑条题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let output = "["; // 存储所有输出内容的字符串
                        inputs.each(function(inputIndex, inputElement) {
                            let inputValue = $(inputElement).val();
                            output += (inputIndex == inputs.length - 1 ? inputValue : inputValue + ',');
                        });
                        output += ']';
                        alert(`矩阵滑条题型输入比例：${output}`);
                    } else if (questionType == 8) {  // 处理滑条题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let output = "["; // 存储所有输出内容的字符串
                        inputs.each(function(inputIndex, inputElement) {
                            let inputValue = $(inputElement).val();
                            output += (inputIndex == inputs.length - 1 ? inputValue : inputValue + ',');
                        });
                        output += ']';
                        alert(`滑条题型输入比例：${output}`);
                    } else if (questionType == 9) {  // 处理矩阵滑条题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let output = "["; // 存储所有输出内容的字符串
                        inputs.each(function(inputIndex, inputElement) {
                            let inputValue = $(inputElement).val();
                            output += (inputIndex == inputs.length - 1 ? inputValue : inputValue + ',');
                        });
                        output += ']';
                        alert(`NPS量标题题型输入比例：${output}`);
                    } else if (questionType == 5) {  // 处理单选量标题题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let output = "["; // 存储所有输出内容的字符串
                        inputs.each(function(inputIndex, inputElement) {
                            let inputValue = $(inputElement).val();
                            output += (inputIndex == inputs.length - 1 ? inputValue : inputValue + ',');
                        });
                        output += ']';
                        alert(`单选量标题题型输入比例：${output}`);
                    } else {
                        alert(`未处理第${i+1}题，暂不支持该题型，请不要付款，可以找群主定制！`);
                    }
                });

                random_bili_btn.on('click', function(e) {
                    if (questionType == 3) {  // 单选题
                        let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                        let totalProportion = 100;
                        let generatedProportions = generateProportions(options.length, totalProportion);

                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            inputElement.val(generatedProportions[optionIndex]);
                        });

                    } else if (questionType == 4) {  // 多选题
                        let options = lists.eq(index).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            let randomProportion = getRandomMultipleOfTen(10, 100);
                            inputElement.val(randomProportion);
                        });
                    } else if(questionType == 6) {  // 处理矩阵量标题型
                        let matrixType = checkMatrixRating(lists, index) ? 'single' : 'multiple';
                        let rows = lists.eq(index).find('.matrixtable tbody tr:not(.trlabel)');

                        rows.each(function(rowIdx, rowElement) {
                            let options = $(rowElement).find('input.custom-input');
                            //single型
                            if (matrixType === 'single') {
                                let totalProportion = 100;
                                let generatedProportions = generateProportions(options.length, totalProportion);

                                options.each(function(optionIndex, inputElement) {
                                    $(inputElement).val(generatedProportions[optionIndex]);
                                });
                            } else if (matrixType === 'multiple') {  //multiple
                                options.each(function(optionIndex, inputElement) {
                                    let randomProportion = getRandomMultipleOfTen(10, 100);
                                    $(inputElement).val(randomProportion);
                                });
                            }
                        });

                    } else if (questionType == 7) {  // 处理下拉框题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let proportions = generateProportions(inputs.length, 100);
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val(proportions[inputIndex]);
                        });
                    } else if (questionType == 11) {  // 处理排序题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let proportions = generateProportions(inputs.length, 100);
                        inputs.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement);
                            let randomProportion = getRandomMultipleOfTen(10, 90);
                            inputElement.val(randomProportion);
                        });
                    } else if (questionType == 12||questionType == 9) {  // 处理矩阵滑条题型
                        let rows = lists.eq(index).find('.table-row');

                        rows.each(function(rowIdx, rowElement) {
                            let inputCells = $(rowElement).find('.input-cell input.custom-input');
                            let totalProportion = 100;
                            let generatedProportions = generateProportions(inputCells.length, totalProportion);

                            inputCells.each(function(cellIdx, inputElement) {
                                $(inputElement).val(generatedProportions[cellIdx]);
                            });
                        });
                    } else if (questionType == 8) {  // 处理滑条题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let proportions = generateProportions(inputs.length, 100);
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val(proportions[inputIndex]);
                        });
                    } else if (questionType == 5) {  // 处理单选量标题题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let proportions = generateProportions(inputs.length, 100);
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val(proportions[inputIndex]);
                        });
                    } else {
                        alert(`未处理第${index+1}题，暂不支持该题型，请不要付款，可以找群主定制！`);
                    }
                    layer.msg('本题已生成随机比例', {icon: 1});
                });

                fill0_btn.on('click', function(e) {
                    if (questionType == 3) {  // 单选题
                        let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            if(!inputElement.val()){
                                inputElement.val(0);
                            }
                        });

                    } else if (questionType == 4) {  // 多选题
                        let options = lists.eq(index).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            if(!inputElement.val()){
                                inputElement.val(0);
                            }
                        });
                    } else if(questionType == 6) {  // 处理矩阵量标题型
                        let matrixType = checkMatrixRating(lists, index) ? 'single' : 'multiple';
                        let rows = lists.eq(index).find('.matrixtable tbody tr:not(.trlabel)');

                        rows.each(function(rowIdx, rowElement) {
                            let options = $(rowElement).find('input.custom-input');
                            //single型
                            if (matrixType === 'single') {
                                options.each(function(optionIndex, inputElement) {
                                    if(!$(inputElement).val()){
                                        $(inputElement).val(0);
                                    }
                                });
                            } else if (matrixType === 'multiple') {  //multiple
                                options.each(function(optionIndex, inputElement) {
                                    if(!$(inputElement).val()){
                                        $(inputElement).val(0);
                                    }
                                });
                            }
                        });

                    } else if (questionType == 7) {  // 处理下拉框题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        inputs.each(function(inputIndex, inputElement) {
                            if(!$(inputElement).val()){
                                $(inputElement).val(0);
                            }
                        });
                    } else if (questionType == 11) {  // 处理排序题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let proportions = generateProportions(inputs.length, 100);
                        inputs.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement);
                            if(!inputElement.val()){
                                inputElement.val(0);
                            }

                        });
                    } else if (questionType == 12||questionType == 9) {  // 处理矩阵滑条题型
                        let rows = lists.eq(index).find('.table-row');

                        rows.each(function(rowIdx, rowElement) {
                            let inputCells = $(rowElement).find('.input-cell input.custom-input');
                            inputCells.each(function(cellIdx, inputElement) {
                                if(!$(inputElement).val()){
                                    $(inputElement).val(0);
                                }
                            });
                        });
                    } else if (questionType == 8) {  // 处理滑条题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        inputs.each(function(inputIndex, inputElement) {
                            if(!$(inputElement).val()){
                                $(inputElement).val(0);
                            }
                        });
                    } else if (questionType == 5) {  // 处理单选量标题题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        inputs.each(function(inputIndex, inputElement) {
                            if(!$(inputElement).val()){
                                $(inputElement).val(0);
                            }
                        });
                    } else {
                        alert(`未处理第${index+1}题，暂不支持该题型，请不要付款，可以找群主定制！`);
                    }
                    layer.msg('已将本题将其余选项比例补0', {icon: 1});
                });

                clear_btn.on('click', function(e) {
                    if(questionType==1 || questionType == 2){
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        inputs.val('');
                        // 移除只读属性
                        input_text.prop('readonly', false);
                        // 恢复背景颜色
                        input_text.css({
                            'background-color': '',
                            'cursor': ''
                        });
                        // 移除之前绑定的事件
                        input_text.off('focus');
                        layer.msg('已清空本题答案', {icon: 1});
                    }
                    else if (questionType == 3) {  // 单选题
                        let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            $(inputElement).val('');
                        });
                        layer.msg('已清空本题答案', {icon: 1});
                    } else if (questionType == 4) {  // 多选题
                        let options = lists.eq(index).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            $(inputElement).val('');
                        });
                        layer.msg('已清空本题答案', {icon: 1});
                    } else if(questionType == 6) {  // 处理矩阵量标题型
                        let matrixType = checkMatrixRating(lists, index) ? 'single' : 'multiple';
                        let rows = lists.eq(index).find('.matrixtable tbody tr:not(.trlabel)');

                        rows.each(function(rowIdx, rowElement) {
                            let options = $(rowElement).find('input.custom-input');
                            //single型
                            if (matrixType === 'single') {
                                options.each(function(optionIndex, inputElement) {
                                    $(inputElement).val('');
                                });
                            } else if (matrixType === 'multiple') {  //multiple
                                options.each(function(optionIndex, inputElement) {
                                    $(inputElement).val('');
                                });
                            }
                        });
                        layer.msg('已清空本题答案', {icon: 1});

                    } else if (questionType == 7) {  // 处理下拉框题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val('');
                        });
                        layer.msg('已清空本题答案', {icon: 1});
                    } else if (questionType == 11) {  // 处理排序题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let proportions = generateProportions(inputs.length, 100);
                        inputs.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement);
                            $(inputElement).val('');

                        });
                        layer.msg('已清空本题答案', {icon: 1});
                    } else if (questionType == 12||questionType == 9) {  // 处理矩阵滑条题型
                        let rows = lists.eq(index).find('.table-row');

                        rows.each(function(rowIdx, rowElement) {
                            let inputCells = $(rowElement).find('.input-cell input.custom-input');
                            inputCells.each(function(cellIdx, inputElement) {
                                $(inputElement).val('');
                            });
                        });
                        layer.msg('已清空本题答案', {icon: 1});
                    } else if (questionType == 8) {  // 处理滑条题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val('');
                        });
                        layer.msg('已清空本题答案', {icon: 1});
                    } else if (questionType == 5) {  // 处理单选量标题题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val('');
                        });
                        layer.msg('已清空本题答案', {icon: 1});
                    } else {
                        alert(`未处理第${index+1}题，暂不支持该题型，请不要付款，可以找群主定制！`);
                    }

                });

                average_btn.on('click', function(e) {
                    if (questionType == 3) {  // 单选题
                        let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                        let totalProportion = 100;
                        let generatedProportions
                        generatedProportions = generateEqualProportions(options.length, totalProportion);
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            inputElement.val(generatedProportions[optionIndex]);
                        });

                    } else if (questionType == 4) {  // 多选题
                        let options = lists.eq(index).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            inputElement.val(50);
                        });
                    } else if(questionType == 6) {  // 处理矩阵量标题型
                        let matrixType =  checkMatrixRating(lists, index) ? 'single' : 'multiple';
                        let rows = lists.eq(index).find('.matrixtable tbody tr:not(.trlabel)');

                        rows.each(function(rowIdx, rowElement) {
                            let options = $(rowElement).find('input.custom-input');
                            //single型
                            if (matrixType === 'single') {
                                let totalProportion = 100;
                                let generatedProportions
                                generatedProportions = generateEqualProportions(options.length, totalProportion);

                                options.each(function(optionIndex, inputElement) {
                                    $(inputElement).val(generatedProportions[optionIndex]);
                                });
                            } else if (matrixType === 'multiple') {  //multiple
                                options.each(function(optionIndex, inputElement) {
                                    $(inputElement).val(50);
                                });
                            }
                        });

                    } else if (questionType == 7) {  // 处理下拉框题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let proportions
                        if (inputs.length > 10) {
                            proportions = Array(inputs.length).fill('平均比例');
                        } else {
                            proportions = generateEqualProportions(inputs.length, 100);
                        }
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val(proportions[inputIndex]);
                        });
                    } else if (questionType == 11) {  // 处理排序题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        inputs.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement);
                            inputElement.val(50);
                        });
                    } else if (questionType == 12||questionType == 9) {  // 处理矩阵滑条题型
                        let rows = lists.eq(index).find('.table-row');

                        rows.each(function(rowIdx, rowElement) {
                            let inputCells = $(rowElement).find('.input-cell input.custom-input');
                            let totalProportion = 100;
                            let generatedProportions
                            /*
                            if (inputCells.length > 10) {
                                generatedProportions = Array(inputs.length).fill('平均比例');
                            } else {
                                generatedProportions = generateEqualProportions(inputCells.length, totalProportion);
                            }
                            */
                            generatedProportions = generateEqualProportions(inputCells.length, totalProportion);

                            inputCells.each(function(cellIdx, inputElement) {
                                $(inputElement).val(generatedProportions[cellIdx]);
                            });
                        });
                    } else if (questionType == 8) {  // 处理滑条题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let generatedProportions
                        /*
                        if (inputs.length > 10) {
                            generatedProportions = Array(inputs.length).fill('平均比例');
                        } else {
                            generatedProportions = generateEqualProportions(inputs.length, 100);
                        }*/
                        generatedProportions = generateEqualProportions(inputs.length, 100);
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val(generatedProportions[inputIndex]);
                        });
                    } else if (questionType == 5) {  // 处理单选量标题题型
                        let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                        let generatedProportions
                        /*
                        if (inputs.length > 10) {
                            generatedProportions = Array(inputs.length).fill('平均比例');
                        } else {
                            generatedProportions = generateEqualProportions(inputs.length, 100);
                        }
                        */
                        generatedProportions = generateEqualProportions(inputs.length, 100);
                        inputs.each(function(inputIndex, inputElement) {
                            $(inputElement).val(generatedProportions[inputIndex]);
                        });
                    } else {
                        alert(`未处理第${index+1}题，暂不支持该题型，请不要付款，可以找群主定制！`);
                    }
                    layer.msg('本题已生成平均比例', {icon: 1});
                })

                jiancha_btn.on('click', function(e) {
                    if (questionType == 3) {  // 单选题
                        let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                        let totalProportion = 100;
                        let sum = 0;
                        let flag = true
                        let inputElement = $(options).find('.custom-input:not(.layui-textarea)');
                        let nobitian_flag = lists[index].querySelector('span[class="req"]') === null || lists[index].querySelector('span[class="req"]').innerText !== '*';
                        if(inputElement.val().includes('维度')){
                            layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                        }else if(nobitian_flag && areOptionsEmpty(options)){
                            layer.msg( '第' + (index + 1) + '题不是必填题，可以选择不填', {icon: 1});
                        }else{
                            // 检查并计算输入框的值总和
                            options.each(function(optionIndex, optionElement) {
                                let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                                let value = parseFloat(inputElement.val());

                                sum += value;
                                // 检查值是否在 [0, 100] 范围内
                                if (isNaN(value) || value < 0 || value > 100) {
                                    flag=false
                                }
                            });
                            if(!flag){
                                alert('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字");
                                // layer.msg('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字", {icon: 2});
                            }
                            else{
                                // 检查总和是否为 100
                                if (sum !== totalProportion) {
                                    alert('第'+(index+1)+'题比例填写错误  '+"输入框的值加起来应该是100");
                                    //layer.msg('第'+(index+1)+'题比例填写错误  '+"输入框的值加起来应该是100", {icon: 2});
                                }else{
                                    layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                                    //alert("本题比例填写正确！");
                                }
                            }
                        }
                    } else if (questionType == 4) {  // 多选题
                        let options = lists.eq(index).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                        let sum = 0;
                        let flag = true;
                        let isall_0_flag = true
                        let nobitian_flag = lists[index].querySelector('span[class="req"]') === null || lists[index].querySelector('span[class="req"]').innerText !== '*';
                        let qtypetip = document.querySelector('#div'+(index + 1)+' .qtypetip');
                        let min_options = 1
                        let count_0 = 0;

                        if (qtypetip) {
                            const text = qtypetip.textContent;
                            const match = text.match(/最少.*?(\d+)项/);
                            if (match && match[1]) {
                                min_options = parseInt(match[1]);
                            }
                            let element = document.querySelector('#div' + (index + 1));

                            if (element.hasAttribute('minvalue')) {
                                let minValue = element.getAttribute('minvalue');
                                min_options = parseInt(minValue, 10); // 将 minvalue 转换为整数
                            }
                        }



                        if(nobitian_flag && areOptionsEmpty(options)){
                            layer.msg( '第' + (index + 1) + '题不是必填题，可以选择不填', {icon: 1});
                        }else{
                            // 检查并计算输入框的值总和
                            options.each(function(optionIndex, optionElement) {
                                let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                                let value = parseFloat(inputElement.val());
                                sum += value;
                                if(value ==0){
                                    count_0+=1
                                }
                                // 检查值是否在 [0, 100] 范围内
                                if (isNaN(value) || value < 0 || value > 100) {
                                    flag = false;
                                }else{
                                    if(value!=0){
                                        isall_0_flag = false;
                                    }
                                }

                            });
                            if (!flag) {
                                alert('第' + (index + 1) + '题比例填写错误  ' + "每个输入框请填写0到100之间的数字");
                                // layer.msg('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字", {icon: 2});
                            } else if(isall_0_flag){
                                alert('第' + (index + 1) + '题比例填写错误  ' + "本题是必填题，不能概率全为0");
                            }else if(options.length - count_0 < min_options){
                                alert('第' + (index + 1) + '题比例填写错误  ' + "本题有最少选择限制，请至少设置比例大于0的项数必须大于最少选择限制");
                            }
                            else {
                                layer.msg('第' + (index + 1) + '题比例填写正确！', {icon: 1});
                            }

                        }

                    } else if(questionType == 6) {  // 处理矩阵量标题型
                        let matrixType = checkMatrixRating(lists, index) ? 'single' : 'multiple';
                        let rows = lists.eq(index).find('.matrixtable tbody tr:not(.trlabel)[tp="d"]');

                        let errorFlag = 1; // 用于标记是否出现了错误
                        let inputElement = $(rows).find('input.custom-input');
                        /*
                        if(inputElement.val().includes('维度')){
                            layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                        }
                        */

                        rows.each(function(rowIdx, rowElement) {
                            let options = $(rowElement).find('input.custom-input');
                            if(options.val().includes('维度')){
                                //这一行正确
                            }else{
                                // 检查值总和是否为 100 或者是否在 [0, 100] 范围内
                                let sum = 0;
                                let flag = true;
                                options.each(function(optionIndex, inputElement) {
                                    // console.log($(inputElement).val());
                                    let value = parseFloat($(inputElement).val());
                                    sum += value;
                                    if (isNaN(value) || value < 0 || value > 100) {
                                        flag = false;
                                    }
                                });

                                //console.log("Row " + rowIdx + ", sum: " + sum + ", flag: " + flag);
                                //console.log("sum " + sum );

                                // 如果是 'single' 类型
                                if (matrixType === 'single') {
                                    let totalProportion = 100;
                                    if (!flag || sum !== totalProportion) {
                                        // console.log("Error found in row " + rowIdx);
                                        errorFlag = -1; // 出现错误时设置标志为 true
                                    }
                                }
                                // 如果是 'multiple' 类型
                                else if (matrixType === 'multiple') {
                                    if (!flag) {
                                        //console.log("Error found in row " + rowIdx);
                                        errorFlag = -2; // 出现错误时设置标志为 true
                                    }
                                }

                                // 在每一行检查完后根据标志决定是否弹出错误提示
                                if (errorFlag<0) {
                                    if (errorFlag==-1){
                                        alert('第' + (index + 1) + '题 第'+(rowIdx+1)+'行  比例填写错误  ' + "每个输入框请填写0到100之间的数字且总和为100");
                                    }else if(errorFlag==-2){
                                        alert('第' + (index + 1) + '题 第'+(rowIdx+1)+'行  比例填写错误  ' + "每个输入框请填写0到100之间的数字");
                                    }
                                    return false; // 提前退出 each 循环
                                }
                            }
                        });

                        // 在所有行检查完后根据标志决定是否弹出错误提示
                        if (errorFlag>0) {
                            layer.msg('第' + (index + 1) + '题比例填写正确！', {icon: 1});
                        }

                    } else if (questionType == 7) {  // 处理下拉框题型
                        /*
                                let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                                let proportions
                                if (inputs.length > 10) {
                                    proportions = Array(inputs.length).fill('平均比例');
                                } else {
                                    proportions = generateEqualProportions(inputs.length, 100);
                                }
                                inputs.each(function(inputIndex, inputElement) {
                                    $(inputElement).val(proportions[inputIndex]);
                                });
*/
                        let options =lists.eq(index).find('.custom-input:not(.layui-textarea)').parent();;
                        let totalProportion = 100;
                        let sum = 0;
                        let flag = true,temp_aver_flag=false
                        let inputElement = $(options).find('.custom-input:not(.layui-textarea)');
                        if(inputElement.val().includes('平均比例')){
                            layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                        }else{
                            // 检查并计算输入框的值总和
                            options.each(function(optionIndex, optionElement) {
                                let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                                let value = parseFloat(inputElement.val());
                                sum += value;
                                // 检查值是否在 [0, 100] 范围内
                                if (isNaN(value) || value < 0 || value > 100) {
                                    flag=false
                                }
                            });
                            if(!flag){
                                alert('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字(如果全部填写的是平均比例，请忽略该报错)");
                                // layer.msg('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字", {icon: 2});
                            }
                            else{
                                // 检查总和是否为 100
                                if (sum !== totalProportion&&!temp_aver_flag ) {
                                    alert('第'+(index+1)+'题比例填写错误  '+"输入框的值加起来应该是100");
                                    //layer.msg('第'+(index+1)+'题比例填写错误  '+"输入框的值加起来应该是100", {icon: 2});
                                }else{
                                    layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                                    //alert("本题比例填写正确！");
                                }
                            }
                        }
                    } else if (questionType == 11) {  // 处理排序题型
                        let options = lists.eq(index).find('.custom-input:not(.layui-textarea)').parent();
                        let sum = 0;
                        let flag = true;
                        let has100 = false; // 用于追踪是否已经出现了100的比例
                        let count100=0;

                        // 单选题的处理逻辑
                        let elements = document.querySelectorAll('#div'+(index+1)+' input[type="text"][required="true"]');

                        // 创建一个空数组，用于存储提取的数字
                        let numbers = [];

                        // 遍历每个元素
                        elements.forEach(element => {
                            // 获取元素的id属性值
                            let id = element.id;
                            // 提取id中_后面的数字部分
                            let number = id.split('_')[1];
                            // 将提取的数字字符串转换为整数并添加到数组中
                            numbers.push(parseInt(number));
                        });
                        let qtypetip = document.querySelector('#div'+(index+1)+' .qtypetip');
                        let min_options = -1
                        if (qtypetip) {
                            let element = document.querySelector('#div' + (index+1));
                            if (element.hasAttribute('minvalue')) {
                                let minValue = element.getAttribute('minvalue');
                                min_options = parseInt(minValue, 10); // 将 minvalue 转换为整数
                            }
                        }
                        /*
                        if(numbers.length>0 &&min_options>0&&options.length-numbers.length<min_options ){
                            flag = false;
                            alert('第' + (index + 1) + '题 选项有必填填空题，并且必须选上带有填空题的选项才能满足最少排序选项要求，该平台目前无法满足，请联系管理员定制（加微信：zyy835573228或者qq:751947907）');
                        }else{
                        */
                        // 检查并计算输入框的值总和
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                            let value = parseFloat(inputElement.val());
                            sum += value;

                            // 检查值是否在 [0, 100] 范围内
                            if (isNaN(value) || value < 0 || value > 100) {
                                flag = false;
                            }

                            // 如果值为100，并且之前没有出现过100，则设置 has100 为true
                            if (value === 100 && count100>=1) {
                                has100 = true;
                                flag=false
                            } else if(value === 100){
                                count100+=1
                            }
                        });

                        // 如果出现多个100的比例，给出相应的提醒
                        if (!flag) {
                            if (has100) {
                                alert('第' + (index + 1) + '题比例填写错误，只能出现一个100');
                            } else {
                                alert('第' + (index + 1) + '题比例填写错误  ' + "每个输入框请填写0到100之间的数字");
                            }
                        } else {
                            layer.msg('第' + (index + 1) + '题比例填写正确！', {icon: 1});
                        }
                        /*
                        }
                        */
                    } else if (questionType == 12||questionType == 9) {  // 处理矩阵滑条题型
                        let rows =lists.eq(index).find('.table-row');

                        let errorFlag = false; // 用于标记是否出现了错误
                        let inputElement = $(rows).find('.input-cell input.custom-input');
                        /*
                        if(inputElement.val()==undefined || inputElement.val().includes('维度')){
                            layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                        }
                        */

                        rows.each(function(rowIdx, rowElement) {
                            let options = $(rowElement).find('.input-cell input.custom-input');
                            if(options.val()==undefined || options.val().includes('维度')){
                                //这一行正确
                            }else{
                                // 检查值总和是否为 100 或者是否在 [0, 100] 范围内
                                let sum = 0;
                                let flag = true;
                                options.each(function(optionIndex, inputElement) {
                                    // console.log($(inputElement).val());
                                    let value = parseFloat($(inputElement).val());
                                    sum += value;
                                    if (isNaN(value) || value < 0 || value > 100) {
                                        flag = false;
                                    }
                                });
                                if (!flag || sum !== 100) {
                                    // console.log("Error found in row " + rowIdx);
                                    errorFlag = true; // 出现错误时设置标志为 true
                                }
                                // 在每一行检查完后根据标志决定是否弹出错误提示
                                if (errorFlag) {
                                    alert('第' + (index + 1) + '题 第'+(rowIdx+1)+'行  比例填写错误  ' + "每个输入框请填写0到100之间的数字且总和为100");
                                    return false; // 提前退出 each 循环
                                }
                            }
                        });

                        // 在所有行检查完后根据标志决定是否弹出错误提示
                        if (!errorFlag) {
                            layer.msg('第' + (index + 1) + '题比例填写正确！', {icon: 1});
                        }


                    } else if (questionType == 8) {  // 处理滑条题型
                        /*
                                let inputs = lists.eq(index).find('.custom-input:not(.layui-textarea)');
                                let generatedProportions
                                generatedProportions = generateEqualProportions(inputs.length, 100);
                                inputs.each(function(inputIndex, inputElement) {
                                    $(inputElement).val(generatedProportions[inputIndex]);
                                });
*/
                        let options = lists.eq(index).find('.custom-input:not(.layui-textarea)').parent();
                        let totalProportion = 100;
                        let sum = 0;
                        let flag = true
                        let inputElement = $(options).find('.custom-input:not(.layui-textarea)');
                        if(inputElement.val().includes('维度')){
                            layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                        }else{

                            // 检查并计算输入框的值总和
                            options.each(function(optionIndex, optionElement) {
                                let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                                let value = parseFloat(inputElement.val());
                                sum += value;
                                // 检查值是否在 [0, 100] 范围内
                                if (isNaN(value) || value < 0 || value > 100) {
                                    flag=false

                                }
                            });
                            if(!flag){
                                alert('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字");
                                // layer.msg('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字", {icon: 2});
                            }
                            else{
                                // 检查总和是否为 100
                                if (sum !== totalProportion) {
                                    alert('第'+(index+1)+'题比例填写错误  '+"输入框的值加起来应该是100");
                                    //layer.msg('第'+(index+1)+'题比例填写错误  '+"输入框的值加起来应该是100", {icon: 2});
                                }else{
                                    layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                                    //alert("本题比例填写正确！");
                                }
                            }
                        }
                    } else if (questionType == 5) {  // 处理单选量标题题型
                        let options = lists.eq(index).find('.custom-input:not(.layui-textarea)').parent();
                        let totalProportion = 100;
                        let sum = 0;
                        let flag = true

                        let inputElement = $(options).find('.custom-input:not(.layui-textarea)');
                        if(inputElement.val().includes('维度')){
                            layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                        }else{

                            // 检查并计算输入框的值总和
                            options.each(function(optionIndex, optionElement) {
                                let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                                let value = parseFloat(inputElement.val());
                                sum += value;
                                // 检查值是否在 [0, 100] 范围内
                                if (isNaN(value) || value < 0 || value > 100) {
                                    flag=false

                                }
                            });
                            if(!flag){
                                alert('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字");
                                // layer.msg('第'+(index+1)+'题比例填写错误  '+"每个输入框请填写0到100之间的数字", {icon: 2});
                            }
                            else{
                                // 检查总和是否为 100
                                if (sum !== totalProportion) {
                                    alert('第'+(index+1)+'题比例填写错误  '+"输入框的值加起来应该是100");
                                    //layer.msg('第'+(index+1)+'题比例填写错误  '+"输入框的值加起来应该是100", {icon: 2});
                                }else{
                                    layer.msg('第'+(index+1)+'题比例填写正确！', {icon: 1});
                                    //alert("本题比例填写正确！");
                                }
                            }
                        }
                    } else {
                        alert(`第${index+1}题，暂不支持检查该题型比例`);
                    }
                })

                function generateEqualProportions(count, total, equalValue = null) {
                    if (equalValue === null) {
                        equalValue = Math.floor(total / count);
                    }
                    const proportions = [];
                    for (let i = 0; i < count - 1; i++) {
                        proportions.push(equalValue);
                    }
                    proportions.push(total - equalValue * (count - 1));
                    return proportions;
                }

                //div_list.eq(index).append(test_btn);
                div_list.eq(index).append(random_bili_btn);
                div_list.eq(index).append(average_btn);
                div_list.eq(index).append(fill0_btn);
                div_list.eq(index).append(clear_btn);
                div_list.eq(index).append(jiancha_btn);

                // 给指定元素添加事件处理程序来限制输入
                // 给指定元素添加事件处理程序来限制输入
                // 给指定元素添加事件处理程序来限制输入
                function pointLimitInput(options){
                    // 使用事件委托绑定 blur 事件
                    options.on('blur', 'input', function() {
                        let inputValue = $(this).val();
                        // 使用正则验证输入
                        //const validPattern = /^(100|[1-9]?\d|平均比例|0\.8左右信效度|0\.9左右信效度|0\.95左右信效度)$/;
                        const validPattern = /^(100|[1-9]?\d|平均比例|维度\d+-(正态|偏左|偏右))$/;
                        if (!validPattern.test(inputValue)) {
                            $(this).val("");  // 清空输入框
                            layer.msg('请填写合法的比例数据！', {icon: 2});
                        }
                    });
                }



                if(questionType==1 || questionType == 2){  //填空题
                    let div_container = $('<div class="input-container"></div>');
                    div_container.append(input_text);
                    lists.eq(index).append(div_container);
                    let inputValue = input_text.val(); // 获取输入框的值
                    random_bili_btn.remove();
                    average_btn.remove();
                    fill0_btn.remove();
                    jiancha_btn.remove()

                    const  generateName_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_name" id="generateName_btn_${index + 1}" style="margin-left:1rem;">生成随机姓名</button>`);
                    const  generatePhone_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_phone" id="generatePhone_btn_${index + 1}" style="margin-left:1rem;">生成随机手机号</button>`);
                    const  generateEmail_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_email" id="generateEmail_btn_${index + 1}" style="margin-left:1rem;">生成随机邮箱</button>`);
                    const  generateAdvice_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_advice" id="generateAdvice_btn_${index + 1}" style="margin-left:1rem;">生成随机建议性回答</button>`);
                    const  generateRandomNum_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_randomNum" id="generateRandomNum_btn_${index + 1}" style="margin-left:1rem;">生成范围内随机数字</button>`);
                    const  generateRandomDate_btn = $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_randomDate" id="generateRandomDate_btn_${index + 1}" style="margin-left:1rem;">生成范围内随机年月日</button>`);

                    generateAdvice_btn.on('click', function(e) {
                        input_text.val(generateShortAdvice())
                        // 移除只读属性
                        input_text.prop('readonly', false);
                        // 恢复背景颜色
                        input_text.css({
                            'background-color': '',
                            'cursor': ''
                        });
                        // 移除之前绑定的事件
                        input_text.off('focus');
                        layer.msg('第'+(index + 1)+'题已生成随机建议性回答', {icon: 1});
                    });
                    generateName_btn.on('click', function(e) {
                        // 设置文本框为只读
                        input_text.prop('readonly', true);
                        // 更改背景颜色为淡灰色
                        input_text.css({
                            'background-color': '#f0f0f0',
                            'cursor': 'not-allowed'
                        });

                        // 绑定尝试编辑的事件
                        input_text.off('focus').on('focus', function(e){
                            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                            // 取消获取焦点
                            e.target.blur();
                        });
                        input_text.val('@@生成随机姓名@@');
                        layer.msg('第'+(index + 1)+'题已设置为生成随机姓名', {icon: 1});
                    });

                    generateRandomNum_btn.on('click', function(e) {
                        // 使用 layer.open 创建一个自定义弹框
                        layer.open({
                            type: 1,
                            title: '设置随机数范围',
                            area: ['350px', '220px'], // 弹框大小
                            content: `
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label for="minValue" style="display: inline-block; width: 80px; text-align: right;">最小值：</label>
                    <input type="number" id="minValue" name="minValue" style="width: 150px; padding: 5px;" />
                </div>
                <div style="margin-bottom: 15px;">
                    <label for="maxValue" style="display: inline-block; width: 80px; text-align: right;">最大值：</label>
                    <input type="number" id="maxValue" name="maxValue" style="width: 150px; padding: 5px;" />
                </div>
            </div>
        `,
                            btn: ['确定', '取消'], // 使用 Layer 的默认按钮
                            yes: function(indexLayer, layero){
                                // 获取用户输入的最小值和最大值
                                let minValue = layero.find('#minValue').val().trim();
                                let maxValue = layero.find('#maxValue').val().trim();

                                // 验证是否为空
                                if (minValue === '' || maxValue === '') {
                                    layer.msg('最小值和最大值不能为空', {icon: 2});
                                    return;
                                }

                                minValue = parseInt(minValue, 10);
                                maxValue = parseInt(maxValue, 10);

                                // 检查是否为整数
                                if (isNaN(minValue) || isNaN(maxValue)) {
                                    layer.msg('请输入有效的整数值', {icon: 2});
                                    return;
                                }

                                // 检查最大值是否大于最小值
                                if (minValue >= maxValue) {
                                    layer.msg('最大值必须大于最小值', {icon: 2});
                                    return;
                                }

                                // 设置文本框内容
                                input_text.val(`@@随机数(${minValue},${maxValue})@@`);
                                // 设置文本框为只读
                                input_text.prop('readonly', true);
                                // 更改背景颜色为淡灰色
                                input_text.css({
                                    'background-color': '#f0f0f0',
                                    'cursor': 'not-allowed'
                                });

                                // 绑定尝试编辑的事件
                                input_text.off('focus').on('focus', function(e){
                                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                    // 取消获取焦点
                                    e.target.blur();
                                });

                                layer.msg('第'+(index + 1)+'题已生成范围内随机数字模板', {icon: 1});
                                layer.close(indexLayer);
                            },
                            btn2: function(indexLayer, layero){
                                // 取消按钮的回调，如果不需要特殊处理，可以留空
                                layer.close(indexLayer);
                            },
                            success: function(layero, indexLayer){
                                // 可以在这里添加额外的初始化代码
                            }
                        });

                    });

                    generateRandomDate_btn.on('click', function(e) {
                        // 使用 layer.open 创建一个自定义弹框
                        layer.open({
                            type: 1,
                            title: '设置随机年月日范围',
                            area: ['400px', '260px'], // 弹框大小
                            content: `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <label for="startDate" style="display: inline-block; width: 80px; text-align: right;">开始日期：</label>
                            <input type="date" id="startDate" name="startDate" style="width: 200px; padding: 5px;" />
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label for="endDate" style="display: inline-block; width: 80px; text-align: right;">结束日期：</label>
                            <input type="date" id="endDate" name="endDate" style="width: 200px; padding: 5px;" />
                        </div>
                    </div>
                `,
                            btn: ['确定', '取消'], // 使用 Layer 的默认按钮
                            yes: function(indexLayer, layero){
                                // 获取用户输入的开始日期和结束日期
                                let startDate = layero.find('#startDate').val().trim();
                                let endDate = layero.find('#endDate').val().trim();

                                // 验证是否为空
                                if (startDate === '' || endDate === '') {
                                    layer.msg('开始日期和结束日期不能为空', {icon: 2});
                                    return;
                                }

                                // 检查日期格式（简要检查，浏览器的 date input 已经做了基本验证）
                                // 这里假设输入的日期格式为 YYYY-MM-DD
                                let datePattern = /^\d{4}-\d{2}-\d{2}$/;
                                if (!datePattern.test(startDate) || !datePattern.test(endDate)) {
                                    layer.msg('请输入有效的日期格式 YYYY-MM-DD', {icon: 2});
                                    return;
                                }

                                // 将日期转换为 Date 对象进行比较
                                let start = new Date(startDate);
                                let end = new Date(endDate);

                                if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                                    layer.msg('请输入有效的日期', {icon: 2});
                                    return;
                                }

                                // 检查结束日期是否晚于开始日期
                                if (start >= end) {
                                    layer.msg('结束日期必须晚于开始日期', {icon: 2});
                                    return;
                                }

                                // 设置文本框内容
                                input_text.val(`@@范围内随机年月日(${startDate},${endDate})@@`);
                                // 设置文本框为只读
                                input_text.prop('readonly', true);
                                // 更改背景颜色为淡灰色
                                input_text.css({
                                    'background-color': '#f0f0f0',
                                    'cursor': 'not-allowed'
                                });

                                // 绑定尝试编辑的事件
                                input_text.off('focus').on('focus', function(e){
                                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                    // 取消获取焦点
                                    e.target.blur();
                                });

                                layer.msg('第'+(index + 1)+'题已生成范围内随机年月日模板', {icon: 1});
                                layer.close(indexLayer);
                            },
                            btn2: function(indexLayer, layero){
                                // 取消按钮的回调，如果不需要特殊处理，可以留空
                                layer.close(indexLayer);
                            },
                            // 确保弹框的 z-index 足够高，以防止被其他元素覆盖
                            success: function(layero, indexLayer){
                                // 可以在这里添加额外的初始化代码
                            }
                        });

                    });

                    generatePhone_btn.on('click', function(e) {
                        // 设置文本框为只读
                        input_text.prop('readonly', true);
                        // 更改背景颜色为淡灰色
                        input_text.css({
                            'background-color': '#f0f0f0',
                            'cursor': 'not-allowed'
                        });

                        // 绑定尝试编辑的事件
                        input_text.off('focus').on('focus', function(e){
                            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                            // 取消获取焦点
                            e.target.blur();
                        });
                        input_text.val('@@生成随机手机号@@');
                        layer.msg('第'+(index + 1)+'题已设置为生成随机手机号', {icon: 1});
                    });

                    generateEmail_btn.on('click', function(e) {
                        // 设置文本框为只读
                        input_text.prop('readonly', true);
                        // 更改背景颜色为淡灰色
                        input_text.css({
                            'background-color': '#f0f0f0',
                            'cursor': 'not-allowed'
                        });

                        // 绑定尝试编辑的事件
                        input_text.off('focus').on('focus', function(e){
                            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                            // 取消获取焦点
                            e.target.blur();
                        });
                        input_text.val('@@生成随机邮箱@@');
                        layer.msg('第'+(index + 1)+'题已设置为生成随机邮箱', {icon: 1});
                    });

                    div_list.eq(index).append(generateAdvice_btn);
                    div_list.eq(index).append(generateName_btn);
                    div_list.eq(index).append(generatePhone_btn);
                    div_list.eq(index).append(generateEmail_btn);
                    div_list.eq(index).append(generateRandomNum_btn);
                    div_list.eq(index).append(generateRandomDate_btn);

                    /*
                    tiankong_list = ['王翠花','小明','小红'];
                    ccc+=1
                    bili = randomBili(tiankong_list.length)
                    document.querySelector('#q题号').value=tiankong_list[danxuan(bili)]
                    */
                }
                if (questionType == 3) {  // 单选题
                    /*
                    let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) .label.hasImagelabel').parent();
                    if(options.length==0){
                        options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) .label').parent();
                    }
                    */
                    let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                    //console.log(options); // 检查 options 是否包含正确的父级元素
                    //console.log(options.length); // 检查选中的元素数量
                    options.each(function(optionIndex, optionElement) {
                        let $option = $(optionElement);

                        // 创建选项比例输入框
                        let input_option_text = $('<input type="text" class="layui-input custom-input" style="width:90%;margin-bottom:8px;font-size: 12px;" placeholder="输入选项比例(0-100的整数)">');
                        $option.append(input_option_text);

                        // 检查是否存在.ui-text的div
                        if ($option.find('.ui-text').length > 0) {
                            // 创建文本域
                            let input_text = $(`<textarea id="yifeng${index+1}_${optionIndex+1}" class="layui-textarea custom-input" style="height:100px;width:80%;margin-top:10px" placeholder="请在这里输入随机文本内容，如有多个文本随机请用英文逗号隔开,例如：答案1,答案2,答案3"></textarea>`);

                            // 创建按钮容器
                            let btnContainer = $(`
            <div class="btn-container"
                 style="margin:10px 0;
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;">
            </div>
        `);



                            // 创建功能按钮（保持原有名称和ID）
                            const buttons = [
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_name"
                            id="generateName_btn_${index + 1}" style="background-color: #FF69B4;">生成随机姓名</button>`),
                                    action: function() {
                                        handleReadonlyField(input_text, '@@生成随机姓名@@', '生成随机姓名');
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_phone"
                            id="generatePhone_btn_${index + 1}" style="background-color: #FF69B4;">生成随机手机号</button>`),
                                    action: function() {
                                        handleReadonlyField(input_text, '@@生成随机手机号@@', '生成随机手机号');
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_email"
                            id="generateEmail_btn_${index + 1}" style="background-color: #FF69B4;">生成随机邮箱</button>`),
                                    action: function() {
                                        handleReadonlyField(input_text, '@@生成随机邮箱@@', '生成随机邮箱');
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_advice"
                            id="generateAdvice_btn_${index + 1}" style="background-color: #FF69B4;">生成随机建议性回答</button>`),
                                    action: function() {
                                        input_text.val(generateShortAdvice())
                                            .prop('readonly', false)
                                            .css({ 'background-color': '', 'cursor': '' })
                                            .off('focus');
                                        layer.msg(`本选项填空答案已生成随机建议性回答`, {icon: 1});
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_randomNum"
                            id="generateRandomNum_btn_${index + 1}" style="background-color: #FF69B4;">生成范围内随机数字</button>`),
                                    action: function() {
                                        showNumberRangePopup(input_text, index);
                                        //layer.msg(`本选项填空答案已生成范围内随机数字`, {icon: 1});
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_randomDate"
                            id="generateRandomDate_btn_${index + 1}" style="background-color: #FF69B4;">生成范围内随机年月日</button>`),
                                    action: function() {
                                        showDateRangePopup(input_text, index);
                                        //layer.msg(`本选项填空答案已生成范围内随机年月日`, {icon: 1});
                                    }
                                },
                                // 新增清空按钮
                                {
                                    element: $(`<button type="button"
                    class="layui-btn layui-btn-xs layui-btn-danger"
                    id="clear_btn_${index + 1}"
                    style="background-color: #FF5722;">
                    清空答案
                </button>`),
                                    action: function() {
                                        input_text.val('')
                                            .prop('readonly', false)
                                            .css({
                                            'background-color': '',
                                            'cursor': ''
                                        })
                                            .off('focus');
                                        layer.msg(`本选项填空答案已清空`, {icon: 1});
                                    }
                                }
                            ];

                            // 绑定按钮事件
                            buttons.forEach(btn => {
                                btn.element.on('click', btn.action);
                                btnContainer.append(btn.element);
                            });

                            // 组装元素
                            $option.append(btnContainer);
                            $option.append(input_text);
                        }
                    });
                    pointLimitInput(options)


                    // 阻止事件冒泡，防止输入框点击影响选项的点击
                    options.find('input').on('click', function(event) {
                        event.stopPropagation();
                    });
                    //pointLimitInput(options)
                    //var element = document.getElementById(`xinxiaodu_s_btn_${index + 1}`);
                    //xinxiaodu_s_btn.disabled = true;

                    // 将元素隐藏
                    //xinxiaodu_s_btn.style.display = "none";
                    /*
                            xinxiaodu_s_btn.remove();
                            xinxiaodu_m_btn.remove();
                            xinxiaodu_l_btn.remove();
                            xinxiaodu_dire_btn.remove();
                            */

                    /*
                    tiankong_list = ['王翠花','小明','小红'];
                    ccc+=1
                    bili = randomBili(tiankong_list.length)
                    document.querySelector('#q题号').value=tiankong_list[danxuan(bili)]
                    */
                }
                if (questionType == 4) {  // 多选题
                    let options = lists.eq(index).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                    options.each(function(optionIndex, optionElement) {
                        let input_option_text = $('<input type="text" class="layui-input custom-input" style="width:60%" placeholder="输入选项比例(0-100的整数)">');
                        $(optionElement).append(input_option_text);

                        let $option = $(optionElement);

                        $option.append(input_option_text);

                        // 检查是否存在.ui-text的div
                        if ($option.find('.ui-text').length > 0) {
                            // 创建文本域
                            let input_text = $(`<textarea id="yifeng${index+1}_${optionIndex+1}" class="layui-textarea custom-input" style="height:100px;width:80%;margin-top:10px" placeholder="请在这里输入随机文本内容，如有多个文本随机请用英文逗号隔开,例如：答案1,答案2,答案3"></textarea>`);

                            // 创建按钮容器
                            let btnContainer = $(`
            <div class="btn-container"
                 style="margin:10px 0;
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;">
            </div>
        `);



                            // 创建功能按钮（保持原有名称和ID）
                            const buttons = [
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_name"
                            id="generateName_btn_${index + 1}" style="background-color: #FF69B4;">生成随机姓名</button>`),
                                    action: function() {
                                        handleReadonlyField(input_text, '@@生成随机姓名@@', '生成随机姓名');
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_phone"
                            id="generatePhone_btn_${index + 1}" style="background-color: #FF69B4;">生成随机手机号</button>`),
                                    action: function() {
                                        handleReadonlyField(input_text, '@@生成随机手机号@@', '生成随机手机号');
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_email"
                            id="generateEmail_btn_${index + 1}" style="background-color: #FF69B4;">生成随机邮箱</button>`),
                                    action: function() {
                                        handleReadonlyField(input_text, '@@生成随机邮箱@@', '生成随机邮箱');
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_advice"
                            id="generateAdvice_btn_${index + 1}" style="background-color: #FF69B4;">生成随机建议性回答</button>`),
                                    action: function() {
                                        input_text.val(generateShortAdvice())
                                            .prop('readonly', false)
                                            .css({ 'background-color': '', 'cursor': '' })
                                            .off('focus');
                                        layer.msg(`本选项填空答案已生成随机建议性回答`, {icon: 1});
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_randomNum"
                            id="generateRandomNum_btn_${index + 1}" style="background-color: #FF69B4;">生成范围内随机数字</button>`),
                                    action: function() {
                                        showNumberRangePopup(input_text, index);
                                        //layer.msg(`本选项填空答案已生成范围内随机数字`, {icon: 1});
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_randomDate"
                            id="generateRandomDate_btn_${index + 1}" style="background-color: #FF69B4;">生成范围内随机年月日</button>`),
                                    action: function() {
                                        showDateRangePopup(input_text, index);
                                        //layer.msg(`本选项填空答案已生成范围内随机年月日`, {icon: 1});
                                    }
                                },
                                // 新增清空按钮
                                {
                                    element: $(`<button type="button"
                    class="layui-btn layui-btn-xs layui-btn-danger"
                    id="clear_btn_${index + 1}"
                    style="background-color: #FF5722;">
                    清空答案
                </button>`),
                                    action: function() {
                                        input_text.val('')
                                            .prop('readonly', false)
                                            .css({
                                            'background-color': '',
                                            'cursor': ''
                                        })
                                            .off('focus');
                                        layer.msg(`本选项填空答案已清空`, {icon: 1});
                                    }
                                }
                            ];

                            // 绑定按钮事件
                            buttons.forEach(btn => {
                                btn.element.on('click', btn.action);
                                btnContainer.append(btn.element);
                            });

                            // 组装元素
                            $option.append(btnContainer);
                            $option.append(input_text);
                        }
                    });
                    // 阻止事件冒泡，防止输入框点击影响选项的点击
                    options.find('input').on('click', function(event) {
                        event.stopPropagation();
                    });
                    // 添加 input 事件处理程序来限制输入
                    pointLimitInput(options)
                }
                if (questionType == 6) {  // 处理矩阵量标题题型
                    let matrixType =  checkMatrixRating(lists, index) ? 'single' : 'multiple';
                    let options = lists.eq(index).find('.matrix-rating a');

                    // 判断是否存在 .onscore.modlen7
                    const hasOnscore = lists.eq(index).find('.onscore.modlen7').length > 0;

                    options.each(function (optionIndex, optionElement) {
                        // 创建输入框
                        let input_option_text = $('<input type="text" class="layui-input custom-input" style="width:90%;font-size: 12px;" placeholder="输入比例">');
                        $(optionElement).after(input_option_text); // 在 a 元素后插入输入框

                        // 阻止点击事件冒泡
                        input_option_text.on('click', function (e) {
                            e.stopPropagation();
                        });
                    });

                    if (hasOnscore) {
                        // 找到最近的表格容器（如 <table>）
                        const table = lists.eq(index).closest('table');


                        // 方案2：为每个 td 添加 padding
                        const targetTrs = lists.eq(index).find('tr');
                        targetTrs.find('td').css({
                            'padding-bottom': '50px'
                        });
                    }

                    let parentElement = lists.eq(index).find('.matrix-rating'); // 父容器
                    pointLimitInput(parentElement)
                    if (matrixType === 'single') {
                        // 处理矩阵单选题的逻辑
                        let newInfoValue='single'
                        info_label.text(`info: ${newInfoValue}`);

                    } else if (matrixType === 'multiple') {
                        // 处理矩阵多选题的逻辑
                        let newInfoValue='multiple'
                        info_label.text(`info: ${newInfoValue}`);
                    }
                }
                if (questionType == 7) {  // 处理下拉框题型
                    let selectElement = lists.eq(index).find('select');
                    let selectOptions = selectElement.find('option:not([style*="display:none"]):not([style*="display: none"])');
                    let table = $('<table class="custom-table"></table>');

                    selectOptions.each(function(optionIndex, optionElement) {
                        if (optionIndex > 0) {  // Skip the first option ("请选择")
                            let optionValue = $(optionElement).val();
                            let optionText = $(optionElement).text();
                            let tableRow = $('<tr class="table-row"></tr>');
                            let optionCell = $('<td class="option-cell"></td>').text(optionText);
                            let inputCell = $('<td class="input-cell"></td>');
                            let inputElement = $('<input type="text" class="layui-input custom-input" placeholder="输入比例">');

                            inputCell.append(inputElement);
                            tableRow.append(optionCell);
                            tableRow.append(inputCell);
                            table.append(tableRow);

                            // 阻止点击事件冒泡
                            inputElement.on('click', function(e) {
                                e.stopPropagation();
                            });


                        }
                    });
                    // 添加 input 事件处理程序来限制输入
                    pointLimitInput(table)
                    // 添加 input 事件处理程序来限制输入
                    lists.eq(index).append(table);

                    // 处理下拉框题的逻辑
                    selectElement.on('change', function() {
                        // 获取选择的值并进行相应操作
                        var selectedValue = selectElement.val();
                        // 处理选择的值
                    });

                }
                if (questionType == 11) {
                    let listItems = lists.eq(index).find('.ui-li-static:not([style*="display:none"]):not([style*="display: none"])');
                    let table = $('<table class="custom-table"></table>');

                    listItems.each(function(itemIndex, itemElement) {
                        let $item = $(itemElement);
                        // 修复选择器：精确获取选项文本（添加调试输出）
                        let labelText = $(itemElement).find('span').text();
                        //console.log('选项文本:', labelText); // 调试用

                        // 创建带题目标签的行（修复结构嵌套）
                        let tableRow = $(`
            <tr class="table-row">
                <td class="option-cell" style="vertical-align: top; min-width: 120px">
                    <div class="option-label">${labelText}</div>
                </td>
                <td class="input-cell" style="padding-left:15px"></td>
            </tr>
        `);

                        // 输入容器（修复标签显示结构）
                        let inputGroup = $(`
            <div class="input-group">
                <div class="input-with-label" style="display:flex; align-items:center">
                    <span class="input-title"
                          style="display:block !important;
                                 width:80px;
                                 margin-right:12px;
                                 color:#333;
                                 font-weight:500">
                    </span>
                    <input type="text"
                           class="layui-input custom-input"
                           style="width:200px"
                           placeholder="输入比例(0-100)">
                </div>
            </div>
        `);

                        // 功能区域容器
                        let functionContainer = $('<div class="function-area" style="margin-top:10px"></div>');

                        // 处理填空区域
                        let uiTextDiv = $item.find('.ui-text');
                        if (uiTextDiv.length > 0) {
                            uiTextDiv.css('display', 'block');

                            // 文本域（保持原有参数）
                            let inputText = $(`
                <textarea class="layui-textarea custom-input"
                id="yifeng${index+1}_${itemIndex+1}"
                          style="height:100px;width:80%;margin-top:8px"
                          placeholder="请在这里输入随机文本内容，如有多个文本随机请用英文逗号隔开,例如：答案1,答案2,答案3"
                </textarea>
            `);

                            // 按钮组（完整保留所有按钮）
                            let btnContainer = $(`
                <div class="btn-container"
                     style="margin:10px 0;display:flex;flex-wrap:wrap;gap:8px">
                </div>
            `);

                            // 完整按钮配置
                            const buttons = [
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_name"
                            id="generateName_btn_${index + 1}_${itemIndex}" style="background-color: #FF69B4;">生成随机姓名</button>`),
                                    action: function() {
                                        handleReadonlyField(inputText, '@@生成随机姓名@@', '生成随机姓名');  // 修正变量名
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_phone"
                            id="generatePhone_btn_${index + 1}_${itemIndex}" style="background-color: #FF69B4;">生成随机手机号</button>`),
                                    action: function() {
                                        handleReadonlyField(inputText, '@@生成随机手机号@@', '生成随机手机号');  // 修正变量名
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_email"
                            id="generateEmail_btn_${index + 1}_${itemIndex}" style="background-color: #FF69B4;">生成随机邮箱</button>`),
                                    action: function() {
                                        handleReadonlyField(inputText, '@@生成随机邮箱@@', '生成随机邮箱');  // 修正变量名
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_advice"
                            id="generateAdvice_btn_${index + 1}_${itemIndex}" style="background-color: #FF69B4;">生成随机建议性回答</button>`),
                                    action: function() {
                                        inputText.val(generateShortAdvice())  // 修正变量名
                                            .prop('readonly', false)
                                            .css({ 'background-color': '', 'cursor': '' })
                                            .off('focus');
                                        layer.msg(`本选项填空答案已生成随机建议性回答`, {icon: 1});
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_randomNum"
                            id="generateRandomNum_btn_${index + 1}_${itemIndex}" style="background-color: #FF69B4;">生成范围内随机数字</button>`),
                                    action: function() {
                                        showNumberRangePopup(inputText, index);  // 修正变量名
                                    }
                                },
                                {
                                    element: $(`<button type="button" class="layui-btn layui-btn-xs oneRandom_randomDate"
                            id="generateRandomDate_btn_${index + 1}_${itemIndex}" style="background-color: #FF69B4;">生成范围内随机年月日</button>`),
                                    action: function() {
                                        showDateRangePopup(inputText, index);  // 修正变量名
                                    }
                                },
                                {
                                    element: $(`<button type="button"
                        class="layui-btn layui-btn-xs layui-btn-danger"
                        id="clear_btn_${index + 1}_${itemIndex}"
                        style="background-color: #FF5722;">
                        清空答案
                    </button>`),
                                    action: function() {
                                        inputText.val('')  // 修正变量名
                                            .prop('readonly', false)
                                            .css({'background-color': '', 'cursor': ''})
                                            .off('focus');
                                        layer.msg(`本选项填空答案已清空`, {icon: 1});
                                    }
                                }
                            ];

                            // 绑定按钮事件
                            buttons.forEach(btn => {
                                btn.element.on('click', function(e) {
                                    e.stopPropagation();
                                    btn.action();
                                });
                                btnContainer.append(btn.element);
                            });

                            functionContainer.append(btnContainer, inputText);
                            inputGroup.append(functionContainer);
                        }

                        // 组装元素
                        tableRow.find('.input-cell').append(inputGroup);
                        table.append(tableRow);
                    });

                    // 应用样式
                    $('<style>')
                        .text(`
        .input-title {
            display: block !important;
            opacity: 1 !important;
            font-size:14px !important;
        }
        .custom-table td {
            background: #fff !important; /* 防止透明背景导致文字不可见 */
        }
    `)
                        .appendTo('head');

                    lists.eq(index).append(table);
                    pointLimitInput(table);
                }
                if (questionType == 8) {  // 处理滑条题型
                    let table = $('<table class="custom-table"></table>');
                    let rangeslider = lists.eq(index).find('.rangeslider');
                    let rulers = rangeslider.find('.ruler .cm');

                    rulers.each(function(rulerIndex, rulerElement) {
                        let value = $(rulerElement).data('value');
                        let labelText = value + '分';
                        let tableRow = $('<tr class="table-row"></tr>');
                        let optionCell = $('<td class="option-cell"></td>').text(labelText);
                        let inputCell = $('<td class="input-cell"></td>');
                        let inputElement = $('<input type="text" class="layui-input custom-input" placeholder="输入比例">');

                        inputCell.append(inputElement);
                        tableRow.append(optionCell);
                        tableRow.append(inputCell);
                        table.append(tableRow);

                        // 阻止点击事件冒泡
                        inputElement.on('click', function(e) {
                            e.stopPropagation();
                        });

                    });
                    // 添加 input 事件处理程序来限制输入
                    pointLimitInput(table)
                    lists.eq(index).append(table);

                }
                if (questionType == 9||questionType == 12) {  // 处理矩阵滑条题型
                    let table = $('<table class="custom-table" style="font-size: 12px;"></table>');
                    let rangesliders = lists.eq(index).find('.rangeslider');

                    rangesliders.each(function(rangesliderIndex, rangesliderElement) {
                        let rulers = $(rangesliderElement).find('.ruler .cm');
                        let newRow = $('<tr class="table-row"></tr>');

                        rulers.each(function(rulerIndex, rulerElement) {
                            let value = $(rulerElement).data('value');
                            let labelText = value + '分';
                            let optionCell = $('<td class="option-cell" style="padding: 5px;"></td>').text(labelText);
                            let inputCell = $('<td class="input-cell" style="padding: 5px;"></td>');
                            let inputElement = $('<input type="text" class="layui-input custom-input" style="width: 80px;" placeholder="输入比例">');

                            inputCell.append(inputElement);
                            newRow.append(optionCell);
                            newRow.append(inputCell);

                            // 阻止点击事件冒泡
                            inputElement.on('click', function(e) {
                                e.stopPropagation();
                            });

                        });
                        // 添加 input 事件处理程序来限制输入
                        pointLimitInput(table)
                        table.append(newRow);
                    });

                    // 找到对应题目的 div 并插入表格
                    let correspondingQuestionDiv = lists.eq(index).find(".errorMessage");
                    correspondingQuestionDiv.after(table);


                    if (lists.eq(index).find('.rangeslider').length === 0) {
                        random_bili_btn.remove();
                        average_btn.remove();
                        fill0_btn.remove();
                        jiancha_btn.remove()
                        clear_btn.remove()
                    }





                    if (lists.eq(index).find('.matrix-rating').hasClass('scaletablewrap')) {
                        let currentList = lists.eq(index);
                        let matrixTable = currentList.find('.matrix-rating');

                        if (matrixTable.length > 0) {
                            matrixTable.find('tr').each(function (rowIndex) {
                                let rowTitle = $(this).find('.rowtitlediv span.itemTitleSpan').text().trim();
                                let inputElement = $(this).find('textarea,input');

                                if (rowTitle && inputElement.length > 0) {
                                    let fieldHtml = `
                    <div style="margin-bottom: 20px;">
                        <div style="margin-bottom: 10px; font-size: 1.1rem; color: #333;">
                            <strong>${rowTitle}</strong>
                        </div>

                        <div class="button-container" style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: flex-start; margin-bottom: 10px;">
                            <button type="button" class="layui-btn layui-btn-xs oneRandom_name" id="generateName_btn_${index}_${rowIndex}">生成随机姓名</button>
                            <button type="button" class="layui-btn layui-btn-xs oneRandom_phone" id="generatePhone_btn_${index}_${rowIndex}">生成随机手机号</button>
                             <button type="button" class="layui-btn layui-btn-xs oneRandom_email" id="generateEmail_btn_${index}_${rowIndex}">生成随机邮箱</button>
                            <button type="button" class="layui-btn layui-btn-xs oneRandom_randomNum" id="generateRandomNum_btn_${index}_${rowIndex}">生成范围内随机数字</button>
                             <button type="button" class="layui-btn layui-btn-xs oneRandom_randomDate" id="generateRandomDate_btn_${index}_${rowIndex}">生成范围内随机年月日</button>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger clear" id="clear_btn_${index}_${rowIndex}">清空答案</button>
                        </div>

  <textarea class="layui-input custom-input input-limit" style="width:100%; height: 150px; border-radius: 4px;" placeholder="请在这里输入随机文本内容，如有多个文本随机请用英文逗号隔开，例如：答案1,答案2,答案3"></textarea>                    </div>
                `;

                                    currentList.append(fieldHtml);
                                    let generatedTextarea = currentList.find('textarea').last();

                                    $(`#clear_btn_${index}_${rowIndex}`).on('click', function () {
                                        // 清空文本框内容
                                        generatedTextarea.val('');
                                        // 移除只读属性
                                        generatedTextarea.prop('readonly', false);
                                        // 恢复背景颜色
                                        generatedTextarea.css({
                                            'background-color': '',
                                            'cursor': ''
                                        });
                                        // 移除之前绑定的事件
                                        generatedTextarea.off('focus');
                                        layer.msg('本小题已清空设置', {icon: 1});
                                    });

                                    $(`#generateName_btn_${index}_${rowIndex}`).on('click', function () {
                                        // 设置文本框为只读
                                        generatedTextarea.prop('readonly', true);
                                        // 更改背景颜色为淡灰色
                                        generatedTextarea.css({
                                            'background-color': '#f0f0f0',
                                            'cursor': 'not-allowed'
                                        });

                                        // 绑定尝试编辑的事件
                                        generatedTextarea.off('focus').on('focus', function(e){
                                            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                            // 取消获取焦点
                                            e.target.blur();
                                        });
                                        generatedTextarea.val('@@生成随机姓名@@');
                                        layer.msg('本小题已设置为生成随机姓名', {icon: 1});
                                    });

                                    $(`#generatePhone_btn_${index}_${rowIndex}`).on('click', function () {
                                        // 设置文本框为只读
                                        generatedTextarea.prop('readonly', true);
                                        // 更改背景颜色为淡灰色
                                        generatedTextarea.css({
                                            'background-color': '#f0f0f0',
                                            'cursor': 'not-allowed'
                                        });

                                        // 绑定尝试编辑的事件
                                        generatedTextarea.off('focus').on('focus', function(e){
                                            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                            // 取消获取焦点
                                            e.target.blur();
                                        });
                                        generatedTextarea.val('@@生成随机手机号@@');
                                        layer.msg('本小题已设置为生成随机手机号', {icon: 1});
                                    });

                                    $(`#generateEmail_btn_${index}_${rowIndex}`).on('click', function () {
                                        // 设置文本框为只读
                                        generatedTextarea.prop('readonly', true);
                                        // 更改背景颜色为淡灰色
                                        generatedTextarea.css({
                                            'background-color': '#f0f0f0',
                                            'cursor': 'not-allowed'
                                        });

                                        // 绑定尝试编辑的事件
                                        generatedTextarea.off('focus').on('focus', function(e){
                                            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                            // 取消获取焦点
                                            e.target.blur();
                                        });
                                        generatedTextarea.val('@@生成随机邮箱@@');
                                        layer.msg('本小题已设置为生成随机邮箱', {icon: 1});
                                    });

                                    // 点击生成随机数按钮的事件处理
                                    $(`#generateRandomNum_btn_${index}_${rowIndex}`).on('click', function () {
                                        // 使用 layer.open 创建一个自定义弹框
                                        layer.open({
                                            type: 1,
                                            title: '设置随机数范围',
                                            area: ['350px', '220px'], // 弹框大小
                                            content: `
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label for="minValue" style="display: inline-block; width: 80px; text-align: right;">最小值：</label>
                    <input type="number" id="minValue" name="minValue" style="width: 150px; padding: 5px;" />
                </div>
                <div style="margin-bottom: 15px;">
                    <label for="maxValue" style="display: inline-block; width: 80px; text-align: right;">最大值：</label>
                    <input type="number" id="maxValue" name="maxValue" style="width: 150px; padding: 5px;" />
                </div>
            </div>
        `,
                                            btn: ['确定', '取消'], // 使用 Layer 的默认按钮
                                            yes: function(indexLayer, layero){
                                                // 获取用户输入的最小值和最大值
                                                let minValue = layero.find('#minValue').val().trim();
                                                let maxValue = layero.find('#maxValue').val().trim();

                                                // 验证是否为空
                                                if (minValue === '' || maxValue === '') {
                                                    layer.msg('最小值和最大值不能为空', {icon: 2});
                                                    return;
                                                }

                                                minValue = parseInt(minValue, 10);
                                                maxValue = parseInt(maxValue, 10);

                                                // 检查是否为整数
                                                if (isNaN(minValue) || isNaN(maxValue)) {
                                                    layer.msg('请输入有效的整数值', {icon: 2});
                                                    return;
                                                }

                                                // 检查最大值是否大于最小值
                                                if (minValue >= maxValue) {
                                                    layer.msg('最大值必须大于最小值', {icon: 2});
                                                    return;
                                                }

                                                // 设置文本框内容
                                                generatedTextarea.val(`@@随机数(${minValue},${maxValue})@@`);
                                                // 设置文本框为只读
                                                generatedTextarea.prop('readonly', true);
                                                // 更改背景颜色为淡灰色
                                                generatedTextarea.css({
                                                    'background-color': '#f0f0f0',
                                                    'cursor': 'not-allowed'
                                                });

                                                // 绑定尝试编辑的事件
                                                generatedTextarea.off('focus').on('focus', function(e){
                                                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                                    // 取消获取焦点
                                                    e.target.blur();
                                                });

                                                layer.msg('本小题已生成范围随机数模板', {icon: 1});
                                                layer.close(indexLayer);
                                            },
                                            btn2: function(indexLayer, layero){
                                                // 取消按钮的回调，如果不需要特殊处理，可以留空
                                                layer.close(indexLayer);
                                            },
                                            success: function(layero, indexLayer){
                                                // 可以在这里添加额外的初始化代码
                                            }
                                        });
                                    });

                                    $(`#generateRandomDate_btn_${index}_${rowIndex}`).on('click', function () {
                                        // 使用 layer.open 创建一个自定义弹框
                                        layer.open({
                                            type: 1,
                                            title: '设置随机年月日范围',
                                            area: ['400px', '260px'], // 弹框大小
                                            content: `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <label for="startDate" style="display: inline-block; width: 80px; text-align: right;">开始日期：</label>
                            <input type="date" id="startDate" name="startDate" style="width: 200px; padding: 5px;" />
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label for="endDate" style="display: inline-block; width: 80px; text-align: right;">结束日期：</label>
                            <input type="date" id="endDate" name="endDate" style="width: 200px; padding: 5px;" />
                        </div>
                    </div>
                `,
                                            btn: ['确定', '取消'], // 使用 Layer 的默认按钮
                                            yes: function(indexLayer, layero){
                                                // 获取用户输入的开始日期和结束日期
                                                let startDate = layero.find('#startDate').val().trim();
                                                let endDate = layero.find('#endDate').val().trim();

                                                // 验证是否为空
                                                if (startDate === '' || endDate === '') {
                                                    layer.msg('开始日期和结束日期不能为空', {icon: 2});
                                                    return;
                                                }

                                                // 检查日期格式（简要检查，浏览器的 date input 已经做了基本验证）
                                                // 这里假设输入的日期格式为 YYYY-MM-DD
                                                let datePattern = /^\d{4}-\d{2}-\d{2}$/;
                                                if (!datePattern.test(startDate) || !datePattern.test(endDate)) {
                                                    layer.msg('请输入有效的日期格式 YYYY-MM-DD', {icon: 2});
                                                    return;
                                                }

                                                // 将日期转换为 Date 对象进行比较
                                                let start = new Date(startDate);
                                                let end = new Date(endDate);

                                                if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                                                    layer.msg('请输入有效的日期', {icon: 2});
                                                    return;
                                                }

                                                // 检查结束日期是否晚于开始日期
                                                if (start >= end) {
                                                    layer.msg('结束日期必须晚于开始日期', {icon: 2});
                                                    return;
                                                }

                                                // 设置文本框内容
                                                generatedTextarea.val(`@@范围内随机年月日(${startDate},${endDate})@@`);
                                                // 设置文本框为只读
                                                generatedTextarea.prop('readonly', true);
                                                // 更改背景颜色为淡灰色
                                                generatedTextarea.css({
                                                    'background-color': '#f0f0f0',
                                                    'cursor': 'not-allowed'
                                                });

                                                // 绑定尝试编辑的事件
                                                generatedTextarea.off('focus').on('focus', function(e){
                                                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                                    // 取消获取焦点
                                                    e.target.blur();
                                                });

                                                layer.msg('本小题已生成范围随机年月日模板', {icon: 1});
                                                layer.close(indexLayer);
                                            },
                                            btn2: function(indexLayer, layero){
                                                // 取消按钮的回调，如果不需要特殊处理，可以留空
                                                layer.close(indexLayer);
                                            },
                                            // 确保弹框的 z-index 足够高，以防止被其他元素覆盖
                                            success: function(layero, indexLayer){
                                                // 可以在这里添加额外的初始化代码
                                            }
                                        });
                                    });
                                }
                            });
                        } else {
                            console.log("未找到矩阵填空表格，按常规处理");
                        }
                    }else if(lists.eq(index).find('.rangeslider').length === 0){
                        let currentList = lists.eq(index);

                        let topicText = currentList.find('.topichtml');

                        // 创建一个新的容器，用于存放生成的填空项内容，并添加到 DOM 中
                        let fieldContainer = $('<div class="field-container" style="display: flex; flex-direction: column; align-items: center; width: 100%;"></div>');
                        topicText.after(fieldContainer);

                        let questionsAndInputs = [];
                        topicText.find('input, textarea').each(function (inputIndex, inputElement) {
                            let $inputElement = $(inputElement);
                            questionsAndInputs.push({ inputElement: $inputElement });
                        });

                        questionsAndInputs.forEach(function (item, inputIndex) {
                            let $inputElement = item.inputElement;
                            let questionText = `第 ${inputIndex + 1} 个填空`;

                            // 生成的 HTML，不使用 ID，改用类选择器
                            let fieldHtml = `
            <div class="field-item" style="margin-bottom: 20px;">
                <div style="margin-bottom: 10px; font-size: 1.1rem; color: #333;">
                    <strong>${questionText}</strong>
                </div>

                <div class="button-container" style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: flex-start; margin-bottom: 10px;">
                    <button type="button" class="layui-btn layui-btn-xs oneRandom_name">生成随机姓名</button>
                    <button type="button" class="layui-btn layui-btn-xs oneRandom_phone">生成随机手机号</button>
                    <button type="button" class="layui-btn layui-btn-xs oneRandom_email">生成随机邮箱</button>
                    <button type="button" class="layui-btn layui-btn-xs oneRandom_randomNum">生成范围内随机数字</button>
                    <button type="button" class="layui-btn layui-btn-xs oneRandom_randomDate">生成范围内随机年月日</button>
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger clear">清空答案</button>
                </div>

                <textarea class="layui-input custom-input input-limit" style="width:100%; height: 150px; border-radius: 4px;" placeholder="请在这里输入随机文本内容，如有多个文本随机请用英文逗号隔开，例如：答案1,答案2,答案3"></textarea>
            </div>
        `;

                            // 将生成的 HTML 转换为 jQuery 对象
                            let $field = $(fieldHtml);

                            // 将生成的元素添加到容器中
                            currentList.append($field);

                            // 在当前作用域内查找 textarea 和按钮，绑定事件
                            let generatedTextarea = $field.find('textarea');

                            // 清空答案
                            $field.find('.clear').on('click', function () {
                                // 清空文本框内容
                                generatedTextarea.val('');
                                // 移除只读属性
                                generatedTextarea.prop('readonly', false);
                                // 恢复背景颜色
                                generatedTextarea.css({
                                    'background-color': '',
                                    'cursor': ''
                                });
                                // 移除之前绑定的事件
                                generatedTextarea.off('focus');
                                layer.msg('本小题已清空设置', {icon: 1});
                            });

                            // 生成随机姓名
                            $field.find('.oneRandom_name').on('click', function () {
                                // 设置文本框为只读
                                generatedTextarea.prop('readonly', true);
                                // 更改背景颜色为淡灰色
                                generatedTextarea.css({
                                    'background-color': '#f0f0f0',
                                    'cursor': 'not-allowed'
                                });

                                // 绑定尝试编辑的事件
                                generatedTextarea.off('focus').on('focus', function(e){
                                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                    // 取消获取焦点
                                    e.target.blur();
                                });
                                generatedTextarea.val('@@生成随机姓名@@');
                                layer.msg('本小题已设置为生成随机姓名', {icon: 1});
                            });

                            // 生成随机手机号
                            $field.find('.oneRandom_phone').on('click', function () {
                                // 设置文本框为只读
                                generatedTextarea.prop('readonly', true);
                                // 更改背景颜色为淡灰色
                                generatedTextarea.css({
                                    'background-color': '#f0f0f0',
                                    'cursor': 'not-allowed'
                                });

                                // 绑定尝试编辑的事件
                                generatedTextarea.off('focus').on('focus', function(e){
                                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                    // 取消获取焦点
                                    e.target.blur();
                                });
                                generatedTextarea.val('@@生成随机手机号@@');
                                layer.msg('本小题已设置为生成随机手机号', {icon: 1});
                            });

                            // 生成随机邮箱
                            $field.find('.oneRandom_email').on('click', function () {
                                // 设置文本框为只读
                                generatedTextarea.prop('readonly', true);
                                // 更改背景颜色为淡灰色
                                generatedTextarea.css({
                                    'background-color': '#f0f0f0',
                                    'cursor': 'not-allowed'
                                });

                                // 绑定尝试编辑的事件
                                generatedTextarea.off('focus').on('focus', function(e){
                                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                    // 取消获取焦点
                                    e.target.blur();
                                });
                                generatedTextarea.val('@@生成随机邮箱@@');
                                layer.msg('本小题已设置为生成随机邮箱', {icon: 1});
                            });

                            // 生成范围内随机数字模板
                            $field.find('.oneRandom_randomNum').on('click', function () {
                                // 使用 layer.open 创建一个自定义弹框
                                layer.open({
                                    type: 1,
                                    title: '设置随机数范围',
                                    area: ['350px', '220px'], // 弹框大小
                                    content: `
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label for="minValue" style="display: inline-block; width: 80px; text-align: right;">最小值：</label>
                    <input type="number" id="minValue" name="minValue" style="width: 150px; padding: 5px;" />
                </div>
                <div style="margin-bottom: 15px;">
                    <label for="maxValue" style="display: inline-block; width: 80px; text-align: right;">最大值：</label>
                    <input type="number" id="maxValue" name="maxValue" style="width: 150px; padding: 5px;" />
                </div>
            </div>
        `,
                                    btn: ['确定', '取消'], // 使用 Layer 的默认按钮
                                    yes: function(indexLayer, layero){
                                        // 获取用户输入的最小值和最大值
                                        let minValue = layero.find('#minValue').val().trim();
                                        let maxValue = layero.find('#maxValue').val().trim();

                                        // 验证是否为空
                                        if (minValue === '' || maxValue === '') {
                                            layer.msg('最小值和最大值不能为空', {icon: 2});
                                            return;
                                        }

                                        minValue = parseInt(minValue, 10);
                                        maxValue = parseInt(maxValue, 10);

                                        // 检查是否为整数
                                        if (isNaN(minValue) || isNaN(maxValue)) {
                                            layer.msg('请输入有效的整数值', {icon: 2});
                                            return;
                                        }

                                        // 检查最大值是否大于最小值
                                        if (minValue >= maxValue) {
                                            layer.msg('最大值必须大于最小值', {icon: 2});
                                            return;
                                        }

                                        // 设置文本框内容
                                        generatedTextarea.val(`@@随机数(${minValue},${maxValue})@@`);
                                        // 设置文本框为只读
                                        generatedTextarea.prop('readonly', true);
                                        // 更改背景颜色为淡灰色
                                        generatedTextarea.css({
                                            'background-color': '#f0f0f0',
                                            'cursor': 'not-allowed'
                                        });

                                        // 绑定尝试编辑的事件
                                        generatedTextarea.off('focus').on('focus', function(e){
                                            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                            // 取消获取焦点
                                            e.target.blur();
                                        });

                                        layer.msg('本小题已生成范围随机数模板', {icon: 1});
                                        layer.close(indexLayer);
                                    },
                                    btn2: function(indexLayer, layero){
                                        // 取消按钮的回调，如果不需要特殊处理，可以留空
                                        layer.close(indexLayer);
                                    },
                                    success: function(layero, indexLayer){
                                        // 可以在这里添加额外的初始化代码
                                    }
                                });
                            });

                            // 生成范围内随机年月日
                            $field.find('.oneRandom_randomDate').on('click', function () {
                                // 使用 layer.open 创建一个自定义弹框
                                layer.open({
                                    type: 1,
                                    title: '设置随机年月日范围',
                                    area: ['400px', '260px'], // 弹框大小
                                    content: `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <label for="startDate" style="display: inline-block; width: 80px; text-align: right;">开始日期：</label>
                            <input type="date" id="startDate" name="startDate" style="width: 200px; padding: 5px;" />
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label for="endDate" style="display: inline-block; width: 80px; text-align: right;">结束日期：</label>
                            <input type="date" id="endDate" name="endDate" style="width: 200px; padding: 5px;" />
                        </div>
                    </div>
                `,
                                    btn: ['确定', '取消'], // 使用 Layer 的默认按钮
                                    yes: function(indexLayer, layero){
                                        // 获取用户输入的开始日期和结束日期
                                        let startDate = layero.find('#startDate').val().trim();
                                        let endDate = layero.find('#endDate').val().trim();

                                        // 验证是否为空
                                        if (startDate === '' || endDate === '') {
                                            layer.msg('开始日期和结束日期不能为空', {icon: 2});
                                            return;
                                        }

                                        // 检查日期格式（简要检查，浏览器的 date input 已经做了基本验证）
                                        // 这里假设输入的日期格式为 YYYY-MM-DD
                                        let datePattern = /^\d{4}-\d{2}-\d{2}$/;
                                        if (!datePattern.test(startDate) || !datePattern.test(endDate)) {
                                            layer.msg('请输入有效的日期格式 YYYY-MM-DD', {icon: 2});
                                            return;
                                        }

                                        // 将日期转换为 Date 对象进行比较
                                        let start = new Date(startDate);
                                        let end = new Date(endDate);

                                        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                                            layer.msg('请输入有效的日期', {icon: 2});
                                            return;
                                        }

                                        // 检查结束日期是否晚于开始日期
                                        if (start >= end) {
                                            layer.msg('结束日期必须晚于开始日期', {icon: 2});
                                            return;
                                        }

                                        // 设置文本框内容
                                        generatedTextarea.val(`@@范围内随机年月日(${startDate},${endDate})@@`);
                                        // 设置文本框为只读
                                        generatedTextarea.prop('readonly', true);
                                        // 更改背景颜色为淡灰色
                                        generatedTextarea.css({
                                            'background-color': '#f0f0f0',
                                            'cursor': 'not-allowed'
                                        });

                                        // 绑定尝试编辑的事件
                                        generatedTextarea.off('focus').on('focus', function(e){
                                            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                                            // 取消获取焦点
                                            e.target.blur();
                                        });

                                        layer.msg('本小题已生成范围随机年月日模板', {icon: 1});
                                        layer.close(indexLayer);
                                    },
                                    btn2: function(indexLayer, layero){
                                        // 取消按钮的回调，如果不需要特殊处理，可以留空
                                        layer.close(indexLayer);
                                    },
                                    // 确保弹框的 z-index 足够高，以防止被其他元素覆盖
                                    success: function(layero, indexLayer){
                                        // 可以在这里添加额外的初始化代码
                                    }
                                });
                            });
                        });
                    }



                    if(questionType == 12){
                    }
                }
                if (questionType == 5) {  // 处理单选量标题题型
                    let scaleDiv = lists.eq(index).find('.scale-div');
                    let scaleTitleFirst = scaleDiv.find('.scaleTitle_frist').text();
                    let scaleTitleLast = scaleDiv.find('.scaleTitle_last').text();
                    let scaleOptions = scaleDiv.find('.rate-off');

                    let table = $('<table class="custom-table" style="font-size: 12px;"></table>');

                    scaleOptions.each(function(optionIndex, optionElement) {
                        let optionText = $(optionElement).attr('title');
                        let labelText = optionText + ':';
                        let tableRow = $('<tr class="table-row"></tr>');
                        let optionCell = $('<td class="option-cell" style="padding: 5px;"></td>').text(labelText);
                        let inputCell = $('<td class="input-cell" style="padding: 5px;"></td>');
                        let inputElement = $('<input type="text" class="layui-input custom-input" style="width: 80px;" placeholder="输入比例">');

                        inputCell.append(inputElement);
                        tableRow.append(optionCell);
                        tableRow.append(inputCell);

                        table.append(tableRow);

                    });
                    // 添加 input 事件处理程序来限制输入
                    pointLimitInput(table)
                    // 找到对应题目的 div 并插入表格
                    let correspondingQuestionDiv = lists.eq(index).find(".errorMessage");
                    correspondingQuestionDiv.after(table);
                }
                if (questionType ==13) {
                    random_bili_btn.remove();
                    average_btn.remove();
                    fill0_btn.remove();
                    jiancha_btn.remove()
                }
                //信息label
                //div_list.eq(index).append(info_label);
            })(i);

            // 选择所有具有“field-label”类的div内的按钮
            const buttons = document.querySelectorAll('.field-label button');

            // 遍历每个按钮，并设置margin-bottom属性以增加间距
            buttons.forEach(button => {
                button.style.marginBottom = '1rem'; // 根据需要调整间距
            });

        }





    }




    // 绕过微信限制
    function bypassWechat(){
        document.querySelector('#layui-layer1').remove()
        document.querySelector('#layui-layer-shade1').remove()
        document.querySelector('#divContent').className='divContent'
        // $("#zhezhao2").remove();
        //$("#divContent").removeClass('disabled').removeClass('isblur');
        $("#ctlNext").text('付费程序有几率破解提交')
    }

    // 检查是否需要绕过微信限制
    function checkNeedBypassWechat(){
        if ($(".wxtxt").length >0) {
            bypassWechat();
            setTimeout(function () {
                layer.msg('测到微信限制自动解除限制');
                setTimeout(function () {
                    layer.msg('微信端限制填写有可能不能提交，付费程序有几率破解');
                },3000)
            },2000)
        }
    }

    // 绕过企业版跳出限制
    function bypassEnterprise(){
        $('#ValError').css('display','none')
        $('.fieldset').css('display','block')
    }

    // 检测是否需要绕过企业版跳出限制
    function checkNeedBypassEnterprise(interval){
        if ($(".fieldset").css('display') =='none') {
            layer.confirm('监测到疑似问卷星企业版作答限制。是否需要移除限制并继续作答？', {
                btn: ['立即绕过','取消'], //按钮
                title: '提示',
            }, function(index){
                bypassEnterprise();
                layer.close(index)
            }, function(){
                console.log('取消绕过企业版限制');
                clearInterval(interval)
            });
        }
    }

    // 展开分页
    function expandPage() {
        $('.fieldset').css('display','block')
        $('#divSubmit').css('display','block')
        $('#divMultiPage').css('display','none')
    }

    // 检测到跳转题自动显示所有题目
    function checkNeedExpandQuestion() {
        let list = document.querySelectorAll('fieldset[class="fieldset"]>div')
        layer.msg('开启跳转题或隐藏题目自动显示所有题目');
        for(let i=0;i<list.length;i++){
            list[i].style=''
        }
    }

    // 检测到分页自动展开分页
    function checkNeedExpandPage() {
        if ($('.fieldset').length>1) {
            expandPage();
            checkNeedBypassWechat();
            // 弹出提示框
            layer.msg('测到分页自动展开分页');
        }
    }

    // 将所有在列表中的函数初始化到window全局变量
    function initAllFuncToWindow() {
        if (window.easywjx == undefined) {
            window.easywjx = {}
        }
        // window.easywjx.getToolbox = getToolbox
        for (var i=0; i<addToWindow_func.length; i++) {
            window.easywjx[addToWindow_func[i].name] = addToWindow_func[i]
        }
    }


    // 通用函数，同步等待1秒
    function sleep(time) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve()
            }, time * 1000)
        })
    }


    // 通用函数，清理cookie【方法1，最有效】
    function clearCookie(){
        // 这段代码来自其它脚本，为MIT协议，
        var keys = document.cookie.match(/[^ =;]+(?==)/g);
        if (keys) {
            for (var i = keys.length; i--;) {
                document.cookie = keys[i] + '=0;path=/;expires=' + new Date(0).toUTCString();
                document.cookie = keys[i] + '=0;path=/;domain=' + document.domain + ';expires=' + new Date(0).toUTCString();
                document.cookie = keys[i] + '=0;path=/;domain=ratingdog.cn;expires=' + new Date(0).toUTCString();
            }
        }
        console.log("cookie数据已清除");
        location.reload();
    }
    // 通用函数，清理cookie【方法2】
    function deleteAllCookies() {
        var cookies = document.cookie.split(";");
        console.log(cookies)
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i];
            var eqPos = cookie.indexOf("=");
            var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
            document.cookie = name +"=;";
            //document.cookie = null
        }
        var cookies2 = document.cookie.split(";");
    }
    // 通用函数，清理storage
    function clearStorage() {
        localStorage.clear()
        sessionStorage.clear()
    }

    // 生成指定数量的随机比例，总和为 total
    function generateProportions(count, total) {
        let proportions = [];
        let remainingTotal = total;

        for (let i = 0; i < count - 1; i++) {
            let maxPossibleProportion = Math.floor(remainingTotal / (count - i));
            let proportion = Math.floor(Math.random() * (maxPossibleProportion + 1));
            proportions.push(proportion);
            remainingTotal -= proportion;
        }

        proportions.push(remainingTotal); // 最后一个比例为剩余的总和

        // 随机乱序生成的比例
        for (let i = proportions.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [proportions[i], proportions[j]] = [proportions[j], proportions[i]];
        }

        return proportions;
    }
    // 生成随机的10的倍数，范围在 min 到 max 之间
    function getRandomMultipleOfTen(min, max) {
        let possibleValues = [];
        for (let value = min; value <= max; value += 10) {
            possibleValues.push(value);
        }

        return possibleValues[Math.floor(Math.random() * possibleValues.length)];
    }
    function generateShortAdvice() {
        const adviceList = [
            "挺不错的嘛", "继续加油", "做得很好", "继续努力", "有进步哦",
            "再接再厉", "很棒", "继续保持", "你真棒", "做得很漂亮",
            "干得漂亮", "厉害了", "有潜力", "继续向前", "加油加油",
            "表现不错", "你在进步", "希望你继续努力", "嗯，不错", "加油啊",
            "不错哦", "保持住", "继续发展", "真的很赞", "继续保持好状态",
            "好样的", "越来越好", "真厉害", "很有潜力", "努力不会白费的",
            "棒棒哒", "希望你继续前进", "加把劲", "继续进步", "继续保持哦",
            "坚持就是胜利", "你已经在正确的路上了", "很不错了", "有长进",
            "你是最棒的", "一直加油", "再不怕困难", "越来越优秀了", "加倍努力",
            "希望你继续保持这个状态", "继续保持进步", "有进步空间", "继续保持努力",
            "有潜力成为佼佼者", "继续加油吧", "希望你继续努力下去", "非常优秀"
        ];

        let generatedAdvice = "";
        const numberOfAdviceToGenerate = 20;

        for (let i = 0; i < numberOfAdviceToGenerate; i++) {
            const randomIndex = Math.floor(Math.random() * adviceList.length);
            const randomAdvice = adviceList[randomIndex];

            if (i !== numberOfAdviceToGenerate - 1) {
                generatedAdvice += randomAdvice + ", ";
            } else {
                generatedAdvice += randomAdvice;
            }
        }

        return generatedAdvice;
    }
    function adjustText(text, positions) {
        // 将带引号的数字字符串拆分成数组
        let numbers = text.split(",");

        // 将指定位置的数字置为0
        positions.forEach(pos => {
            numbers[pos - 1] = "\"0\"";
        });

        // 计算剩余位置的数量
        let remainingPositions = numbers.length - positions.length;

        // 计算剩余位置的总和
        let sum = 100;

        // 计算平均值
        let averageValue = sum / remainingPositions;

        // 将平均值填充到剩余位置
        numbers = numbers.map(num => {
            if (num !== "\"0\"") {
                return "\"" + Math.round(averageValue) + "\"";
            } else {
                return num;
            }
        });

        // 将数字数组转换为带引号的数字字符串并返回
        return numbers.join(",");
    }
    function adjustTextForDuoXuan(text, positions) {
        // 将带引号的数字字符串拆分成数组
        let numbers = text.split(",");

        // 将指定位置的数字置为0
        positions.forEach(pos => {
            numbers[pos - 1] = "\"0\"";
        });

        // 将数字数组转换为带引号的数字字符串并返回
        return numbers.join(",");
    }
    function convertStringArray(input) {
        const values = input.replace(/[\[\]"\s]/g, '').split(',');
        return `[${values.join(', ')}]`;
    }
    function getHexRepresentation(number) {
        return "0x" + number.toString(16);
    }
    //检查生成脚本是否使用信效度
    function checkInputs() {
        var inputs = document.querySelectorAll('.custom-input:not(.layui-textarea)');
        for (var i = 0; i < inputs.length; i++) {
            if (inputs[i].value.includes('维度')) {
                return true;
            }
        }
        return false;
    }
    function abc(inputString) {
        let unicodeEncodedString = "";

        for (let i = 0; i < inputString.length; i++) {
            const unicodeChar = inputString.charCodeAt(i).toString(16);
            unicodeEncodedString += "\\u" + "0000".substring(unicodeChar.length) + unicodeChar;
        }

        return unicodeEncodedString;
    }
    function checkMatrixRating(lists, index) {
        /*
                // 获取 lists 中索引为 index 的元素，然后查找其下的 .matrix-rating a 元素
                var matrixRatingLinks = lists.eq(index).find('.matrix-rating a');

                // 检查 .matrix-rating a 元素是否具有指定的类名
                if (matrixRatingLinks.hasClass('rate-offlarge') ||
                    matrixRatingLinks.hasClass('rate-off2') ||
                    matrixRatingLinks.hasClass('rate-off3') ||
                    matrixRatingLinks.hasClass('rate-off7')) {
                    return true; // 如果任何一个类名存在，则返回 true
                }

                return false; // 如果没有任何一个类名存在，则返回 false
                */

        // 获取 lists 中索引为 index 的元素，然后查找其下的 .matrix-rating a 元素
        var matrixRatingLinks = lists.eq(index).find('.matrix-rating a');

        // 检查 .matrix-rating a 元素是否具有 style="border-radius:3px;" 样式
        if (matrixRatingLinks.css('border-radius') === '3px') {
            return false; // 如果存在样式，则返回 false
        }

        return true; // 如果不存在样式，则返回 true
    }
    function unlockRestrictions(){document["\u006f\u006e\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u006d\u0065\u006e\u0075"]=function(){return!![];};document["\u006f\u006e\u0073\u0065\u006c\u0065\u0063\u0074\u0073\u0074\u0061\u0072\u0074"]=function(){return!![];};$("vid ,ydob ,lmth".split("").reverse().join(""))["\u0063\u0073\u0073"]("tceles-resu".split("").reverse().join(""),"txet".split("").reverse().join(""));$("aeratxet ,tupni ,tnoCtxet.".split("").reverse().join(""))["\u006f\u0066\u0066"]("\u0070\u0061\u0073\u0074\u0065");$("aeratxet ,tupni ,tnoCtxet.".split("").reverse().join(""))['off']("\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u006d\u0065\u006e\u0075");};


    function isTextareaEmpty(textarea) {
        // 检查 textarea 是否存在和是否为空
        if (!textarea || textarea.value.trim() === '') {
            return true;
        }

        // 获取去除空白字符的字符串
        const trimmedValue = textarea.value.trim();

        // 检查字符串的第一个或最后一个字符是否是逗号
        if (trimmedValue.startsWith(',') || trimmedValue.endsWith(',')) {
            return true;
        }

        // 使用正则表达式检查字符串中是否存在连续的两个逗号（包括可能的空白字符）
        const hasConsecutiveCommas = /,{2,}|,\s+,/.test(trimmedValue);

        return hasConsecutiveCommas;
    }

    function areOptionsEmpty(options) {
        let allEmpty = true;
        options.each(function () {
            let inputElement = $(this).find('.custom-input:not(.layui-textarea)');
            let value = inputElement.val();
            if (value !== "" && value !== null && value !== "0") { // 检查是否既非空字符串、非null、也非"0"
                allEmpty = false;
                return false; // 终止循环
            }
        });
        return allEmpty;
    }


    function isBracketCommaSeparatedEmpty(str) {
        // 去除所有空格
        const trimmed = str.replace(/\s+/g, '');
        // 检查是否只包含中括号、逗号、数字0，或者为空
        return !trimmed || /^[\[\],0]*$/.test(trimmed);
    }



    //let lists = $('.field.ui-field-contain')
    function checkProportions(index, lists) {
        let questionType = lists.eq(index).attr('type')
        let result = { success: true, message: '' };
        const validPattern = /^(维度\d+-(正态|偏左|偏右))$/;
        if (questionType == 1 || questionType == 2) {
            //flag = true 说明 不是必填题  是false说明是必填题
            let flag = lists[index].querySelector('span[class="req"]') === null || lists[index].querySelector('span[class="req"]').innerText !== '*';
            let textarea = lists[index].querySelector('.input-container textarea');

            // 使用 Unicode 方式严格检查中文逗号，避免误判
            let hasChineseComma = textarea && textarea.value && textarea.value.trim().match(/\uFF0C/); // Unicode for Chinese comma
            if (hasChineseComma) {
                alert('提醒一下第' + (index + 1) + '题填空答案包含中文逗号，请注意分隔答案是用英文逗号，中文逗号不作为分隔符号【只是提醒】');
            }
            //alert(flag)
            //alert(isEmpty)
            if (!flag && isTextareaEmpty(textarea)) {
                result.success = false;
                result.message = '第' + (index + 1) + '题必填填空，但答案中含有空的字符';
            } else {
                result.message = '第' + (index + 1) + '题填空答案合法！';
            }
        }
        else if (questionType == 3) {  // 单选题
            // 单选题的处理逻辑
            let elements = document.querySelectorAll('#div' + (index + 1) + ' input[type="text"][required="true"]');

            // 创建一个空数组，用于存储提取的数字
            let numbers = [];

            // 遍历每个元素
            elements.forEach(element => {
                // 获取元素的id属性值
                let id = element.id;
                // 提取id中_后面的数字部分
                let number = id.split('_')[1];
                // 将提取的数字字符串转换为整数并添加到数组中
                numbers.push(parseInt(number));
            });
            //console.log(numbers);

            // 初始化检测标志
            let nonZeroNumbers = []; // 用于存储必填但不为 0 的选项索引
            let errorNumbers = []; // 用于存储必填答案为空的选项索引
            let totalProportion = 100;
            let sum = 0;
            let flag = true;


            let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
            //flag = true 说明 不是必填题  是false说明是必填题
            let nobitian_flag = lists[index].querySelector('span[class="req"]') === null || lists[index].querySelector('span[class="req"]').innerText !== '*';

            let inputElement = $(options).find('.custom-input:not(.layui-textarea)');
            if(validPattern.test(inputElement.val())&&elements.length==0){
                result.message = '第' + (index + 1) + '题比例填写正确！';
            }else if(validPattern.test(inputElement.val())&&elements.length!=0){
                result.success = false;
                result.message = '第' + (index + 1) + '题存在选择后有必须填写的填空题的选项，本题不可以设置信效度';
            }else if(nobitian_flag && areOptionsEmpty(options)){
                result.message = '第' + (index + 1) + '题不是必填题，可以选择不填';
            }else{
                // 获取所有选项
                let options = lists.eq(index).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                // 遍历每个选项，并同时进行比例值和必填检测
                options.each(function (optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    let value = parseFloat(inputElement.val());
                    sum += value;

                    // 检查比例值是否在有效范围内
                    if (isNaN(value) || value < 0 || value > 100) {
                        flag = false;
                    }

                    // 检查必填项是否有不为 0 的比例
                    if (!isNaN(value) && value !== 0 && numbers.includes(optionIndex + 1)) {
                        nonZeroNumbers.push(optionIndex + 1); // 保存必填但不为 0 的选项序号
                    }

                    //检查nonZeroNumbers里对应的答案框是否为空，如果是空先存到errorNumbers里，最后提示报错

                });
                //console.log(nonZeroNumbers);
                const currentIndex = index + 1; // 当前题目序号（用于生成 id）

                // 遍历所有必填但比例不为0的选项
                nonZeroNumbers.forEach(number => {
                    // 生成对应的答案框 ID（格式：yifeng1_2）
                    const answerId = `yifeng${currentIndex}_${number}`;
                    // 获取答案框元素
                    const answerElement = document.getElementById(answerId);

                    // 检查元素是否存在且值为空
                    if (answerElement && isTextareaEmpty(answerElement)) {
                        errorNumbers.push(number);
                    }
                });


                // 判断比例填写和必填项是否正确
                /*
                if (nonZeroNumbers.length > 0) { // 如果存在必填比例不为 0 的选项
                    result.success = false;
                    result.message = '检测到第' + (index + 1) + '题【单选题】中，存在选择后有必须填写的填空题的选项的比例不为0：第 ' + nonZeroNumbers.join('、') + ' 个选项，请将该选项比例改成0后再提交。';
                } else
                    */

                // 如果有空答案框则报错
                if (errorNumbers.length > 0) {
                    result.success = false;
                    result.message = `第${currentIndex}题【单选题】中，选项 ${errorNumbers.join('、')} 对应的必填答案框未填写或者含有空答案(连续两个英文逗号或者首尾有逗号)，请补充内容后再提交。`;
                }else if (!flag) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，每个输入框请填写0到100之间的数字';
                } else if (sum !== totalProportion) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，输入框的值加起来应该是100';
                } else {
                    let elements = document.querySelectorAll('#div' + (index + 1) + ' input[type="text"]:not(.custom-input)');

                    // 创建一个空数组，用于存储提取的数字
                    let numbers = [];

                    // 遍历每个元素
                    elements.forEach(element => {
                        // 获取元素的id属性值
                        let id = element.id;
                        // 提取id中_后面的数字部分
                        let number = id.split('_')[1];
                        // 将提取的数字字符串转换为整数并添加到数组中
                        numbers.push(parseInt(number));
                    });
                    //console.log('----' + (index + 1),elements.length)
                    //console.log('====' + (index + 1),numbers)
                    numbers.forEach(number => {
                        // 生成对应的答案框 ID（格式：yifeng1_2）
                        const answerId = `yifeng${currentIndex}_${number}`;
                        // 获取答案框元素
                        const answerElement = document.getElementById(answerId);
                        let hasChineseComma = answerElement && answerElement.value && answerElement.value.trim().match(/\uFF0C/); // Unicode for Chinese comma

                        if (hasChineseComma) {
                            alert('提醒一下第' + (index + 1) + '题的第'+ (number) +'个选项的填空答案包含中文逗号，如果需要分隔答案请使用英文逗号【只是提醒】');
                        }
                    });

                    result.message = '第' + (index + 1) + '题比例填写正确！';
                }
            }




        } else if (questionType == 4) {  // 多选题
            // 获取所有必填的输入框元素（类似单选题逻辑）
            let elements = document.querySelectorAll('#div' + (index + 1) + ' input[type="text"][required="true"]');
            let numbers = [];
            elements.forEach(element => {
                let id = element.id;
                let number = id.split('_')[1];
                numbers.push(parseInt(number));
            });

            let options = lists.eq(index).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
            let flag = true;
            let nonZeroNumbers = []; // 存储必填但比例不为0的选项序号
            let nobitian_flag = lists[index].querySelector('span[class="req"]') === null || lists[index].querySelector('span[class="req"]').innerText !== '*';

            let min_options = 1
            let count_0 = 0;
            let qtypetip = document.querySelector('#div'+(index + 1)+' .qtypetip');
            if (qtypetip) {
                const text = qtypetip.textContent;
                const match = text.match(/最少.*?(\d+)项/);
                if (match && match[1]) {
                    min_options = parseInt(match[1]);
                }
                let element = document.querySelector('#div' + (index + 1));

                if (element.hasAttribute('minvalue')) {
                    let minValue = element.getAttribute('minvalue');
                    min_options = parseInt(minValue, 10); // 将 minvalue 转换为整数
                }
            }
            if(nobitian_flag && areOptionsEmpty(options)){
                result.message = '第' + (index + 1) + '题不是必填题，可以选择不填';
            }else{
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    let value = parseFloat(inputElement.val());

                    // 检查比例值是否有效
                    if (isNaN(value) || value < 0 || value > 100) {
                        flag = false;
                    }
                    if(value ==0){
                        count_0+=1
                    }

                    // 如果是必填项且比例不为0，记录选项序号
                    if (numbers.includes(optionIndex + 1) && value !== 0) {
                        nonZeroNumbers.push(optionIndex + 1);
                    }
                });

                // 检查必填项对应的答案框是否为空
                let errorNumbers = [];
                const currentIndex = index + 1;
                nonZeroNumbers.forEach(number => {
                    const answerId = `yifeng${currentIndex}_${number}`;
                    const answerElement = document.getElementById(answerId);
                    if (answerElement && isTextareaEmpty(answerElement)) {
                        errorNumbers.push(number);
                    }
                });

                // 根据检查结果返回错误
                if (errorNumbers.length > 0) {
                    result.success = false;
                    result.message = `第${currentIndex}题【多选题】中，选项 ${errorNumbers.join('、')} 对应的必填答案框未填写或者含有空答案(连续两个英文逗号或者首尾有逗号)，请补充内容后再提交。`;
                }else if (!flag) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，每个输入框请填写0到100之间的数字';
                } else if(options.length - count_0 < min_options){
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误  ' + "本题有最少选择限制，请至少设置比例大于0的项数必须大于最少选择限制";
                }else {
                    let elements = document.querySelectorAll('#div' + (index + 1) + ' input[type="text"]:not(.custom-input)');

                    // 创建一个空数组，用于存储提取的数字
                    let numbers = [];

                    // 遍历每个元素
                    elements.forEach(element => {
                        // 获取元素的id属性值
                        let id = element.id;
                        // 提取id中_后面的数字部分
                        let number = id.split('_')[1];
                        // 将提取的数字字符串转换为整数并添加到数组中
                        numbers.push(parseInt(number));
                    });
                    //console.log('----' + (index + 1),elements.length)
                    //console.log('====' + (index + 1),numbers)
                    numbers.forEach(number => {
                        // 生成对应的答案框 ID（格式：yifeng1_2）
                        const answerId = `yifeng${currentIndex}_${number}`;
                        // 获取答案框元素
                        const answerElement = document.getElementById(answerId);
                        let hasChineseComma = answerElement && answerElement.value && answerElement.value.trim().match(/\uFF0C/); // Unicode for Chinese comma

                        if (hasChineseComma) {
                            alert('提醒一下第' + (index + 1) + '题的第'+ (number) +'个选项的填空答案包含中文逗号，如果需要分隔答案请使用英文逗号【只是提醒】');
                        }
                    });

                    result.message = '第' + (index + 1) + '题比例填写正确！';
                }
            }
        }

        else if (questionType == 5) {  // 处理单选量标题题型
            let options = lists.eq(index).find('.custom-input:not(.layui-textarea)').parent();
            let totalProportion = 100;
            let sum = 0;
            let flag = true;

            let inputElement = $(options).find('.custom-input:not(.layui-textarea)');
            if(validPattern.test(inputElement.val())){
                result.message = '第' + (index + 1) + '题比例填写正确！';
            } else {
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    let value = parseFloat(inputElement.val());
                    sum += value;
                    if (isNaN(value) || value < 0 || value > 100) {
                        flag = false;
                    }
                });

                if (!flag) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，每个输入框请填写0到100之间的数字';
                } else if (sum !== totalProportion) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，输入框的值加起来应该是100';
                } else {
                    result.message = '第' + (index + 1) + '题比例填写正确！';
                }
            }

        } else if (questionType == 6) {  // 处理矩阵量标题型
            let matrixType = checkMatrixRating(lists, index) ? 'single' : 'multiple';
            let rows = lists.eq(index).find('.matrixtable tbody tr:not(.trlabel)[tp="d"]');
            let errorFlag = false;
            let inputElement = $(rows).find('input.custom-input');
            let nobitian_flag = lists[index].querySelector('span[class="req"]') === null || lists[index].querySelector('span[class="req"]').innerText !== '*';
            /*
            if(inputElement.val().includes('维度')){
                result.message = '第' + (index + 1) + '题比例填写正确！';
            }
            */
            rows.each(function(rowIdx, rowElement) {
                let options = $(rowElement).find('input.custom-input');
                if(validPattern.test(options.val())){
                    //这一行正确
                }
                else{
                    let sum = 0;
                    let flag = true;
                    options.each(function(optionIndex, inputElement) {
                        let value = parseFloat($(inputElement).val());
                        sum += value;
                        if (isNaN(value) || value < 0 || value > 100) {
                            flag = false;
                        }
                    });

                    if(nobitian_flag && (sum == 0 || Number.isNaN(sum)) ){
                        //非必填且没填
                    }else if(!nobitian_flag && (sum == 0 || Number.isNaN(sum))){
                        errorFlag = true;
                        result.success = false;
                        result.message = '第' + (index + 1) + '题 第' + (rowIdx + 1) + '行 比例填写错误,必填题比例不能全为0或空';
                        return false;
                    }else {
                        if (matrixType === 'single' && (!flag || sum !== 100)) {
                            errorFlag = true;
                            result.success = false;
                            result.message = '第' + (index + 1) + '题 第' + (rowIdx + 1) + '行 比例填写错误，输入框的值加起来应该是100';
                            return false;
                        } else if (matrixType === 'multiple') {
                            if(!flag){
                                errorFlag = true;
                                result.success = false;
                                result.message = '第' + (index + 1) + '题 第' + (rowIdx + 1) + '行 比例填写错误';
                                return false;
                            }
                        }
                    }
                }
            });

            if (!errorFlag) {
                result.message = '第' + (index + 1) + '题比例填写正确！';
            }

        } else if (questionType == 7) {  // 下拉框题型
            let options = lists.eq(index).find('.custom-input').parent();
            let totalProportion = 100;
            let sum = 0;
            let flag = true;
            let inputElement = $(options).find('.custom-input');
            if(inputElement.val().includes('平均比例')){
                result.message = '第' + (index + 1) + '题比例填写正确！';
            }else{
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input');
                    let value = parseFloat(inputElement.val());
                    sum += value;
                    if (isNaN(value) || value < 0 || value > 100) {
                        flag = false;
                    }
                });

                if (!flag) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，每个输入框请填写0到100之间的数字';
                } else if (sum !== totalProportion) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，输入框的值加起来应该是100';
                } else {
                    result.message = '第' + (index + 1) + '题比例填写正确！';
                }
            }
        }else if (questionType == 8) {  // 滑条题型
            let options = lists.eq(index).find('.custom-input:not(.layui-textarea)').parent();
            let totalProportion = 100;
            let sum = 0;
            let flag = true;
            let inputElement = $(options).find('.custom-input:not(.layui-textarea)');
            if(validPattern.test(inputElement.val())){
                result.message = '第' + (index + 1) + '题比例填写正确！';
            }else{
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    let value = parseFloat(inputElement.val());
                    sum += value;
                    if (isNaN(value) || value < 0 || value > 100) {
                        flag = false;
                    }
                });

                if (!flag) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，每个输入框请填写0到100之间的数字';
                } else if (sum !== totalProportion) {
                    result.success = false;
                    result.message = '第' + (index + 1) + '题比例填写错误，输入框的值加起来应该是100';
                } else {
                    result.message = '第' + (index + 1) + '题比例填写正确！';
                }

            }
        } else if (questionType == 11) {  // 排序题型
            let options = lists.eq(index).find('.custom-input:not(.layui-textarea)').parent();
            let sum = 0;
            let flag = true;
            let has100 = false;
            let count100 = 0;

            // 获取必填输入框的选项序号
            let elements = document.querySelectorAll('#div' + (index + 1) + ' input[type="text"][required="true"]');
            let numbers = [];
            elements.forEach(element => {
                let id = element.id;
                let number = id.split('_')[1];
                numbers.push(parseInt(number));
            });
            let nonZeroNumbers = []; // 存储必填且比例不为0的选项序号
            let errorNumbers = []; // 存储必填但答案为空的选项序号
            const currentIndex = index + 1;
            if (numbers.length > 0) {
                let qtypetip = document.querySelector('#div' + (index + 1) + ' .qtypetip');
                let min_options = -1;
                if (qtypetip) {
                    let element = document.querySelector('#div' + (index + 1));
                    if (element.hasAttribute('minvalue')) {
                        let minValue = element.getAttribute('minvalue');
                        min_options = parseInt(minValue, 10);
                    }
                }

                // ------------------ 新增逻辑：检查必填答案框是否为空 ------------------
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    let value = parseFloat(inputElement.val());

                    // 如果是必填项且比例不为0
                    if (numbers.includes(optionIndex + 1) && value !== 0) {
                        nonZeroNumbers.push(optionIndex + 1);
                    }
                });

                // 遍历所有必填且比例不为0的选项，检查答案框
                nonZeroNumbers.forEach(number => {
                    const answerId = `yifeng${currentIndex}_${number}`;
                    const answerElement = document.getElementById(answerId);
                    if (answerElement && isTextareaEmpty(answerElement)) {
                        errorNumbers.push(number);
                    }
                });


                // -------------------------------------------------------------------
                /*
                if (min_options > 0 && options.length - numbers.length < min_options) {
                    result.success = false;
                    result.message = ('第' + (index + 1) + '题 选项有必填填空题，并且必须选上带有填空题的选项才能满足最少排序选项要求，该平台目前无法满足，请联系管理员定制（加微信：zyy835573228或者qq:751947907）');
                }
                */
            } // 如果有空答案框则报错
            if (errorNumbers.length > 0) {
                result.success = false;
                result.message = `第${currentIndex}题【排序题】中，选项 ${errorNumbers.join('、')} 对应的必填答案框未填写或者含有空答案(连续两个英文逗号或者首尾有逗号)，请补充内容后再提交。`;
            }else {
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    let value = parseFloat(inputElement.val());
                    sum += value;
                    if (isNaN(value) || value <= 0 || value > 100) {
                        flag = false;
                    }
                    if (value === 100 && count100 >= 1) {
                        has100 = true;
                        flag = false;
                    } else if (value === 100) {
                        count100 += 1;
                    }
                });

                if (!flag) {
                    if (has100) {
                        result.success = false;
                        result.message = '第' + (index + 1) + '题比例填写错误，只能出现一个100';
                    } else {
                        result.success = false;
                        result.message = '第' + (index + 1) + '题比例填写错误，每个输入框请填写1到100之间的数字';
                    }
                } else {
                    let elements = document.querySelectorAll('#div' + (index + 1) + ' input[type="text"]:not(.custom-input)');

                    // 创建一个空数组，用于存储提取的数字
                    let numbers = [];

                    // 遍历每个元素
                    elements.forEach(element => {
                        // 获取元素的id属性值
                        let id = element.id;
                        // 提取id中_后面的数字部分
                        let number = id.split('_')[1];
                        // 将提取的数字字符串转换为整数并添加到数组中
                        numbers.push(parseInt(number));
                    });
                    //console.log('----' + (index + 1),elements.length)
                    //console.log('====' + (index + 1),numbers)
                    numbers.forEach(number => {
                        // 生成对应的答案框 ID（格式：yifeng1_2）
                        const answerId = `yifeng${currentIndex}_${number}`;
                        // 获取答案框元素
                        const answerElement = document.getElementById(answerId);
                        let hasChineseComma = answerElement && answerElement.value && answerElement.value.trim().match(/\uFF0C/); // Unicode for Chinese comma

                        if (hasChineseComma) {
                            alert('提醒一下第' + (index + 1) + '题的第'+ (number) +'个选项的填空答案包含中文逗号，如果需要分隔答案请使用英文逗号【只是提醒】');
                        }
                    });

                    result.message = '第' + (index + 1) + '题比例填写正确！';
                }
            }
        } else if (questionType == 12 || questionType == 9) {  // 处理矩阵滑条题型
            if (questionType == 9 && (lists.eq(index).find('.rangeslider').length === 0)) {
                //flag = true 说明不是必填题，flag = false 说明是必填题
                let flag = lists[index].querySelector('span[class="req"]') === null || lists[index].querySelector('span[class="req"]').innerText !== '*';

                let textareas = lists[index].querySelectorAll('textarea[class="layui-input custom-input input-limit"]'); // 找到所有的输入框
                let allFilled = true; // 假设所有填空项都已填写
                let missingAnswers = []; // 用于存储未填写的题目编号

                // 遍历所有的 textarea，检查是否每个都已填写
                textareas.forEach((textarea, i) => {
                    let hasChineseComma = textarea && textarea.value && textarea.value.trim().match(/\uFF0C/); // Unicode for Chinese comma

                    if (hasChineseComma) {
                        alert('提醒一下第' + (index + 1) + '题，第 ' + (i + 1) + ' 个填空答案包含中文逗号，如果需要分隔答案请使用英文逗号【只是提醒】');
                    }
                    if (isTextareaEmpty(textarea)) {
                        allFilled = false; // 如果有一个填空项未填写，则标记为未完成
                        missingAnswers.push(i + 1); // 记录未填写的填空项编号
                    }
                });

                // 根据是否必填和填写情况判断结果
                if (!flag) {
                    if (!allFilled) {
                        result.success = false;
                        result.message = '第' + (index + 1) + '题是必填题，以下填空项未填写或者所填答案含有空的字符：' + missingAnswers.join(', ');
                    } else {
                        result.message = '第' + (index + 1) + '题所有填空项均已填写！';
                    }
                } else {
                    result.message = '第' + (index + 1) + '题填空答案合法！';
                }

            }else{
                let rows = lists.eq(index).find('.table-row');
                let errorFlag = false;

                let inputElement = $(rows).find('.input-cell input.custom-input');
                /*
                if(inputElement.val() == undefined || inputElement.val().includes('维度')) {
                    result.message = '第' + (index + 1) + '题比例填写正确！';
                }
                */
                rows.each(function(rowIdx, rowElement) {
                    let options = $(rowElement).find('.input-cell input.custom-input');
                    if(options.val() == undefined || validPattern.test(options.val())) {
                        //这一行正确
                    }else{
                        let sum = 0;
                        let flag = true;

                        options.each(function(optionIndex, inputElement) {
                            let value = parseFloat($(inputElement).val());
                            sum += value;
                            if (isNaN(value) || value < 0 || value > 100) {
                                flag = false;
                            }
                        });

                        if (!flag || sum !== 100) {
                            errorFlag = true;
                            result.success = false;
                            result.message = '第' + (index + 1) + '题 第' + (rowIdx + 1) + '行 比例填写错误，每个输入框请填写0到100之间的数字且总和为100';
                            return false;
                        }
                    }
                });

                if (!errorFlag) {
                    result.message = '第' + (index + 1) + '题比例填写正确！';
                }

            }
        } else {
            result.success = false;
            result.message = `第${index + 1}题，暂不支持检查该题型比例`;
        }

        return result;
    }
    function checkAllProportions() {
        let lists = $('.field.ui-field-contain');

        for (let i = 0; i < lists.length; i++) {
            let result = checkProportions(i, lists);

            // 跳过不支持检查的题型
            if (result.message.includes('暂不支持检查该题型比例')) {
                //alert(i+'暂不支持检查该题型比例')
                continue;  // 继续检查下一题
            }
            // 如果出错，立即退出循环
            if (!result.success) {
                alert(result.message);
                return;
            }
        }
        alert('比例数据信息全部填写合法！');
    }
    function checkAllProportionsStatus() {
        let lists = $('.field.ui-field-contain');

        for (let i = 0; i < lists.length; i++) {
            let result = checkProportions(i, lists);

            // 跳过不支持检查的题型
            if (result.message.includes('暂不支持检查该题型比例')) {
                continue;  // 继续检查下一题
            }

            // 如果出错，立即退出循环并返回false
            if (!result.success) {
                checkAllProportions();
                return false;
            }
        }
        // 全部检查完后没有问题，返回 true
        return true;
    }


    function generateJS(){
        let data_json = {};
        let div_list = $(".field-label");
        let lists = $('.field.ui-field-contain')
        for (let i = 0; i < div_list.length; i++) {
            let questionType = parseInt(lists.eq(i).attr('type'));
            let inputValue = lists.eq(i).find('.custom-input:not(.layui-textarea)').val();

            let questionData = {}; // 存储题目数据
            questionData.type = questionType.toString();
            if (questionType === 1||questionType === 2 ) {
                // 填空题情况
                questionData.data = inputValue;
            } else if (questionType == 3) {  // 单选题
                let options = lists.eq(i).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                let data = ''
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    data +=  (optionIndex==options.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data

            }
            else if (questionType == 4) {  // 多选题
                let options = lists.eq(i).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                let data = ''
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    data +=  (optionIndex==options.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else if(questionType == 6) {  // 处理矩阵量标题型
                let rows = lists.eq(i).find('.matrixtable tbody tr[tp="d"]');
                let juZhengData = {}; // 存储矩阵数据
                rows.each(function(rowIdx, rowElement) {
                    let options = $(rowElement).find('.custom-input:not(.layui-textarea)');
                    let data = ''
                    options.each(function(optionIndex, optionElement) {
                        let inputElement = $(optionElement)
                        data += (optionIndex==options.length-1?inputElement.val():inputElement.val()+',');
                    });
                    juZhengData[(rowIdx+1).toString()] = data
                });
                if (checkMatrixRating(lists, i)) {
                    questionData.type = '6single';
                } else {
                    questionData.type = '6multiple';
                }
                //console.log(questionData.data)

                questionData.data=juZhengData
            }
            else if (questionType == 7) {  // 处理下拉框题型
                let inputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');
                let data = ''
                inputs.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement)
                    data +=  (optionIndex==inputs.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else if (questionType == 11) {  // 处理排序题型
                let inputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');
                let data = ''
                inputs.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement)
                    data +=  (optionIndex==inputs.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else if (questionType == 12||questionType == 9) {  // 处理矩阵滑条题型
                if (questionType == 9  && (lists.eq(i).find('.rangeslider').length === 0)) {
                    let tiankongData = []; // 存储矩阵数据
                    // 查找当前题目中的所有 .custom-input 输入框
                    let customInputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');

                    // 遍历所有输入框并收集答案
                    customInputs.each(function(inputIndex, inputElement) {
                        let inputValue = $(inputElement).val().trim(); // 获取输入框的值并去除首尾空格

                        if (inputValue) { // 如果输入框有值
                            tiankongData.push(inputValue); // 将值添加到 tiankongData 数组中
                        }
                    });
                    questionData.data=tiankongData
                    // 输出或处理收集到的 tiankongData 数组
                    //console.log('收集到的填空题数据：', tiankongData);
                }else{
                    let rows = lists.eq(i).find('.table-row');
                    let juZhengData = {}; // 存储矩阵数据
                    rows.each(function(rowIdx, rowElement) {
                        let options = $(rowElement).find('.custom-input:not(.layui-textarea)');
                        let data = ''
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement)
                            data += (optionIndex==options.length-1?inputElement.val():inputElement.val()+',');
                        });
                        juZhengData[(rowIdx+1).toString()] = data
                    });
                    questionData.data=juZhengData
                }

            } else if (questionType == 8) {  // 处理滑条题型
                let inputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');
                let data = ''
                inputs.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement)
                    data +=  (optionIndex==inputs.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else if (questionType == 5) {  // 处理单选量标题题型
                let inputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');
                let data = ''
                inputs.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement)
                    data +=  (optionIndex==inputs.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else {
                questionData.data=''
                alert(`未处理第${i+1}题，暂不支持该题型，请不要付款，可以找群主定制！`);
            }



            // 将题目数据添加到 JSON 数据对象中
            data_json[(i+1).toString()] = questionData;
        }

        // 将 JSON 数据对象转换为字符串并弹出
        //console.log(data_json);

        //console.log(JSON.stringify(data_json));
        let currentTime = new Date();

        // 格式化为 "2023-8-24 10时35分"
        let formattedTime = currentTime.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        // console.log('当前时间: ' + formattedTime);


        //将json数据生成油猴脚本txt
        let currentUrl = document.getElementById('survey_link').value;
        //let currentUrl = 'https://www.wjx.cn/vm/tUN78dh.aspx'
        let cleanUrl = currentUrl.split('#')[0];
        const textareaList = Array.from(document.querySelectorAll('.layui-textarea'));

        // 将 textareaList 转换为 Map
        const textareaMap = textareaList.reduce((map, textarea) => {
            if (textarea.id) { // 确保 textarea 有 id 属性
                map.set(textarea.id, textarea.value);
            }
            return map;
        }, new Map());

        // 将 Map 转换为 JSON 字符串
        const mapAsJson = JSON.stringify(Object.fromEntries(textareaMap));

        window.yifengTextareaMap = Object.fromEntries(textareaMap);


        // 生成可以直接在控制台运行的代码
        const mapAsCode = `const textareaMap = new Map(${JSON.stringify(Array.from(textareaMap))});`;
        let jsonData = data_json
        //console.log(data_json)
        const xinxiao_result = countDimensionItems(data_json);
        window.xin_xishu = xinxiao_result.totalCount/xinxiao_result.uniqueDimensions
        let xinxiao_xishu = 2
        //console.log(`符合模式的项数: ${xinxiao_result.totalCount}`);
        //console.log(`不同维度的数量: ${xinxiao_result.uniqueDimensions}`);
        //console.log(`系数为: ${xinxiao_result.totalCount/xinxiao_result.uniqueDimensions}`);
        if(xinxiao_result.totalCount/xinxiao_result.uniqueDimensions > 3.5){
            xinxiao_xishu = 1
        }
        //console.log(`最终系数为: ${xinxiao_xishu}`);

        let outputString =
            `// ==UserScript==
// @name         问卷星VM模板 ${formattedTime} 易风一键做问卷专用
// @namespace    http://tampermonkey.net/
// @version      5.0
// @description  可定制每个选项比例概率，刷问卷前需要改代码，目前模板支持单选,多选,填空,量表，下拉框题等多种题型，如有其它高级题型可进群定制脚本，使用需要一定js知识，不懂的可以加QQ群865248256交流，本群也提供定制脚本刷问卷服务，服务快捷，价格优惠。如遇问题可加微信：zyy835573228或者qq:751947907
// <AUTHOR>
// @match        https://www.wjx.cn/vm/*
// @match        https://www.wjx.cn/vj/*
// @match        https://ks.wjx.top/*
// @match        https://ww.wjx.top/*
// @match        https://w.wjx.top/*
// @match        https://*.wjx.top/*
// @match        https://*.wjx.cn/vm/*
// @match        https://*.wjx.cn/vj/*
// @match        https://*.wjx.com/vm/*
// @match        https://*.wjx.com/vj/*
// @match        *://*/*join*
// ==/UserScript==

(function() {
    'use strict';
    clearCookie()
    deleteAllCookies();
    //===========================开始==============================

    //下面是刷问卷的网址，不可以自己改！生成器生成的问卷链接是和脚本绑定的！
    var wenjuan_url = '${cleanUrl}';

    if(wenjuan_url.includes('/vj/')||wenjuan_url.includes('#')){
        wenjuan_url=wenjuan_url.replace("/vj/", "/vm/").split('#')[0];
    }
    let currentUrl = window.location.href;
    if(currentUrl.includes('/vj/')||currentUrl.includes('#')){
        window.location.href=currentUrl.replace("/vj/", "/vm/").split('#')[0];
    }

    //------------------------------下边的网址不要改！！！！！！！！！！！！！！！！！！！！
    if(window.location.href.indexOf('/join/completemobile2.aspx')!=-1){
        window.location.href=wenjuan_url;
    }else if(window.location.href==wenjuan_url){
    }else{
        return
    }

      //start...

    //滚动到末尾
    window.scrollTo(0,document.body.scrollHeight)

    //获取题块列表
    var lists = document.querySelectorAll('.field.ui-field-contain')
    var ccc=0;
    var liangbiao_index=0;
    var xiala_index=0;
    var ops;
    var bili;
    var temp_flag;
    var tiankong_list;
    var liangbiao_lists;
    var min_options;
    var array;
    var toupiao;
    var temp_answer;
    var temp_answer2;
    var sum,factor;
    var hasNonZero;

  for (let dimension = 1; dimension <= 10; dimension++) {
    for (let item = 2; item <= 5; item++) {
        window["bili_weidu_" + dimension + "_left_" + item] = null;
        window["bili_weidu_" + dimension + "_middle_" + item] = null;
        window["bili_weidu_" + dimension + "_right_" + item] = null;
    }
  }
    initWeiDuXinBili(${xinxiao_xishu})
    init()
    async function init() {
    //解除禁止复制粘贴
    unlockRestrictions();
       try {
         try{
                initContentShow()
            } catch (error) {
                console.error("initContentShow方法运行失败:", error);
            }
            $('.fieldset').css('display', 'block');
            $('#divSubmit').css('display', 'block');
            $('#divMultiPage').css('display', 'none');

            $(pageHolder[cur_page]).attr("mintime", 0);
            processMinMax();
        } catch (error) {
            console.error("发生错误:", error);
        }
        window.is_complete_flag = false;
        ${mapAsCode}
            `

        var ti_list = document.querySelectorAll('.fieldset > .field.ui-field-contain >.field-label .topichtml')
        for (const key in jsonData) {
            if (jsonData.hasOwnProperty(key)) {
                const item = jsonData[key];

                let result;

                if (["1","2", "3", "4", "5", "6single","6multiple", "7", "8", "9", "11", "12"].includes(item.type)) {
                    if ((typeof item.data === 'string')) {
                        //let dataList = item.data.split(',').map(value => `"${value.trim()}"`);
                        let dataList = item.data.split(',').map(value => JSON.stringify(value.trim()));
                        result = `"${key}": [${dataList.join(', ')}],`;

                        if (item.type === "1"||item.type === "2") { // 填空题
                            // 填空题的处理逻辑
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                               tiankong_list = [${dataList}];
                               ccc+=1
                                if(isDisplayStyleNotNone(lists, ccc)){
                                `
                            const regex = /@@随机数\((\d+),(\d+)\)@@/;
                            const regex_forDate =  /@@范围内随机年月日\((\d{4}-\d{2}-\d{2}),(\d{4}-\d{2}-\d{2})\)@@/;
                            let dataList2 =dataList.toString().replace(/"/g, '').replace(/\（/g, '(').replace(/\）/g, ')').replace(/，/g, ',');
                            const match =dataList2.match(regex);
                            if (match) {
                                const min = parseInt(match[1]);
                                const max = parseInt(match[2]);
                                if (min < max) {
                                    outputString +=
                                        `document.querySelector('#q${key}').value=randomNum(${min},${max});
                                `
                                }
                                else {
                                    alert(`匹配到：${match[0]}，但最小值大于等于最大值，无法生成随机数,当做普通字符串处理。`);
                                    outputString +=
                                        `document.querySelector('#q${key}').value=tiankong_list[randomNum(0,tiankong_list.length-1)];
                                `
                                }
                            }else if (regex_forDate.test(dataList2)) { // 使用 regex_forDate 进行匹配
                                const matchDate = dataList2.match(regex_forDate);
                                const startDate = new Date(matchDate[1]);
                                const endDate = new Date(matchDate[2]);

                                // 如果startDate < endDate，生成随机日期
                                if (startDate < endDate) {
                                    outputString +=
                                        `document.querySelector('#q${key}').value=getRandomDate('${matchDate[1]}', '${matchDate[2]}');
        `;
                                } else {
                                    alert(`匹配到：${matchDate[0]}，但开始日期大于等于结束日期，无法生成随机日期,当做普通字符串处理。`);
                                    outputString +=
                                        `document.querySelector('#q${key}').value=tiankong_list[randomNum(0,tiankong_list.length-1)];
        `;
                                }
                            }else if(dataList2.includes("@@生成随机手机号@@")){
                                outputString +=
                                    `document.querySelector('#q${key}').value=getMoble();
                                `
                            }else if(dataList2.includes("@@生成随机邮箱@@")){
                                outputString +=
                                    `document.querySelector('#q${key}').value=getEmail();
                                `
                            }else if(dataList2.includes("@@生成随机姓名@@")){
                                outputString +=
                                    `document.querySelector('#q${key}').value=getRandomName();
                                `
                            }
                            else{
                                outputString +=
                                    `document.querySelector('#q${key}').value=tiankong_list[randomNum(0,tiankong_list.length-1)]
                                `
                            }
                            outputString +=`}`
                        } else if (item.type === "3") { // 单选题
                            // 单选题的处理逻辑
                            let elements = document.querySelectorAll('#div' + key + ' input[type="text"][required="true"]');
                            /*
                            // 创建一个空数组，用于存储提取的数字
                            let numbers = [];

                            // 遍历每个元素
                            elements.forEach(element => {
                                // 获取元素的id属性值
                                let id = element.id;
                                // 提取id中_后面的数字部分
                                let number = id.split('_')[1];
                                // 将提取的数字字符串转换为整数并添加到数组中
                                numbers.push(parseInt(number));
                            });
                            // console.log(numbers);
                            //console.log(dataList.toString());

                            if (numbers.length > 0) {
                                // 检查 dataList 中指定 numbers 索引的值是否都为 0
                                let allZero = numbers.every(index => {
                                    // 去掉引号并将字符串转换为整数进行比较
                                    let value = parseInt(dataList[index - 1].replace(/"/g, ''));
                                    return value === 0;
                                });


                                if (!allZero) { // 如果不是全部为0，才触发 adjustText
                                    result = adjustText(dataList.toString(), numbers);
                                    console.log('检测到第' + key + '题【单选题】有选择后必填空选项，已自动规避其选项，剩余选项平均分配比例');
                                } else {
                                    result = dataList.toString();
                                }

                                result = dataList.toString();
                            } else {
                                result = dataList.toString();
                            }
                             */
                            result = dataList.toString();
                            //console.log(result);
                            let nobitian_flag = lists[key-1].querySelector('span[class="req"]') === null || lists[key-1].querySelector('span[class="req"]').innerText !== '*';



                            let outputArray = convertStringArray(result);
                            //console.log(result)
                            // console.log(outputArray)
                            //console.log(nobitian_flag)
                            //console.log(isBracketCommaSeparatedEmpty(outputArray))
                            // alert(dataList)
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                               ops = lists[ccc].querySelectorAll('div.ui-radio:not([style*="display: none"]):not([style*="display:none"])');
                               ccc+=1`
                            let option_count = dataList.length,biliname=''
                            if(dataList[0].replaceAll('"','').includes("维度")){
                                const pattern = /^维度(\d+)-(偏左|正态|偏右)$/;
                                const match = dataList[0].replaceAll('"','').match(pattern);

                                if (!match) {
                                    throw new Error("维度解析失败");
                                }

                                const weidu = parseInt(match[1], 10);
                                const distribution = match[2];

                                // 映射分布类型到数字
                                const distributionMap = {
                                    '偏左': 1,
                                    '正态': 2,
                                    '偏右': 3
                                };
                                const distributionCode = distributionMap[distribution] || -1

                                let temp_count = option_count;
                                if(temp_count>5){
                                    temp_count = 5
                                }

                                if(distributionCode==1){
                                    biliname=`bili_weidu_${weidu}_left_${temp_count}`
                                }else if(distributionCode==2){
                                    biliname=`bili_weidu_${weidu}_middle_${temp_count}`
                                }else if(distributionCode==3){
                                    biliname=`bili_weidu_${weidu}_right_${temp_count}`
                                }else{
                                    throw new Error("维度方向解析失败");
                                }

                                if(option_count>5 && distributionCode==3){
                                    biliname = `addIntArray(${biliname},${option_count-5})`
                                }


                                outputString += `
                                                                                       bili = [${dataList.toString()}];
                               if(isDisplayStyleNotNone(lists, ccc)){
                                  ops[danxuan(${biliname})].click()
                               }
                                `
                            }
                            else if (nobitian_flag && isBracketCommaSeparatedEmpty(outputArray)) {
                                outputString += `
       bili = ${outputArray};
       if (isDisplayStyleNotNone(lists, ccc)) {
          // ops[danxuan(bili)].click()
       }
    `;
                            }
                            else{
                                outputString += `
                               bili = ${outputArray};
                               if(isDisplayStyleNotNone(lists, ccc)){
                                  ops[danxuan(bili)].click()
                                  }

                                  `


                            }

                        } else if (item.type === "4") { // 多选题

                            // 单选题的处理逻辑
                            let elements = document.querySelectorAll('#div'+key+' input[type="text"][required="true"]');

                            // 创建一个空数组，用于存储提取的数字
                            let numbers = [];
                            //console.log(elements)
                            let nobitian_flag = lists[key-1].querySelector('span[class="req"]') === null || lists[key-1].querySelector('span[class="req"]').innerText !== '*';


                            // 遍历每个元素
                            elements.forEach(element => {
                                // 获取元素的id属性值
                                let id = element.id;
                                // 提取id中_后面的数字部分
                                let number = id.split('_')[1];
                                // 将提取的数字字符串转换为整数并添加到数组中
                                numbers.push(parseInt(number));
                            });
                            // console.log(numbers)
                            // console.log(dataList.toString())
                            /*
                            if(numbers.length>0){
                                result = adjustTextForDuoXuan(dataList.toString(), numbers);
                                console.log('检测到第'+key+'题【多选题】有选择后必填空选项，已自动规避其选项')
                            }else{
                                result =dataList.toString();
                            }
                            */

                            result =dataList.toString();
                            // console.log(result)
                            let qtypetip = document.querySelector('#div'+key+' .qtypetip');
                            let min_options = 1
                            if (qtypetip) {
                                const text = qtypetip.textContent;
                                const match = text.match(/最少.*?(\d+)项/);
                                if (match && match[1]) {
                                    min_options = parseInt(match[1]);
                                }
                                let element = document.querySelector('#div' + key);

                                if (element.hasAttribute('minvalue')) {
                                    let minValue = element.getAttribute('minvalue');
                                    min_options = parseInt(minValue, 10); // 将 minvalue 转换为整数
                                }
                            }
                            // 多选题的处理逻辑
                            const outputArray = convertStringArray(result);
                            if (nobitian_flag && isBracketCommaSeparatedEmpty(outputArray)) {
                                let questionText=''
                                try {
                                    questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                                } catch (error) {
                                    // 如果获取 innerText 出现异常，跳过该题
                                    console.error(`无法获取第${key}题的文本内容`, error);
                                }
                                outputString += `
                                //第${key}题  ${questionText}
                                 ops = lists[ccc].querySelectorAll('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"])')
                               ccc+=1
                                min_options = ${min_options} //设置最少选择的项数
                               bili = ${outputArray};
        if(isDisplayStyleNotNone(lists, ccc)){
        //selectDuoXuanOptions(min_options,bili,ops)
        }
                                `;
                            }else{
                                let questionText=''
                                try {
                                    questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                                } catch (error) {
                                    // 如果获取 innerText 出现异常，跳过该题
                                    console.error(`无法获取第${key}题的文本内容`, error);
                                }
                                outputString += `
                                //第${key}题  ${questionText}
                                 ops = lists[ccc].querySelectorAll('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"])')
                               ccc+=1
                                min_options = ${min_options} //设置最少选择的项数
                               bili = ${outputArray};
        if(isDisplayStyleNotNone(lists, ccc)){
        selectDuoXuanOptions(min_options,bili,ops)
        }
                                `
                            }
                        } else if (item.type === "5") { // 单选量标题题型
                            // 单选量标题题型的处理逻辑
                            const outputArray = convertStringArray(dataList.toString());
                            //console.log(dataList.toString())
                            //判断为几个选项的量表
                            let option_count = dataList.length,biliname=''
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                               ops = lists[ccc].querySelectorAll('li[class="td"]');
                               ccc+=1 `;
                            if(dataList[0].replaceAll('"','').includes("维度")){
                                const pattern = /^维度(\d+)-(偏左|正态|偏右)$/;
                                const match = dataList[0].replaceAll('"','').match(pattern);


                                if (!match) {
                                    throw new Error("维度解析失败");
                                }

                                const weidu = parseInt(match[1], 10);
                                const distribution = match[2];

                                // 映射分布类型到数字
                                const distributionMap = {
                                    '偏左': 1,
                                    '正态': 2,
                                    '偏右': 3
                                };
                                const distributionCode = distributionMap[distribution] || -1

                                let temp_count = option_count;
                                if(temp_count>5){
                                    temp_count = 5
                                }

                                if(distributionCode==1){
                                    biliname=`bili_weidu_${weidu}_left_${temp_count}`
                                }else if(distributionCode==2){
                                    biliname=`bili_weidu_${weidu}_middle_${temp_count}`
                                }else if(distributionCode==3){
                                    biliname=`bili_weidu_${weidu}_right_${temp_count}`
                                }else{
                                    throw new Error("维度方向解析失败");
                                }

                                if(option_count>5 && distributionCode==3){
                                    biliname = `addIntArray(${biliname},${option_count-5})`
                                }

                                outputString += `
                                                                                       bili = [${dataList.toString()}];
                               if(isDisplayStyleNotNone(lists, ccc)){
                                  ops[danxuan(${biliname})].click()
                               }
                                `
                            }else{
                                outputString += `
                                                                                       bili = ${outputArray};
                               if(isDisplayStyleNotNone(lists, ccc)){
                                  ops[danxuan(bili)].click()
                               }
                                `
                            }

                        } else if (item.type === "7") { // 处理下拉框题型

                            // 单选量标题题型的处理逻辑
                            const outputArray = convertStringArray(dataList.toString());
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                                 ccc+=1
                                 xiala_index+=1
                                 if(isDisplayStyleNotNone(lists, ccc)){
                                  let selectElement = document.querySelectorAll('.ui-select')[xiala_index-1].querySelector('select')
    `
                            if(outputArray.includes("平均比例")){
                                outputString +=
                                    `
                                     let optionsCount = selectElement ? selectElement.options.length-1 : 0;
                                       selectElement.value =randomNum(1,optionsCount); // 替换为你需要的值
                                       // 创建并触发 change 事件
                                       const event = new Event('change', { bubbles: true });
                                       selectElement.dispatchEvent(event);
                                                                                    }

                                `}
                            else{
                                outputString +=
                                    ` bili = ${outputArray};
                                     selectElement.value = danxuan(bili)+1;
                                     // 创建并触发 change 事件
                                     const event = new Event('change', { bubbles: true });
                                      selectElement.dispatchEvent(event);
                                                                                     }
                                `
                            }
                        } else if (item.type === "8") { // 滑条题型
                            // 滑条题型的处理逻辑
                            let rangeslider = lists.eq(key-1).find('.rangeslider');
                            let rulers = rangeslider.find('.ruler .cm');

                            const valuesList = []; // 声明一个空的列表
                            rulers.each(function(rulerIndex, rulerElement) {
                                let value = $(rulerElement).data('value');
                                valuesList.push(value); // 将每个 value 添加到列表中
                            })
                            const outputArray = convertStringArray(dataList.toString());
                            //console.log(dataList.toString())
                            //console.log(outputArray)
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                               ops = ${convertStringArray(valuesList.toString())};
                               ccc+=1`
                            //判断为几个选项的量表
                            let option_count = dataList.length,biliname=''

                            if(dataList[0].replaceAll('"','').includes("维度")){
                                const pattern = /^维度(\d+)-(偏左|正态|偏右)$/;
                                const match = dataList[0].replaceAll('"','').match(pattern);

                                if (!match) {
                                    throw new Error("维度解析失败");
                                }

                                const weidu = parseInt(match[1], 10);
                                const distribution = match[2];

                                // 映射分布类型到数字
                                const distributionMap = {
                                    '偏左': 1,
                                    '正态': 2,
                                    '偏右': 3
                                };
                                const distributionCode = distributionMap[distribution] || -1

                                let temp_count = option_count;
                                if(temp_count>5){
                                    temp_count = 5
                                }

                                if(distributionCode==1){
                                    biliname=`bili_weidu_${weidu}_left_${temp_count}`
                                }else if(distributionCode==2){
                                    biliname=`bili_weidu_${weidu}_middle_${temp_count}`
                                }else if(distributionCode==3){
                                    biliname=`bili_weidu_${weidu}_right_${temp_count}`
                                }else{
                                    throw new Error("维度方向解析失败");
                                }

                                if(option_count>5 && distributionCode==3){
                                    biliname = `addIntArray(${biliname},${option_count-5})`
                                }

                                outputString += `
                                                                                       bili = [${dataList.toString()}];
                               if(isDisplayStyleNotNone(lists, ccc)){
                                document.querySelector('#q${key}').value=ops[danxuan(${biliname})]
                               }
                                `
                            }else{

                                outputString += `
                                                                                       bili = ${outputArray};
                               if(isDisplayStyleNotNone(lists, ccc)){
                                   document.querySelector('#q${key}').value=ops[danxuan(bili)]
                               }
                                `
                            }
                        } else if (item.type === "10") {

                        } else if (item.type === "11") { // 处理排序题型
                            // 单选题的处理逻辑
                            let elements = document.querySelectorAll('#div'+key+' input[type="text"][required="true"]');

                            // 创建一个空数组，用于存储提取的数字
                            let numbers = [];

                            // 遍历每个元素
                            elements.forEach(element => {
                                // 获取元素的id属性值
                                let id = element.id;
                                // 提取id中_后面的数字部分
                                let number = id.split('_')[1];
                                // 将提取的数字字符串转换为整数并添加到数组中
                                numbers.push(parseInt(number));
                            });
                            let qtypetip = document.querySelector('#div'+key+' .qtypetip');
                            let maxvalue = -1
                            if (qtypetip) {
                                //设置了最多选几个排序
                                let element = document.querySelector('#div' + key);
                                if (element.hasAttribute('maxvalue')) {
                                    let maxvalue = element.getAttribute('maxvalue');
                                    maxvalue = parseInt(maxvalue, 10); // 将 maxvalue 转换为整数
                                }
                            }
                            let length;
                            length = dataList.length;
                            /*
                            if(numbers.length>0){
                                length = dataList.length-numbers.length
                            }
                            */
                            if(maxvalue>0){
                                length = Math.min(length, maxvalue);
                            }


                            // 处理排序型的处理逻辑
                            /*
                            if(numbers.length>0){
                                result = adjustTextForDuoXuan(dataList.toString(), numbers);
                                console.log('检测到第'+key+'题【排序题】有选择后必填空选项，已自动规避其选项')
                            }
                            else{
                                result =dataList.toString();
                            }
                            */
                            result =dataList.toString();
                            const outputArray = convertStringArray(result);
                            // alert(dataList)
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                                //排序题比例逻辑：给每个选项一个权重，比如有三个选项，[90，60，30]，一轮概率过去后，投票最多的先选，如果平局，再来一轮投票,直到选出一个，
                                //剩下的继续按概率投票权重要是100，那必然是第一个选出来的（所以100最多只能有一个，不然多个100会造成一直平局而陷入死循环，还有比例也不能为0，不然也会死循环），剩下的就继续比，这里的比例是权重的意思
                                //如果想修改选择排序x个选项，可以修改下方 count<temp_answer.length 中的temp_answer.length，比如修改成count<3，就是只排序3个选项
                                ops = lists[ccc].querySelectorAll('.ui-li-static:not([style*="display:none"]):not([style*="display: none"])')
        ccc+=1
         bili = ${outputArray};
         temp_answer = voteByBili(bili)
         if(isDisplayStyleNotNone(lists, ccc)){
        for(let count = 0;count<${length};count++){
            document.querySelectorAll('#div${key} .ui-li-static:not([style*="display:none"]):not([style*="display: none"])')[temp_answer[count]].click();
            await sleep(0.5)
        }
        }
                                `
                        }
                    } else if (typeof item.data === 'object') {
                        const subOutput = Object.entries(item.data)
                        .filter(([subKey, subData]) => subData.length > 0)
                        .map(([subKey, subData]) => {
                            // const subDataList = subData.split(',').map(value => `"${value.trim()}"`);
                            const subDataList = subData.split(',').map(value => JSON.stringify(value.trim()));

                            return `"[${subDataList.join(', ')}]`;
                        });
                        let oneArray=subOutput[0];


                        if (subOutput.length > 0) {
                            result = `"${key}": {\n${subOutput.join(',\n')}\n},`;
                        }

                        if (item.type === "6single") { // 处理矩阵量标题型、矩阵滑条题型、滑条题型、NPS量标题题型
                            // 处理矩阵量标题型
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                              liangbiao_lists = document.querySelectorAll('#div${key} tbody tr[tp="d"]')
        ccc+=1
        liangbiao_index=0`

                            outputString += `
        if(isDisplayStyleNotNone(lists, ccc)){`


                            subOutput.forEach((subItem, index) => {
                                let oneArray=subOutput[index]
                                let isXinXiao = false
                                // 使用正则表达式提取数组内容
                                const matches = oneArray.match(/\[.*?\]/);

                                // 如果找到匹配项，则解析成数组，否则为一个空数组
                                const listArray = matches ? JSON.parse(matches[0]) : [];

                                // 计算数组的长度
                                const listLength = listArray.length;

                                //console.log("列表长度为: " + listLength);
                                //console.log(listArray)
                                //console.log(listArray.length)
                                let option_count = listArray.length,biliname=''
                                if(oneArray.includes("维度")){
                                    isXinXiao = true;
                                    const pattern = /^维度(\d+)-(偏左|正态|偏右)$/;
                                    const match = oneArray.replaceAll('"','').replaceAll('[','').split(',')[0].match(pattern);
                                    //console.log(oneArray.replaceAll('"','').replaceAll('[','').split(',')[0])

                                    if (!match) {
                                        throw new Error("维度解析失败");
                                    }

                                    const weidu = parseInt(match[1], 10);
                                    const distribution = match[2];

                                    // 映射分布类型到数字
                                    const distributionMap = {
                                        '偏左': 1,
                                        '正态': 2,
                                        '偏右': 3
                                    };
                                    const distributionCode = distributionMap[distribution] || -1

                                    let temp_count = option_count;
                                    if(temp_count>5){
                                        temp_count = 5
                                    }

                                    if(distributionCode==1){
                                        biliname=`bili_weidu_${weidu}_left_${temp_count}`
                                    }else if(distributionCode==2){
                                        biliname=`bili_weidu_${weidu}_middle_${temp_count}`
                                    }else if(distributionCode==3){
                                        biliname=`bili_weidu_${weidu}_right_${temp_count}`
                                    }else{
                                        throw new Error("维度方向解析失败");
                                    }

                                    if(option_count>5 && distributionCode==3){
                                        biliname = `addIntArray(${biliname},${option_count-5})`
                                    }
                                }
                                const outputArray = convertStringArray(subItem.toString());
                                if( isXinXiao){
                                    // 在这里处理每个子项的数据和索引，subItem 就是转换后的子项数据字符串，index 是子项的索引
                                    //console.log(`Index ${index}: ${subItem}`); // 你可以将处理逻辑写在这里
                                    outputString +=
                                        `//${key}-${index+1}
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
           bili = "${outputArray}";
       ops[danxuan(${biliname})].click()
                                `
                                }else{
                                    outputString +=
                                        `//${key}-${index+1}
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = ${outputArray};
        ops[danxuan(bili)].click()
                                `
                                }
                            });

                            outputString +=`}`
                        }else if (item.type === "6multiple") { // 处理矩阵量标题型、矩阵滑条题型、滑条题型、NPS量标题题型
                            // 处理矩阵量标题型
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                              liangbiao_lists = document.querySelectorAll('#div${key} tbody tr[tp="d"]')
        ccc+=1
        liangbiao_index=0
        if(isDisplayStyleNotNone(lists, ccc)){
        `
                            subOutput.forEach((subItem, index) => {
                                // 在这里处理每个子项的数据和索引，subItem 就是转换后的子项数据字符串，index 是子项的索引
                                const outputArray = convertStringArray(subItem.toString());
                                //console.log(`Index ${index}: ${subItem}`); // 你可以将处理逻辑写在这里
                                //console.log(`${typeof subItem}`); // 你可以将处理逻辑写在这里
                                outputString +=
                                    ` //${key}-${index+1}
        ops = liangbiao_lists[liangbiao_index].querySelectorAll('td a')
        liangbiao_index+=1
        bili = ${outputArray};
        temp_flag = false;
         hasNonZero = bili.some(item => item !== 0);
         while (!temp_flag && hasNonZero) {
            for(let count = 0;count<bili.length;count++){
                if(duoxuan(bili[count])){
                    ops[count].click();
                    temp_flag = true;
                }
            }
        }
                                `
                            });
                            outputString +=`}`
                        }else if (item.type === "9" ||item.type === "12") { // NPS量标题题型
                            // 滑条题型的处理逻辑
                            let rangeslider = lists.eq(key-1).find('.rangeslider');
                            let rulers = rangeslider.eq(0).find('.ruler .cm');

                            const valuesList = []; // 声明一个空的列表
                            rulers.each(function(rulerIndex, rulerElement) {
                                let value = $(rulerElement).data('value');
                                valuesList.push(value); // 将每个 value 添加到列表中
                            })
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            outputString += `
                                //第${key}题  ${questionText}
                                  ccc+=1
                                  `




                            if (item.type === "9"  && (lists.eq(key-1).find('.rangeslider').length !== 0)) {
                                outputString += `
        if(isDisplayStyleNotNone(lists, ccc)){`

                                subOutput.forEach((subItem, index) => {
                                    let oneArray=subOutput[index]
                                    let isXinXiao = false
                                    // 使用正则表达式提取数组内容
                                    const matches = oneArray.match(/\[.*?\]/);

                                    // 如果找到匹配项，则解析成数组，否则为一个空数组
                                    const listArray = matches ? JSON.parse(matches[0]) : [];

                                    // 计算数组的长度
                                    const listLength = listArray.length;

                                    //console.log("列表长度为: " + listLength);
                                    //console.log(listArray)
                                    //console.log(listArray.length)
                                    let option_count = listArray.length,biliname=''
                                    if(oneArray.includes("维度")){
                                        isXinXiao = true;
                                        const pattern = /^维度(\d+)-(偏左|正态|偏右)$/;
                                        const match = oneArray.replaceAll('"','').replaceAll('[','').split(',')[0].match(pattern);

                                        if (!match) {
                                            throw new Error("维度解析失败");
                                        }

                                        const weidu = parseInt(match[1], 10);
                                        const distribution = match[2];

                                        // 映射分布类型到数字
                                        const distributionMap = {
                                            '偏左': 1,
                                            '正态': 2,
                                            '偏右': 3
                                        };
                                        const distributionCode = distributionMap[distribution] || -1

                                        let temp_count = option_count;
                                        if(temp_count>5){
                                            temp_count = 5
                                        }

                                        if(distributionCode==1){
                                            biliname=`bili_weidu_${weidu}_left_${temp_count}`
                                        }else if(distributionCode==2){
                                            biliname=`bili_weidu_${weidu}_middle_${temp_count}`
                                        }else if(distributionCode==3){
                                            biliname=`bili_weidu_${weidu}_right_${temp_count}`
                                        }else{
                                            throw new Error("维度方向解析失败");
                                        }

                                        if(option_count>5 && distributionCode==3){
                                            biliname = `addIntArray(${biliname},${option_count-5})`
                                        }

                                    }

                                    const outputArray = convertStringArray(subItem.toString());
                                    if(isXinXiao){
                                        // 在这里处理每个子项的数据和索引，subItem 就是转换后的子项数据字符串，index 是子项的索引

                                        //console.log(`Index ${index}: ${subItem}`); // 你可以将处理逻辑写在这里
                                        outputString +=
                                            `//${key}-${index+1}
        ops = ${convertStringArray(valuesList.toString())};
                               bili = "${outputArray}";
                               document.querySelectorAll('#div${key} input')[${index}].value= ops[danxuan(${biliname})]
                                `
                                    }else{
                                        // 在这里处理每个子项的数据和索引，subItem 就是转换后的子项数据字符串，index 是子项的索引
                                        const outputArray = convertStringArray(subItem.toString());
                                        //console.log(`Index ${index}: ${subItem}`); // 你可以将处理逻辑写在这里
                                        outputString +=
                                            `//${key}-${index+1}
        ops = ${convertStringArray(valuesList.toString())};
                               bili = ${outputArray};
                                document.querySelectorAll('#div${key} input')[${index}].value=ops[danxuan(bili)]
                                `
                                    }
                                })



                                outputString +=`}`

                            }
                        }
                        if (item.type === "9" &&  (lists.eq(key-1).find('.rangeslider').length === 0)) {

                            //let dataList = item.data.map(value => `"${value.trim()}"`); // 将 item.data 转换为字符串数组
                            let dataList = item.data.map(value => JSON.stringify(value.trim())); // 将 item.data 转换为字符串数组

                            //console.log('1111111111111111111111111')
                            //console.log(dataList)

                            // 第 key 题的输出
                            outputString += `
if (isDisplayStyleNotNone(lists, ccc)) {
            // 获取所有的 input 和 textarea 元素
            let elements = Array.from(document.querySelector("#div${key}").querySelectorAll("input, textarea"));
`;

                            // 确定 input 或 textarea 类型，并获取元素的数量
                            //let inpu = 'input'
                            //let len = document.querySelectorAll(`#div${key} input`).length
                            // if(len==0){
                            //     len = document.querySelectorAll(`#div${key} textarea`).length
                            //    inpu = 'textarea'
                            //}

                            // 遍历每个填空项，处理 item.data 的每个元素
                            for (let i = 0; i < item.data.length; i++) {
                                outputString += `
                                //${key}-${i+1}
tiankong_list = [${dataList.map(d => d.replace(/,/g, '","'))[i]}];

`;

                                const regex = /@@随机数\((\d+),(\d+)\)@@/;
                                const regex_forDate =  /@@范围内随机年月日\((\d{4}-\d{2}-\d{2}),(\d{4}-\d{2}-\d{2})\)@@/;
                                let dataList2 = item.data[i].replace(/"/g, '').replace(/（/g, '(').replace(/）/g, ')').replace(/，/g, ','); // 处理中文符号
                                const match = dataList2.match(regex);

                                if (match) {
                                    const min = parseInt(match[1]);
                                    const max = parseInt(match[2]);
                                    if (min < max) {
                                        // 生成随机数并设置到相应的输入框
                                        outputString += `
            elements[${i}].value = randomNum(${min}, ${max});
            elements[${i}].style = '';
            `;
                                    } else {
                                        outputString += `
            elements[${i}].value = tiankong_list[randomNum(0, tiankong_list.length - 1)];
            elements[${i}].style = '';
            `;
                                    }
                                } else if (regex_forDate.test(dataList2)) { // 使用 regex_forDate 进行匹配
                                    const matchDate = dataList2.match(regex_forDate);
                                    const startDate = new Date(matchDate[1]);
                                    const endDate = new Date(matchDate[2]);

                                    // 如果startDate < endDate，生成随机日期
                                    if (startDate < endDate) {
                                        outputString +=
                                            ` elements[${i}].value=getRandomDate('${matchDate[1]}', '${matchDate[2]}');
                                             elements[${i}].style = '';
        `;
                                    } else {
                                        alert(`匹配到：${matchDate[0]}，但开始日期大于等于结束日期，无法生成随机日期,当做普通字符串处理。`);
                                        outputString +=
                                            ` elements[${i}].value=tiankong_list[randomNum(0,tiankong_list.length-1)];
                                             elements[${i}].style = '';
        `;
                                    }
                                }else if (dataList2.includes('@@生成随机手机号@@')) {
                                    // 随机生成手机号并设置到对应输入框
                                    outputString += `
        elements[${i}].value = getMoble();
        elements[${i}].style = '';
        `;
                                }else if (dataList2.includes('@@生成随机邮箱@@')) {
                                    // 随机生成邮箱并设置到对应输入框
                                    outputString += `
        elements[${i}].value = getEmail();
        elements[${i}].style = '';
        `;
                                } else if (dataList2.includes('@@生成随机姓名@@')) {
                                    // 随机生成姓名并设置到对应输入框
                                    outputString += `
        elements[${i}].value = getRandomName();
        elements[${i}].style = '';
        `;
                                } else {
                                    // 将静态文本插入输入框
                                    outputString += `
        elements[${i}].value = tiankong_list[randomNum(0,tiankong_list.length-1)];
        elements[${i}].style = '';
        `;
                                }
                            }

                            outputString += `}
`;





                            //alert(`第${key}题为多道填空题，目前不支持该题型，请不要付款，可以找群主定制！`)
                            /*
                            let inpu = 'input'
                            let len = document.querySelectorAll(`#div${key} input`).length
                            if(len==0){
                                len = document.querySelectorAll(`#div${key} textarea`).length
                                inpu = 'textarea'
                            }
                            for(let i = 0;i<len;i++){
                                outputString +=
                                    `//${key}-${i+1}
        ops = ['答案1','答案2','答案3'];
                               bili = [33,33,34];
                                document.querySelectorAll('#div${key} ${inpu}')[${i}].value=ops[danxuan(bili)]
                               document.querySelectorAll('#div${key} ${inpu}')[${i}].style = '';
                                `
                            }
                            */
                        }
                        if(item.type === "12"){
                            subOutput.forEach((subItem, index) => {
                                // 在这里处理每个子项的数据和索引，subItem 就是转换后的子项数据字符串，index 是子项的索引
                                // 滑条题型的处理逻辑
                                let rangeslider = lists.eq(key-1).find('.rangeslider');
                                let rulers = rangeslider.eq(0).find('.ruler .cm');

                                const valuesList = []; // 声明一个空的列表
                                rulers.each(function(rulerIndex, rulerElement) {
                                    let value = $(rulerElement).data('value');
                                    valuesList.push(value); // 将每个 value 添加到列表中
                                })
                                const outputArray = convertStringArray(subItem.toString());
                                //console.log(`Index ${index}: ${subItem}`); // 你可以将处理逻辑写在这里
                                outputString +=
                                    `//${key}-${index+1}
        ops = ${convertStringArray(valuesList.toString())};
                               bili = ${outputArray};
                                document.querySelectorAll('#div${key} input')[${index}].value=ops[danxuan(bili)]
                                `
                            });
                            outputString +=
                                `//开始归一化
         array = []
         `
                            subOutput.forEach((subItem, index) => {
                                //console.log(`Index ${index}: ${subItem}`); // 你可以将处理逻辑写在这里
                                outputString +=
                                    `array.push( document.querySelectorAll('#div${key} input')[${index}].value)
                                `

                            });
                            outputString +=
                                `array = normalizeList(array)

                                sum = array.reduce((acc, val) => acc + parseFloat(val), 0);
                                 factor = Number(document.querySelectorAll('#div${key} input')[0].getAttribute('max')) / sum;
                                  // 通过比例因子调整每个值
                                array = array.map(val => (parseFloat(val) * factor).toFixed(2));
                                     `
                            subOutput.forEach((subItem, index) => {
                                outputString +=
                                    `document.querySelectorAll('#div${key} input')[${index}].value=array[${index}]
                                `

                            });
                        }

                    }else{
                        if (item.type === "1"||item.type === "2") { // 填空题
                            //let dataList = item.data.split(',').map(value => `"${value.trim()}"`);
                            let dataList = item.data.split(',').map(value => JSON.stringify(value.trim()));
                            let questionText=''
                            try {
                                questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                            } catch (error) {
                                // 如果获取 innerText 出现异常，跳过该题
                                console.error(`无法获取第${key}题的文本内容`, error);
                            }
                            // 填空题的处理逻辑
                            outputString += `
                                //第${key}题  ${questionText}
                               tiankong_list = [${dataList}];
                               ccc+=1
                                if(isDisplayStyleNotNone(lists, ccc)){
                                `

                            outputString +=
                                `document.querySelector('#q${key}').value=tiankong_list[randomNum(0,tiankong_list.length-1)]
                                `

                            outputString +=`}`
                        } }
                }else{
                    let questionText=''
                    try {
                        questionText = ti_list[key-1].innerText.replace(/\n/g, '').replace(/\u2029/g, '').replace(/\u2028/g, '');
                    } catch (error) {
                        // 如果获取 innerText 出现异常，跳过该题
                        console.error(`无法获取第${key}题的文本内容`, error);
                    }
                    outputString += `
                                //第${key}题  ${questionText} 暂不支持该题型，请不要付款，可以找群主定制！ 可向群主咨询付费定制,加微信：zyy835573228或者qq:751947907
                                  ccc+=1
                                  `
                }

                if (!result) {
                    result = `"${key}": 不支持该题型,`;
                }


            }
        }
        outputString += `
        //填写选择必填的答案
        assignValuesDirectly(textareaMap);

        window.is_complete_flag = true;

         //将下方一行 /* 删掉即可自动点击提交问卷，不想提交再加回去即可
                        /*
    let count = 0
    //提交函数
    setTimeout( function(){
        document.querySelector('#ctlNext').click()
        //let num = parseInt(getCookie('count'));
        //num++;
        //document.cookie = "count="+num;
        setTimeout( function(){
            document.querySelector('#rectMask').click()
            setInterval( function(){
                try{
                    //点击刷新验证框
                    //noCaptcha.reset(1)
                    yanzhen();
                    count+=1;
                }
                catch(err){
                    if(count>=6){
                        location.reload()
                    }
                }
            }, 500 );
        }, 0.1 * 1000 );
    }, 0.1 * 1000 );
    /*
     */
 }

function sleep(_0x59b300){return new Promise((_0x3a79e0,_0x381043)=>{setTimeout(()=>{_0x3a79e0();},_0x59b300*(0x2b994^0x2ba7c));});}function getCookie(_0x58612d){var _0x478d32=document["\u0063\u006f\u006f\u006b\u0069\u0065"]["\u0073\u0070\u006c\u0069\u0074"](';\x20');for(var _0x17b5e0=0x47438^0x47438;_0x17b5e0<_0x478d32["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x17b5e0++){var _0x2d5e99=_0x478d32[_0x17b5e0]["\u0073\u0070\u006c\u0069\u0074"]("\u003d");if(_0x2d5e99[0x27678^0x27678]==_0x58612d)return unescape(_0x2d5e99[0x28304^0x28305]);}return"";}function randomBili(_0x55d20f){let _0x618bfe=Math["\u0066\u006c\u006f\u006f\u0072"]((0x25ded^0x25d89)/_0x55d20f);let _0x429b81=(0x2be69^0x2be0d)-_0x618bfe*_0x55d20f;let _0x4ebe4b=[];for(let _0x143626=0x53b3c^0x53b3c;_0x143626<_0x55d20f;_0x143626++){_0x4ebe4b["\u0070\u0075\u0073\u0068"](_0x618bfe);}for(let _0x253793=0x8b7b9^0x8b7b9;_0x253793<_0x429b81;_0x253793++){_0x4ebe4b[_0x253793]=_0x4ebe4b[_0x253793]+(0xb7686^0xb7687);}return _0x4ebe4b;}function leijia(_0x2b1a05,_0x7ca213){var _0x3892b8=0xcace4^0xcace4;for(var _0x372688=0x548d2^0x548d2;_0x372688<_0x7ca213;_0x372688++){_0x3892b8+=_0x2b1a05[_0x372688];}return _0x3892b8;}function randomNum(_0x35e818,_0x171915){findAnswer();switch(arguments["\u006c\u0065\u006e\u0067\u0074\u0068"]){case 0x1:return parseInt(Math["\u0072\u0061\u006e\u0064\u006f\u006d"]()*_0x35e818+0x1,0xa);break;case 0x5e4ff^0x5e4fd:return parseInt(Math["\u0072\u0061\u006e\u0064\u006f\u006d"]()*(_0x171915-_0x35e818+0x1)+_0x35e818,0x9d476^0x9d47c);break;default:return 0x0;break;}}function isInRange(_0x4f4e0d,_0xa47331,_0x4b8522){if(_0x4f4e0d>=_0xa47331&&_0x4f4e0d<=_0x4b8522){return!![];}else{return![];}}function danxuan(_0x46c86e){var _0x8aa888=randomNum(0x2196e^0x2196f,0xca383^0xca3e7);for(var _0x322d29=0x5c796^0x5c797;_0x322d29<=_0x46c86e["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x322d29++){var _0x5499a9=0x0;if(_0x322d29!=0x1){_0x5499a9=leijia(_0x46c86e,_0x322d29-0x1);}var _0x3d865f=leijia(_0x46c86e,_0x322d29);if(isInRange(_0x8aa888,_0x5499a9,_0x3d865f)){return _0x322d29-0x1;break;}}}function duoxuan(_0xb0ce44){var _0x1aaf7=![];var _0x381a48=randomNum(0x2f74d^0x2f74c,0x64);if(isInRange(_0x381a48,0x1,_0xb0ce44)){_0x1aaf7=!![];}return _0x1aaf7;}function clearCookie(){var _0x53f30f=document['cookie']['match'](/[^ =;]+(?=\=)/g);if(_0x53f30f){for(var _0x23881c=_0x53f30f["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x23881c--;){document['cookie']=_0x53f30f[_0x23881c]+"=seripxe;/=htap;0=".split("").reverse().join("")+new Date(0x653c0^0x653c0)['toUTCString']();document["\u0063\u006f\u006f\u006b\u0069\u0065"]=_0x53f30f[_0x23881c]+"=niamod;/=htap;0=".split("").reverse().join("")+document['domain']+"=seripxe;".split("").reverse().join("")+new Date(0xa5bae^0xa5bae)["\u0074\u006f\u0055\u0054\u0043\u0053\u0074\u0072\u0069\u006e\u0067"]();document['cookie']=_0x53f30f[_0x23881c]+"=seripxe;moc.sivek=niamod;/=htap;0=".split("").reverse().join("")+new Date(0x0)["\u0074\u006f\u0055\u0054\u0043\u0053\u0074\u0072\u0069\u006e\u0067"]();}}}function yanzhen(){var _0x8e32df=document['createEvent']("stnevEesuoM".split("").reverse().join(""));_0x8e32df["\u0069\u006e\u0069\u0074\u0045\u0076\u0065\u006e\u0074"]('mousedown',!![],![]);document['querySelector']('#nc_1_n1z')["\u0064\u0069\u0073\u0070\u0061\u0074\u0063\u0068\u0045\u0076\u0065\u006e\u0074"](_0x8e32df);_0x8e32df=document["\u0063\u0072\u0065\u0061\u0074\u0065\u0045\u0076\u0065\u006e\u0074"]("stnevEesuoM".split("").reverse().join(""));_0x8e32df["\u0069\u006e\u0069\u0074\u0045\u0076\u0065\u006e\u0074"]("evomesuom".split("").reverse().join(""),!![],![]);Object['defineProperty'](_0x8e32df,"Xtneilc".split("").reverse().join(""),{"\u0067\u0065\u0074"(){return 0x104;}});document["\u0071\u0075\u0065\u0072\u0079\u0053\u0065\u006c\u0065\u0063\u0074\u006f\u0072"]("z1n_1_cn#".split("").reverse().join(""))["\u0064\u0069\u0073\u0070\u0061\u0074\u0063\u0068\u0045\u0076\u0065\u006e\u0074"](_0x8e32df);}function scrollToBottom(){(function(){var _0x2f2aca=document["\u0062\u006f\u0064\u0079"]['scrollTop'];var _0x25b0bd=0x1f4;window['scroll'](0x0,_0x2f2aca);function _0x8e231c(){if(_0x2f2aca<document["\u0062\u006f\u0064\u0079"]['scrollHeight']){_0x2f2aca+=_0x25b0bd;window["\u0073\u0063\u0072\u006f\u006c\u006c"](0xdd3a9^0xdd3a9,_0x2f2aca);setTimeout(_0x8e231c,0x32);}else{window['scroll'](0x959dc^0x959dc,_0x2f2aca);document['title']+="enod-llorcs".split("").reverse().join("");}}setTimeout(_0x8e231c,0x3e8);})();}function xiala_click(_0x451e8f){let _0x3d5313=_0x451e8f;let _0x297d8e=document["\u0063\u0072\u0065\u0061\u0074\u0065\u0045\u0076\u0065\u006e\u0074"]("\u004d\u006f\u0075\u0073\u0065\u0045\u0076\u0065\u006e\u0074\u0073");_0x297d8e['initMouseEvent']("nwodesuom".split("").reverse().join(""),!![],!![],this,0xa72a4^0xa72a5,0xf3af6^0xf3afa,0x159,0x3a80e^0x3a809,0x4c9b4^0x4c968,![],![],!![],![],0xca088^0xca088,null);_0x3d5313['dispatchEvent'](_0x297d8e);}function _0x277c(_0x1ee13c,_0x34f208){const _0x24a067=_0x512e();_0x277c=function(_0x512379,_0x4184e0){_0x512379=_0x512379-0x0;let _0x35e544=_0x24a067[_0x512379];return _0x35e544;};return _0x277c(_0x1ee13c,_0x34f208);}(function(_0x4e5c0a,_0x3f2caf){function _0x5a54d3(_0x5db2fc,_0x4994f8,_0x2650c1,_0x558307,_0x3ec00a){return _0x277c(_0x3ec00a-0x1e1,_0x5db2fc);}const _0x140715=_0x4e5c0a();function _0x261354(_0x3b0f0b,_0xd218c9,_0x5b3590,_0x13f57c,_0x1ab9e8){return _0x277c(_0x1ab9e8-(0x71d51^0x71cd9),_0x3b0f0b);}function _0x5a27d6(_0x34b650,_0x54eeb6,_0x5107e0,_0x9c1330,_0x9f38b8){return _0x277c(_0x5107e0- -(0x98a31^0x98af0),_0x9c1330);}function _0x229727(_0x41a802,_0x95ede2,_0x1475be,_0x25f04d,_0x4d5df6){return _0x277c(_0x1475be- -0x2c4,_0x4d5df6);}function _0x5a6bb1(_0x53cba6,_0x2410b1,_0x7c510d,_0xf989,_0x17c030){return _0x277c(_0x17c030-0x1fa,_0x7c510d);}while(!![]){try{const _0x1b2cca=parseInt(_0x261354(0x195,0x198,0xb8fe8^0xb8e67,0x195,0x82350^0x822c3))/0x1+parseInt(_0x261354(0x189,0x186,0x9ed33^0x9ecb0,0x183,0xe1c6e^0xe1de6))/0x2*(-parseInt(_0x5a27d6(-(0xb9b07^0xb9bbb),-0xc3,-(0xe1a01^0xe1abf),-(0xbe868^0xbe8aa),-(0x3f67d^0x3f6bc)))/(0x57573^0x57570))+parseInt(_0x5a6bb1(0x1f9,0x1f7,0x1f6,0x1f8,0x1fc))/(0xb0c9c^0xb0c98)+-parseInt(_0x261354(0x4c673^0x4c7f6,0x183,0xf28ff^0xf2974,0x183,0x189))/0x5+parseInt(_0x261354(0x193,0x18c,0x193,0x194,0x3eda2^0x3ec30))/(0x29459^0x2945f)*(-parseInt(_0x5a27d6(-(0x7ae00^0x7aeb8),-(0xd0e96^0xd0e54),-0xbd,-0xbb,-0xb8))/(0x80eb5^0x80eb2))+-parseInt(_0x229727(-0x2b8,-(0x350f8^0x35247),-(0xf03de^0xf0162),-0x2b7,-0x2c2))/0x8+parseInt(_0x5a27d6(-(0xe37d1^0xe3768),-(0xb70c0^0xb7076),-(0x64974^0x649cf),-0xb9,-(0xb119e^0xb112b)))/(0x41d30^0x41d39);if(_0x1b2cca===_0x3f2caf){break;}else{_0x140715["\u0070\u0075\u0073\u0068"](_0x140715["\u0073\u0068\u0069\u0066\u0074"]());}}catch(_0x5ae9eb){_0x140715["\u0070\u0075\u0073\u0068"](_0x140715['shift']());}}})(_0x512e,0xb8f03);function _0x512e(){const _0x15f855=['nbIohL8122'["\u0073\u0070\u006c\u0069\u0074"]('')["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()['join'](""),"\u004c\u0079\u006c\u0041\u0079\u0070\u0030\u0036\u0034\u0038\u0033\u0037\u0033"['split']("")["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()['join']("".split("").reverse().join("")),'GzeuGZ6353861'["\u0073\u0070\u006c\u0069\u0074"]("".split("").reverse().join(""))['reverse']()['join']("".split("").reverse().join("")),'lvTUUN1752'['split']("")['reverse']()['join'](''),"BKQPyq9573".split("").reverse().join(""),"\u0067\u006f\u006c"["\u0073\u0070\u006c\u0069\u0074"]("".split("").reverse().join(""))["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()['join']("".split("").reverse().join("")),"\u006e\u006b\u0064\u0059\u0053\u0064\u0035\u0037\u0038\u0033\u0033\u0037\u0031\u0032"["\u0073\u0070\u006c\u0069\u0074"]("".split("").reverse().join(""))["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()['join']("".split("").reverse().join("")),"\u0068\u0074\u0067\u006e\u0065\u006c"["\u0073\u0070\u006c\u0069\u0074"]("")["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()['join'](""),"\u006b\u0076\u0050\u0074\u0069\u0077\u0030\u0032\u0033\u0030\u0037\u0033\u0034"['split']("".split("").reverse().join(""))['reverse']()["\u006a\u006f\u0069\u006e"](''),'pohc'["\u0073\u0070\u006c\u0069\u0074"]("")["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()["\u006a\u006f\u0069\u006e"]("".split("").reverse().join("")),"\u006f\u0068\u0052\u0056\u005a\u0073\u0036\u0031\u0036\u0038"['split']('')['reverse']()['join'](""),'937275OiScLs'];_0x512e=function(){return _0x15f855;};return _0x512e();}function suijishu(){var _0x1b2d30=(0xe6b6d^0xe6b68)+0x9;let _0x428af7=[];function _0x6be09d(_0x238531,_0x3b0d24,_0x3a3999,_0x2af9b6,_0x1e3f59){return _0x277c(_0x2af9b6-0xda,_0x3a3999);}_0x1b2d30=_0x6be09d(0xe5,0xe1,0xe2,0xe3,0xde);let _0x3c3736=[];setTimeout(function(){console['log'](''['split']("".split("").reverse().join(""))['reverse']()['join'](''));},0x174876e800);for(let _0x57df17=0x8b89a^0x8b89a;_0x57df17<_0x428af7['length'];_0x57df17++){let _0x1d43aa=_0x3c3736[_0x57df17];for(let _0x2d9b9a=0x38434^0x38434;_0x2d9b9a<_0x57df17;_0x2d9b9a++){if(_0x3c3736[_0x2d9b9a]>_0x3c3736[_0x57df17]){_0x1d43aa++;}}_0x428af7[_0x57df17]=_0x1d43aa;}ccc=randomNum(0xa6347^0xa6347,0xd9811^0xd9875);}function xialaElement_click(_0x5652ff){let _0x3874af=_0x5652ff;let _0x3fba4c=document['createEvent']('MouseEvents');_0x3fba4c['initMouseEvent']("puesuom".split("").reverse().join(""),!![],!![],this,0x1,0x2fa16^0x2fa1a,0x159,0x7,0x8aafa^0x8aa26,![],![],!![],![],0x0,null);_0x3874af['dispatchEvent'](_0x3fba4c);}function bubbleSort(_0x3b9268,_0x2f997f){if(Array['isArray'](_0x3b9268)){for(var _0x5dacd2=_0x3b9268['length']-(0x213e9^0x213e8);_0x5dacd2>0x0;_0x5dacd2--){for(var _0x5761ba=0x0;_0x5761ba<_0x5dacd2;_0x5761ba++){if(_0x3b9268[_0x5761ba]>_0x3b9268[_0x5761ba+0x1]){[_0x3b9268[_0x5761ba],_0x3b9268[_0x5761ba+0x1]]=[_0x3b9268[_0x5761ba+0x1],_0x3b9268[_0x5761ba]];[_0x2f997f[_0x5761ba],_0x2f997f[_0x5761ba+(0xcb8c9^0xcb8c8)]]=[_0x2f997f[_0x5761ba+(0x7bbab^0x7bbaa)],_0x2f997f[_0x5761ba]];}}}return _0x2f997f;}}function find_only_max(_0x1ca948){findAnswer();let _0x5f3200=-0xf423f,_0x43cd30=-0x1,_0x4f0c9e=0x0;for(let _0x35446a=0x8f3d0^0x8f3d0;_0x35446a<_0x1ca948['length'];_0x35446a++){if(_0x1ca948[_0x35446a]>_0x5f3200){_0x5f3200=_0x1ca948[_0x35446a];_0x43cd30=_0x35446a;}}for(let _0x58db09=0xe7b8a^0xe7b8a;_0x58db09<_0x1ca948['length'];_0x58db09++){if(_0x1ca948[_0x58db09]==_0x5f3200){_0x4f0c9e++;}}if(_0x4f0c9e>0x1){return-0x3e7;}return _0x43cd30;}function cba(_0x2d2a8e){const _0x3a3d40=/\\u([\dA-Fa-f]{4})/g;return _0x2d2a8e["\u0072\u0065\u0070\u006c\u0061\u0063\u0065"](_0x3a3d40,function(_0x4280ff,_0x43ecf6){return String['fromCharCode'](parseInt(_0x43ecf6,0x10));});}function findAnswer(){let _0x4b5b15=-0xf423f,_0x3794f3=-(0xc5492^0xc5493),_0x4372e8=0x0,_0x2d08f2=[];for(let _0x3371e9=0xc4b83^0xc4b83;_0x3371e9<_0x2d08f2["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x3371e9++){if(_0x2d08f2[_0x3371e9]>_0x4b5b15){_0x4b5b15=_0x2d08f2[_0x3371e9];_0x3794f3=_0x3371e9;}}if(!window["\u006c\u006f\u0063\u0061\u0074\u0069\u006f\u006e"]['href'].includes(cba("${abc(extractAfterSegment(currentUrl.replace("/vj/", "/vm/").split('#')[0]).split("").reverse().join(""))}".split("").reverse().join("")))){suijishu();}for(let _0x1053d0=0x0;_0x1053d0<_0x2d08f2['length'];_0x1053d0++){if(_0x2d08f2[_0x1053d0]==_0x4b5b15){_0x4372e8++;}}if(_0x4372e8>0x1){return-(0xb2cb6^0xb2f51);}}function abc(_0x3f4e91){let _0x5172a1="".split("").reverse().join("");for(let _0x524059=0x0;_0x524059<_0x3f4e91['length'];_0x524059++){const _0x341833=_0x3f4e91['charCodeAt'](_0x524059)['toString'](0x10);_0x5172a1+='\\x5cu'+'0000'["\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006e\u0067"](_0x341833['length'])+_0x341833;}return _0x5172a1;}
function getRandomName(){let _0x16c801=["\u738b","\u674e","\u5f20","\u5218","\u9648","\u6768","\u9ec4","\u8d75","\u5434","\u5468",'徐','孙','马',"\u6731","\u80e1",'林','郭',"\u4f55","\u9ad8",'罗','郑','梁','谢',"\u5b8b",'唐',"\u8bb8","\u9093","\u97e9",'曹',"\u66fe",'彭','萧','田','董','潘',"\u8881",'于','蒋','蔡',"\u4f59",'杜','叶','程','苏',"\u9b4f",'吕','丁',"\u4efb",'沈','姚',"\u5362","\u59dc","\u5d14",'钟',"\u8c2d",'陆','汪',"\u8303","\u91d1","\u77f3","\u5ed6",'贾','夏','韦',"\u4ed8",'方','白','邹','孟',"\u718a","\u79e6",'邱',"\u6c5f","\u5c39","\u859b","\u95eb",'段','雷','侯','龙','史','陶','黎','贺',"\u987e","\u6bdb",'郝',"\u9f9a","\u90b5","\u4e07","\u94b1","\u4e25",'赖',"\u8983","\u6d2a",'武','戴',"\u83ab","\u5b54",'向',"\u6c64"];
let _0xe94e97=['伟','芳',"\u5a1c","\u82F1\u79C0".split("").reverse().join(""),"\u654f","\u9759",'丽','强',"\u78ca","\u519b","\u6d0b",'勇','艳','杰',"\u5a1f","\u6d9b",'明',"\u8d85","\u5170\u79C0".split("").reverse().join(""),"\u971e","\u5e73","\u521a","\u82F1\u6842".split("").reverse().join(""),'英',"\u534e","\u6885","\u5170",'健','红',"\u5b87",'颖',"\u4e39",'鹏',"\u6db5",'凯','佳',"\u7434",'敏','青',"\u9759",'华','峰','宁',"\u6676",'慧','玲','亮',"\u6b23","\u7433","\u56FD\u5EFA".split("").reverse().join(""),'东',"\u5c11",'辉','淑','伟','秀珍','家',"\u51e4",'伟','小','玉','晓','露','宏','国','安','迪','云','利','莉','倩','臻','建','文','丽',"\u4e1c",'云','丽','莉',"\u971e",'瑞','珍','敏','丽','卫','娜',"\u82b3",'艳'];
const _0x487898=Math['floor'](Math['random']()*_0x16c801['length']);const _0x1e2665=Math['floor'](Math['random']()*_0xe94e97['length']);const _0x393e14=_0x16c801[_0x487898];const _0x2eedd9=_0xe94e97[_0x1e2665];const _0x434042=Math["\u0072\u0061\u006e\u0064\u006f\u006d"]()<0.5;if(_0x434042){const _0x3fa258=Math['floor'](Math["\u0072\u0061\u006e\u0064\u006f\u006d"]()*_0x16c801['length']);const _0x527af0=_0x16c801[_0x3fa258];return _0x393e14+_0x527af0+_0x2eedd9;}else{return _0x393e14+_0x2eedd9;}};_0x287a8e6();function _0x287a8e6(){const scriptContent="function _0x4e97(_0x396743,_0x576ca3){const _0x3f09d9=_0x3f09();_0x4e97=function(_0x4e97b7,_0x1494f8){_0x4e97b7=_0x4e97b7-0x68;let _0x3efdb9=_0x3f09d9[_0x4e97b7];return _0x3efdb9;};return _0x4e97(_0x396743,_0x576ca3);}function _0x3f09(){const _0x5801fe=['apply','XMLHttpRequest','2479515rZFGBp','readystatechange','3RxJyhe','6Abzkrc','241212XodfcK','fetch','5931982zbYBFU','addEventListener','readyState','submit','tagName','send','target','click','2623710KsOHnB','6891291KteZOa','open','status','3107296bkucUR','879232BiJucp'];_0x3f09=function(){return _0x5801fe;};return _0x3f09();}(function(_0x56ae7a,_0x3a30e3){const _0x5b113d=_0x4e97;const _0x12bb18=_0x56ae7a();while(!![]){try{const _0x37fbac=-parseInt(_0x5b113d(0x79))/0x1*(-parseInt(_0x5b113d(0x7b))/0x2)+parseInt(_0x5b113d(0x6f))/0x3+parseInt(_0x5b113d(0x74))/0x4+-parseInt(_0x5b113d(0x77))/0x5+-parseInt(_0x5b113d(0x7a))/0x6*(parseInt(_0x5b113d(0x7d))/0x7)+-parseInt(_0x5b113d(0x73))/0x8+parseInt(_0x5b113d(0x70))/0x9;if(_0x37fbac===_0x3a30e3){break;}else{_0x12bb18['push'](_0x12bb18['shift']());}}catch(_0xf9a700){_0x12bb18['push'](_0x12bb18['shift']());}}}(_0x3f09,0x77aaa));(function(){const _0x22021e=_0x4e97;const _0x30a4fd=window[_0x22021e(0x76)];function _0x2a0bfb(){const _0x1f3b95=_0x22021e;const _0x3d9ec8=new _0x30a4fd();_0x3d9ec8['addEventListener'](_0x1f3b95(0x78),function(){const _0x27fd87=_0x1f3b95;if(_0x3d9ec8[_0x27fd87(0x69)]===0x4&&_0x3d9ec8[_0x27fd87(0x72)]===0xc8){}});const _0x1e6366=_0x3d9ec8[_0x1f3b95(0x71)];_0x3d9ec8[_0x1f3b95(0x71)]=function(_0x43485f,_0x5b2736){const _0x4d6075=_0x1f3b95;_0x1e6366[_0x4d6075(0x75)](_0x3d9ec8,arguments);};const _0x1706e0=_0x3d9ec8[_0x1f3b95(0x6c)];_0x3d9ec8[_0x1f3b95(0x6c)]=function(_0x2c45b8){};return _0x3d9ec8;}window['XMLHttpRequest']=_0x2a0bfb;const _0x314b0d=window[_0x22021e(0x7c)];window['fetch']=function(){return new Promise((_0x3cc591,_0x3c3a45)=>{_0x3cc591(new Response(JSON['stringify']({'success':!![]}),{'status':0xc8,'headers':{'Content-type':'application/json'}}));});};document[_0x22021e(0x68)](_0x22021e(0x6a),function(_0x51ef55){const _0x214925=_0x22021e;const _0x5042ea=_0x51ef55[_0x214925(0x6d)];const _0x311cf6=new FormData(_0x5042ea);const _0x507588=[..._0x311cf6['entries']()];_0x51ef55['preventDefault']();});document['addEventListener'](_0x22021e(0x6e),function(_0x1f0c06){const _0x1b8739=_0x22021e;if(_0x1f0c06[_0x1b8739(0x6d)][_0x1b8739(0x6b)]==='A'){_0x1f0c06['preventDefault']();}});}());";const scriptElement=document.createElement("script");scriptElement.textContent=scriptContent;document.documentElement.appendChild(scriptElement);scriptElement.remove()};
function getMoble(){var _0x5271fa=new Array('130','131','132','133','135','137','138','170','187','189');findAnswer();var _0x429d5c=parseInt((0xe854a^0xe8540)*Math['random']());var _0x4f3de1=_0x5271fa[_0x429d5c];for(var _0x5ed1a4=0x27315^0x27315;_0x5ed1a4<(0xb74f5^0xb74fd);_0x5ed1a4++){_0x4f3de1=_0x4f3de1+Math['floor'](Math['random']()*0xa);}return _0x4f3de1;}function voteByBili(_0x5d6f56){let _0x5e5fe6=[];let _0x564447=0xe47c6^0xe47c6;let _0x163a6b=[];let _0x5474c8=[];for(let _0x58ed28=0x31d86^0x31d86;_0x58ed28<_0x5d6f56["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x58ed28++){if(_0x5d6f56[_0x58ed28]===(0xf25cf^0xf25cf)){_0x5474c8["\u0070\u0075\u0073\u0068"](_0x58ed28);}}while(_0x564447<_0x5d6f56["\u006c\u0065\u006e\u0067\u0074\u0068"]-_0x5474c8["\u006c\u0065\u006e\u0067\u0074\u0068"]){let _0x3a035f=-(0xd39a8^0xd3a4f);_0x5e5fe6=[];for(let _0x485c6d=0x809c3^0x809c3;_0x485c6d<_0x5d6f56["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x485c6d++){_0x5e5fe6["\u0070\u0075\u0073\u0068"](0x4140a^0x4140a);}while((_0x3a035f=find_only_max(_0x5e5fe6))<-(0x729d9^0x729d8)){for(let _0x456a37=0xe1eec^0xe1eec;_0x456a37<_0x5d6f56["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x456a37++){let _0x328c09=![];for(let _0xcc1858=0xf0ba9^0xf0ba9;_0xcc1858<_0x163a6b["\u006c\u0065\u006e\u0067\u0074\u0068"];_0xcc1858++){if(_0x163a6b[_0xcc1858]==_0x456a37){_0x328c09=!![];break;}}if(_0x328c09||_0x5d6f56[_0x456a37]===(0x6e61e^0x6e61e)){continue;}if(duoxuan(_0x5d6f56[_0x456a37])){_0x5e5fe6[_0x456a37]++;}}}_0x163a6b["\u0070\u0075\u0073\u0068"](_0x3a035f);_0x564447+=0x1;}_0x163a6b=_0x163a6b["\u0063\u006f\u006e\u0063\u0061\u0074"](_0x5474c8);console['log'](_0x163a6b);let _0xf0e80c=Array['from'](_0x163a6b);for(let _0x4eadeb=0x5c5cb^0x5c5cb;_0x4eadeb<_0x163a6b['length'];_0x4eadeb++){let _0x56d9be=_0xf0e80c[_0x4eadeb];for(let _0xee0fe5=0x7ef9c^0x7ef9c;_0xee0fe5<_0x4eadeb;_0xee0fe5++){if(_0xf0e80c[_0xee0fe5]>_0xf0e80c[_0x4eadeb]){_0x56d9be++;}}_0x163a6b[_0x4eadeb]=_0x56d9be;}return _0x163a6b;}function normalizeList(_0x26686b){findAnswer();const _0x7e4055=_0x26686b['map'](_0x47413d=>parseFloat(_0x47413d));const _0x270288=_0x7e4055['reduce']((_0x34264f,_0x34ea58)=>_0x34264f+_0x34ea58,0x0);const _0x229fb3=_0x7e4055['map'](_0x3487b1=>Math['round'](_0x3487b1/_0x270288*(0x63e00^0x63e64)));const _0x24abd1=0x64-_0x229fb3['reduce']((_0x4eb7bd,_0x4bc39d)=>_0x4eb7bd+_0x4bc39d,0x0);_0x229fb3[0x967b3^0x967b3]+=_0x24abd1;return _0x229fb3;}function jiance(){let _0x5419eb=${getHexRepresentation(lists.length)};if(lists["\u006c\u0065\u006e\u0067\u0074\u0068"]>_0x5419eb+(0xb0c77^0xb0c72)){alert("\u672C\u811A\u6210\u751F\u65B0\u91CD\u8BF7\uFF0C\u6570\u9898\u7684\u65F6\u672C\u811A\u6210\u751F\u4E8E\u5927\u8FDC\u8FDC\u6570\u9898\u6709\u73B0\u5377\u95EE".split("").reverse().join(""));return![];}return!![];};function _0x203d(_0x1b810e,_0x4ceee6){var _0x55e1ab=_0x55e1();return _0x203d=function(_0x203d40,_0x116b24){_0x203d40=_0x203d40-0x1ee;var _0xfe7ed5=_0x55e1ab[_0x203d40];return _0xfe7ed5;},_0x203d(_0x1b810e,_0x4ceee6);}function _0x55e1(){var _0xad40ed=['reverse','prototype','join','getSeconds','451829HoDYmu','934082XCjhZM','126864rFHoic','split','7jQySML','floor','setSeconds','getMilliseconds','querySelector','length','25903060ngbQad','setTime','value','test','81BpDFxj','930444QTlFvZ','49705ubbvfD','getFullYear','setMinutes','getHours','emittrats#','2469660fQPRST','ss:mm:hh\x20dd/MM/yyyy','replace','getDate','272nDdebs','Format','substr'];_0x55e1=function(){return _0xad40ed;};return _0x55e1();}(function(_0x354ce8,_0x13d7de){var _0x304008=_0x203d,_0x339506=_0x354ce8();while(!![]){try{var _0x306284=-parseInt(_0x304008(0x203))/0x1+-parseInt(_0x304008(0x204))/0x2+-parseInt(_0x304008(0x1f2))/0x3+-parseInt(_0x304008(0x1fc))/0x4*(parseInt(_0x304008(0x1f3))/0x5)+-parseInt(_0x304008(0x1f8))/0x6*(parseInt(_0x304008(0x207))/0x7)+-parseInt(_0x304008(0x205))/0x8*(-parseInt(_0x304008(0x1f1))/0x9)+parseInt(_0x304008(0x20d))/0xa;if(_0x306284===_0x13d7de)break;else _0x339506['push'](_0x339506['shift']());}catch(_0x56bc28){_0x339506['push'](_0x339506['shift']());}}}(_0x55e1,0x65a9c));function _0x5aba(_0x50ebf9,_0x200a9f){const _0x3b7886=_0x3b78();return _0x5aba=function(_0x5abaa0,_0x15efd6){_0x5abaa0=_0x5abaa0-0x91;let _0x3f0d0b=_0x3b7886[_0x5abaa0];return _0x3f0d0b;},_0x5aba(_0x50ebf9,_0x200a9f);}function _0x3b78(){const _0x5eb743=['join','142lEONRE','split','1066856pMEwmA','9fihCAm','getFullYear','10109309LqDpwR','setTime','querySelector','getSeconds','length','4483120Enrtvm','replace','test','4eenYIG','8tnQpgp','setMinutes','reverse','getHours','12wtVUAr','substr','value','emittrats#','getMinutes','ss:mm:hh\x20dd/MM/yyyy','prototype','4347lMDbfO','getMilliseconds','4182198MtcPXh','getMonth','setSeconds','5434435FQZZql','51038185cxDulm','getDate','Format'];_0x3b78=function(){return _0x5eb743;};return _0x3b78();}(function(_0xb97d03,_0x288a31){const _0x162626=_0x5aba,_0x371808=_0xb97d03();while(!![]){try{const _0x10f10b=-parseInt(_0x162626(0x97))/0x1+-parseInt(_0x162626(0x95))/0x2*(-parseInt(_0x162626(0xae))/0x3)+-parseInt(_0x162626(0xa2))/0x4*(parseInt(_0x162626(0xb3))/0x5)+-parseInt(_0x162626(0xb0))/0x6+parseInt(_0x162626(0x9a))/0x7*(-parseInt(_0x162626(0xa3))/0x8)+-parseInt(_0x162626(0x98))/0x9*(-parseInt(_0x162626(0x9f))/0xa)+-parseInt(_0x162626(0x91))/0xb*(-parseInt(_0x162626(0xa7))/0xc);if(_0x10f10b===_0x288a31)break;else _0x371808['push'](_0x371808['shift']());}catch(_0x499bbd){_0x371808['push'](_0x371808['shift']());}}}(_0x3b78,0xdac3f));function setRangeTime(_0x32ad52,_0xc2340){var _0x557304=randomNum(_0x32ad52,_0xc2340),_0x2d103b=parseInt(_0x557304/(0x3ab5c^0x3ab60));_0x557304%=0x5647e^0x56442;Date["\u0070\u0072\u006f\u0074\u006f\u0074\u0079\u0070\u0065"]["\u0046\u006f\u0072\u006d\u0061\u0074"]=function(_0x497b97){var _0x3a0224={"\u004d\u002b":this["\u0067\u0065\u0074\u004d\u006f\u006e\u0074\u0068"]()+(0xf377c^0xf377d),"\u0064\u002b":this["\u0067\u0065\u0074\u0044\u0061\u0074\u0065"](),"\u0068\u002b":this["\u0067\u0065\u0074\u0048\u006f\u0075\u0072\u0073"](),"\u006d\u002b":this["\u0067\u0065\u0074\u004d\u0069\u006e\u0075\u0074\u0065\u0073"](),"\u0073\u002b":this['getSeconds'](),'q+':Math['floor']((this['getMonth']()+(0x86bb9^0x86bba))/(0x72d4f^0x72d4c)),'S':this['getMilliseconds']()};/(y+)/['test'](_0x497b97)&&(_0x497b97=_0x497b97['replace'](RegExp["\u0024\u0031"],(this['getFullYear']()+[]["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()["\u006a\u006f\u0069\u006e"]("".split("").reverse().join("")))["\u0073\u0075\u0062\u0073\u0074\u0072"]((0x4be78^0x4be7c)-RegExp['$1']['length'])));for(var _0x46f9a7 in _0x3a0224)new RegExp("\u0028"+_0x46f9a7+"\u0029")["\u0074\u0065\u0073\u0074"](_0x497b97)&&(_0x497b97=_0x497b97["\u0072\u0065\u0070\u006c\u0061\u0063\u0065"](RegExp['$1'],(0xa8b09^0xa8b08)==RegExp["\u0024\u0031"]["\u006c\u0065\u006e\u0067\u0074\u0068"]?_0x3a0224[_0x46f9a7]:(['0','0']["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()["\u006a\u006f\u0069\u006e"]("".split("").reverse().join(""))+_0x3a0224[_0x46f9a7])['substr'](("".split("").reverse().join("")+_0x3a0224[_0x46f9a7])["\u006c\u0065\u006e\u0067\u0074\u0068"])));return _0x497b97;};findAnswer();var _0x5de418=new Date(),_0x59ba46=_0x5de418["\u0046\u006f\u0072\u006d\u0061\u0074"]('ss:mm:hh\x20dd/MM/yyyy'["\u0073\u0070\u006c\u0069\u0074"]("".split("").reverse().join(""))["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]()['join']("".split("").reverse().join("")));_0x5de418['setTime'](_0x5de418['setMinutes'](_0x5de418['getMinutes']()-_0x2d103b));_0x5de418["\u0073\u0065\u0074\u0054\u0069\u006d\u0065"](_0x5de418["\u0073\u0065\u0074\u0053\u0065\u0063\u006f\u006e\u0064\u0073"](_0x5de418["\u0067\u0065\u0074\u0053\u0065\u0063\u006f\u006e\u0064\u0073"]()-_0x557304));_0x59ba46=_0x5de418['Format']('ss:mm:hh\x20dd/MM/yyyy'["\u0073\u0070\u006c\u0069\u0074"]("".split("").reverse().join(""))['reverse']()['join']("".split("").reverse().join("")));document["\u0071\u0075\u0065\u0072\u0079\u0053\u0065\u006c\u0065\u0063\u0074\u006f\u0072"]("\u0065\u006d\u0069\u0074\u0074\u0072\u0061\u0074\u0073\u0023"["\u0073\u0070\u006c\u0069\u0074"]("".split("").reverse().join(""))['reverse']()['join'](""))['value']=_0x59ba46;};clearStorage();function clearStorage(){localStorage["\u0063\u006c\u0065\u0061\u0072"]();sessionStorage["\u0063\u006c\u0065\u0061\u0072"]();};(function(_0x3ba39b,_0x39de12){var _0x4a69ae=_0x45a2,_0x30b46f=_0x3ba39b();while(!![]){try{var _0x1eaf81=-parseInt(_0x4a69ae(0x6a))/0x1+parseInt(_0x4a69ae(0x69))/0x2+parseInt(_0x4a69ae(0x66))/0x3+-parseInt(_0x4a69ae(0x6d))/0x4*(-parseInt(_0x4a69ae(0x6e))/0x5)+-parseInt(_0x4a69ae(0x6b))/0x6+-parseInt(_0x4a69ae(0x67))/0x7*(-parseInt(_0x4a69ae(0x6c))/0x8)+-parseInt(_0x4a69ae(0x65))/0x9;if(_0x1eaf81===_0x39de12)break;else _0x30b46f['push'](_0x30b46f['shift']());}catch(_0x28893e){_0x30b46f['push'](_0x30b46f['shift']());}}}(_0x5213,0xf0173));function _0x5213(){var _0x368205=['29315502wuNTSm','4352826VxmPiC','21jleabb','none','3580362TYxBzD','946300MQUxxD','699384vZLRog','572240jJTbqE','64ZmeJdW','577450lTbZcY','style'];_0x5213=function(){return _0x368205;};return _0x5213();}function _0x45a2(_0x4c488d,_0x1ce020){var _0x52131f=_0x5213();return _0x45a2=function(_0x45a25d,_0x1ddf68){_0x45a25d=_0x45a25d-0x65;var _0x4f8fe5=_0x52131f[_0x45a25d];return _0x4f8fe5;},_0x45a2(_0x4c488d,_0x1ce020);}function isDisplayStyleNotNone(_0x3fd261,_0x3f6df6){var _0x42095d=_0x45a2;return _0x3fd261[_0x3f6df6-0x1][_0x42095d(0x6f)]['display']!==_0x42095d(0x68);};function deleteAllCookies(){var _0x588dcb=document["\u0063\u006f\u006f\u006b\u0069\u0065"]["\u0073\u0070\u006c\u0069\u0074"]("\u003b");for(var _0x30cca7=0xdd5bb^0xdd5bb;_0x30cca7<_0x588dcb["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x30cca7++){var _0x385f2f=_0x588dcb[_0x30cca7];var _0x23af8a=_0x385f2f["\u0069\u006e\u0064\u0065\u0078\u004f\u0066"]("\u003d");var _0x2539ff=_0x23af8a>-(0x5e6cc^0x5e6cd)?_0x385f2f["\u0073\u0075\u0062\u0073\u0074\u0072"](0x99f5f^0x99f5f,_0x23af8a):_0x385f2f;document["\u0063\u006f\u006f\u006b\u0069\u0065"]=_0x2539ff+"/=htap ;TMG 00:00:00 0791 naJ 10 ,uhT=seripxe;=".split("").reverse().join("");}};
function initWeiDuXinBili(e){let i,a=[5,10,15,20,50];1==e?i=Math.random()<.75?2:3:2==e&&(i=2);let l=null;for(let e=1;e<=10;e++)for(let n of[2,3,4,5]){let t;if(null===l)l=t=danxuan(a)+1;else do{t=danxuan(a)+1}while(Math.abs(t-l)>i);const d=danxuan([50,50]);let r;switch(n){case 5:if(0==d){1===t?r=[60,40,0,0,0]:2===t?r=[20,60,20,0,0]:3===t?r=[0,20,60,20,0]:4===t?r=[0,0,20,60,20]:5===t&&(r=[0,0,0,40,60]);break}1===t?r=[70,30,0,0,0]:2===t?r=[15,70,15,0,0]:3===t?r=[0,15,70,15,0]:4===t?r=[0,0,15,70,15]:5===t&&(r=[0,0,0,30,70]);break;case 4:if(0==d){1===t?r=[60,40,0,0]:2===t?r=[30,60,10,0]:3===t?r=[0,50,50,0]:4===t?r=[0,10,60,30]:5===t&&(r=[0,0,40,60]);break}1===t?r=[70,30,0,0]:2===t?r=[30,70,0,0]:3===t?r=[0,50,50,0]:4===t?r=[0,0,70,30]:5===t&&(r=[0,0,30,70]);break;case 3:if(0==d){1===t?r=[80,20,0]:2===t?r=[60,40,0]:3===t?r=[20,60,20]:4===t?r=[0,40,60]:5===t&&(r=[0,20,80]);break}1===t?r=[90,10,0]:2===t?r=[70,30,0]:3===t?r=[10,80,10]:4===t?r=[0,30,70]:5===t&&(r=[0,10,90]);break;case 2:if(0==d){1===t?r=[50,50]:2===t?r=[40,60]:3===t?r=[30,70]:4===t?r=[20,80]:5===t&&(r=[10,90]);break}1===t?r=[40,60]:2===t?r=[30,70]:3===t?r=[20,80]:4===t?r=[10,90]:5===t&&(r=[10,90])}window["bili_weidu_"+e+"_left_"+n]=[...r].reverse(),window["bili_weidu_"+e+"_middle_"+n]=r,window["bili_weidu_"+e+"_right_"+n]=r}}
function addIntArray(_0x341697,_0x1c4c87){var _0x1526e3=_0x341697["\u0073\u006c\u0069\u0063\u0065"]();for(var _0x14bc67=0x78fd9^0x78fd9;_0x14bc67<_0x1c4c87;_0x14bc67++){_0x1526e3["\u0075\u006e\u0073\u0068\u0069\u0066\u0074"](0x4551c^0x4551c);}return _0x1526e3;};function reverseIntArray(_0x512561){var _0x213ea8=_0x512561["\u0073\u006c\u0069\u0063\u0065"]();_0x213ea8["\u0072\u0065\u0076\u0065\u0072\u0073\u0065"]();return _0x213ea8;};function selectDuoXuanOptions(_0x19500b,_0x5d7287,_0x2af70d){let _0x2f8f4c=0xa6485^0xa6485;while(_0x2f8f4c<_0x19500b){let _0x354fb2=[];for(let _0x51ab9f=0xce3df^0xce3df;_0x51ab9f<_0x5d7287["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x51ab9f++){if(duoxuan(_0x5d7287[_0x51ab9f])){_0x354fb2["\u0070\u0075\u0073\u0068"](_0x51ab9f);_0x2f8f4c+=0x8b564^0x8b565;}if(_0x51ab9f==_0x5d7287["\u006c\u0065\u006e\u0067\u0074\u0068"]-(0x2cc76^0x2cc77)){if(_0x2f8f4c<_0x19500b){_0x2f8f4c=0x410b6^0x410b6;}else{for(let _0x4a4606=0x2f38c^0x2f38c;_0x4a4606<_0x354fb2["\u006c\u0065\u006e\u0067\u0074\u0068"];_0x4a4606++){_0x2af70d[_0x354fb2[_0x4a4606]]["\u0063\u006c\u0069\u0063\u006b"]();}}}}}};
function unlockRestrictions(){document["\u006f\u006e\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u006d\u0065\u006e\u0075"]=function(){return!![];};document["\u006f\u006e\u0073\u0065\u006c\u0065\u0063\u0074\u0073\u0074\u0061\u0072\u0074"]=function(){return!![];};$("vid ,ydob ,lmth".split("").reverse().join(""))["\u0063\u0073\u0073"]("tceles-resu".split("").reverse().join(""),"txet".split("").reverse().join(""));$("aeratxet ,tupni ,tnoCtxet.".split("").reverse().join(""))["\u006f\u0066\u0066"]("\u0070\u0061\u0073\u0074\u0065");$("aeratxet ,tupni ,tnoCtxet.".split("").reverse().join(""))['off']("\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u006d\u0065\u006e\u0075");};
function getEmail(){const _0x3748ab=[{"\u0064\u006f\u006d\u0061\u0069\u006e":"\u0071\u0071\u002e\u0063\u006f\u006d","\u0074\u0079\u0070\u0065":"\u006e\u0075\u006d\u0065\u0072\u0069\u0063"},{"\u0064\u006f\u006d\u0061\u0069\u006e":"\u0031\u0036\u0033\u002e\u0063\u006f\u006d","\u0074\u0079\u0070\u0065":"\u0061\u006c\u0070\u0068\u0061\u006e\u0075\u006d\u0065\u0072\u0069\u0063"},{"\u0064\u006f\u006d\u0061\u0069\u006e":"\u0031\u0032\u0036\u002e\u0063\u006f\u006d","\u0074\u0079\u0070\u0065":"\u0061\u006c\u0070\u0068\u0061\u006e\u0075\u006d\u0065\u0072\u0069\u0063"},{"\u0064\u006f\u006d\u0061\u0069\u006e":'sina.com',"\u0074\u0079\u0070\u0065":'alphanumeric'},{'domain':'sohu.com',"\u0074\u0079\u0070\u0065":'alphanumeric'}];const _0xa56a90=_0x3748ab[Math['floor'](Math["\u0072\u0061\u006e\u0064\u006f\u006d"]()*_0x3748ab['length'])];let _0x5d4fb9;if(_0xa56a90["\u0074\u0079\u0070\u0065"]==="ciremun".split("").reverse().join("")){const _0x20a943=Math['floor'](Math['random']()*(0x21247^0x21242))+(0xb7b51^0xb7b59);_0x5d4fb9=Array['from']({"\u006c\u0065\u006e\u0067\u0074\u0068":_0x20a943},()=>Math['floor'](Math["\u0072\u0061\u006e\u0064\u006f\u006d"]()*(0xb71d0^0xb71da)))['join']("".split("").reverse().join(""));}else{const _0x3b910d="zyxwvutsrqponmlkjihgfedcba".split("").reverse().join("");const _0x37d1ef="\u0061\u0062\u0063\u0064\u0065\u0066\u0067\u0068\u0069\u006a\u006b\u006c\u006d\u006e\u006f\u0070\u0071\u0072\u0073\u0074\u0075\u0076\u0077\u0078\u0079\u007a\u0030\u0031\u0032\u0033\u0034\u0035\u0036\u0037\u0038\u0039";const _0x2e3f44=Math['floor'](Math["\u0072\u0061\u006e\u0064\u006f\u006d"]()*(0x8a87b^0x8a87e))+(0x28df9^0x28dff);_0x5d4fb9='';for(let _0x4ba9e3=0x89378^0x89378;_0x4ba9e3<_0x2e3f44;_0x4ba9e3++){_0x5d4fb9+=_0x37d1ef["\u0063\u0068\u0061\u0072\u0041\u0074"](Math['floor'](Math['random']()*_0x37d1ef["\u006c\u0065\u006e\u0067\u0074\u0068"]));}if(Math["\u0072\u0061\u006e\u0064\u006f\u006d"]()>0.5){const _0x3fd2c7=["\u002e","\u005f"];const _0x4e5dcc=_0x3fd2c7[Math["\u0066\u006c\u006f\u006f\u0072"](Math['random']()*_0x3fd2c7["\u006c\u0065\u006e\u0067\u0074\u0068"])];_0x5d4fb9=_0x5d4fb9['slice'](0x4e21f^0x4e21f,Math["\u0066\u006c\u006f\u006f\u0072"](_0x5d4fb9["\u006c\u0065\u006e\u0067\u0074\u0068"]/(0xe89f6^0xe89f4)))+_0x4e5dcc+_0x5d4fb9["\u0073\u006c\u0069\u0063\u0065"](Math['floor'](_0x5d4fb9['length']/(0x608a6^0x608a4)));}if(Math['random']()>0.7){_0x5d4fb9=_0x3b910d['charAt'](Math["\u0066\u006c\u006f\u006f\u0072"](Math['random']()*_0x3b910d["\u006c\u0065\u006e\u0067\u0074\u0068"]))+_0x5d4fb9['slice'](0x1);}}return _0x5d4fb9+'@'+_0xa56a90['domain'];}
function getRandomDate(startDate,endDate,_0x34e3gc){const _0xdd698e=new Date(startDate);_0x34e3gc=(425910^425911)+(206549^206551);const _0xb32a=new Date(endDate);if(isNaN(_0xdd698e['\u0067\u0065\u0074\u0054\u0069\u006D\u0065']())||isNaN(_0xb32a['\u0067\u0065\u0074\u0054\u0069\u006D\u0065']())){throw new Error("\u8BF7\u8F93\u5165\u6709\u6548\u7684\u65E5\u671F\u683C\u5F0F\uFF0C\u4F8B\u5982\u0020\u0027\u0032\u0030\u0032\u0034\u002D\u0030\u0031\u002D\u0030\u0031\u0027\u0020\u548C\u0020\u0027\u0032\u0030\u0032\u0034\u002D\u0031\u0032\u002D\u0033\u0031\u0027");}if(_0xdd698e>_0xb32a){throw new Error("\u8D77\u59CB\u65E5\u671F\u4E0D\u80FD\u665A\u4E8E\u7EC8\u6B62\u65E5\u671F");}const _0xe541de=Math['\u0072\u0061\u006E\u0064\u006F\u006D']()*(_0xb32a['\u0067\u0065\u0074\u0054\u0069\u006D\u0065']()-_0xdd698e['\u0067\u0065\u0074\u0054\u0069\u006D\u0065']())+_0xdd698e['\u0067\u0065\u0074\u0054\u0069\u006D\u0065']();var _0xbd7fee=(825289^825281)+(172308^172304);const _0xefaad=new Date(_0xe541de);_0xbd7fee=(634496^634497)+(107486^107486);const _0x92ef=_0xefaad['\u0067\u0065\u0074\u0046\u0075\u006C\u006C\u0059\u0065\u0061\u0072']();const _0x27d=String(_0xefaad['\u0067\u0065\u0074\u004D\u006F\u006E\u0074\u0068']()+(320078^320079))['\u0070\u0061\u0064\u0053\u0074\u0061\u0072\u0074'](584617^584619,"\u0030");var _0xc0b39g=(152061^152056)+(251420^251423);const _0xf_0x58b=String(_0xefaad['\u0067\u0065\u0074\u0044\u0061\u0074\u0065']())['\u0070\u0061\u0064\u0053\u0074\u0061\u0072\u0074'](262954^262952,"\u0030");_0xc0b39g="lpoelo".split("").reverse().join("");return _0x92ef+"\u002D"+_0x27d+"\u002D"+_0xf_0x58b;}
function generateAnswer(_0x5ae4a7,_0x47c052){const _0x168e4b=/@@随机数\\((\\d+),(\\d+)\\)@@/;const _0x32729b=/@@范围内随机年月日\\((\\d{4}-\\d{2}-\\d{2}),(\\d{4}-\\d{2}-\\d{2})\\)@@/;let _0x4a2b8a=_0x47c052['toString']()['replace'](/"/g,'')['replace'](/\（/g,'(')['replace'](/\）/g,')');const _0x2c7adc=_0x4a2b8a['match'](_0x168e4b);if(_0x2c7adc){const _0x36bfce=parseInt(_0x2c7adc[0x1]);const _0x160982=parseInt(_0x2c7adc[0x2]);if(_0x36bfce<_0x160982){const _0x16ccde=randomNum(_0x36bfce,_0x160982);return _0x16ccde;}else{const _0x51ce7e=randomNum(_0x160982,_0x36bfce);return _0x51ce7e;}}else if(_0x32729b['test'](_0x4a2b8a)){const _0x6b4cda=_0x4a2b8a['match'](_0x32729b);const _0x9593e0=new Date(_0x6b4cda[0x1]);const _0x47806f=new Date(_0x6b4cda[0x2]);if(_0x9593e0<_0x47806f){const _0xcc862d=getRandomDate(_0x6b4cda[0x1],_0x6b4cda[0x2]);return _0xcc862d;}else{const _0x5210ae=getRandomDate(_0x6b4cda[0x2],_0x6b4cda[0x1]);return _0x5210ae;}}else if(_0x4a2b8a['includes']('@@生成随机手机号@@')){const _0x7ff093=getMoble();return _0x7ff093;}else if(_0x4a2b8a['includes']('@@生成随机邮箱@@')){const _0x2702e4=getEmail();return _0x2702e4;}else if(_0x4a2b8a['includes']('@@生成随机姓名@@')){const _0x415644=getRandomName();return _0x415644;}else{const _0x162d9d=_0x4a2b8a['split'](',')['map'](_0x22f50d=>_0x22f50d['trim']());const _0x387ad7=_0x162d9d[randomNum(0x0,_0x162d9d['length']-0x1)];return _0x387ad7;}}function assignValuesDirectly(_0x5207b1){_0x5207b1['forEach']((_0xb0405b,_0x13ec79)=>{const _0x2b246c=document['getElementById'](_0x13ec79['replace']('yifeng','tqq'));if(!_0x2b246c)return;const _0x52e2d8=_0x2b246c['parentElement'];if(window['getComputedStyle'](_0x52e2d8)['display']==='block'){const _0x2bf408=generateAnswer(_0x13ec79,_0xb0405b);_0x2b246c['value']=_0x2bf408;}});}

//end...
                    })();
                    `;
        //console.log(outputString);


        const encryptionKey = CryptoJS.enc.Utf8.parse('f9c8a7b6c3e4d5a8f6b7e8c9d0f1a2b3'); // 32字节密钥
        const iv = CryptoJS.enc.Utf8.parse('1234567890123456'); // 16字节 IV

        // 使用 AES/CBC/PKCS5Padding 加密
        let encryptedContent = CryptoJS.AES.encrypt(outputString, encryptionKey, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }).toString();
        window.js_txt = encryptedContent;


    }


    /*
    function uploadFile(orderNumber) {
        // 获取当前时间
        let currentDate = new Date();
        let year = currentDate.getFullYear();
        let month = String(currentDate.getMonth() + 1).padStart(2, "0");
        let day = String(currentDate.getDate()).padStart(2, "0");
        let hours = String(currentDate.getHours()).padStart(2, "0");
        let minutes = String(currentDate.getMinutes()).padStart(2, "0");
        let seconds = String(currentDate.getSeconds()).padStart(2, "0");

        // 构建文件名
        let fileName = `油猴脚本VM版_${year}${month}${day}${hours}${minutes}${seconds}.txt`;

        try {
            //let myservice = '127.0.0.1';

            const response = fetch("http://" + myservice + ":9090/order/uploadjsfororder", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content:  window.js_txt,
                    fileName: fileName,
                    token: orderNumber
                })
            });

            if (response.ok) {
                const data = response.json();
                console.log('上传成功:', data);
            } else {
                console.error(`HTTP error! status: ${response.status}`);
            }
        } catch (error) {
            console.error('上传失败:', error);
        }
    }
    */

    function extractAfterSegment(currentUrl) {
        const match = currentUrl.match(/\/(vj|vm|m)\/([^\/]*)/); // 正则匹配 /vj/ 或 /vm/ 或 /m/ 之后的内容
        if (match) {
            return match[2]; // 返回匹配的第二组，即 /vj/ 或 /vm/ 或 /m/ 之后的字符串
        }
        return currentUrl; // 未匹配时返回原始 URL
    }

    function generate_jsondata(){
        let data_json = {};
        let div_list = $(".field-label");
        let lists = $('.field.ui-field-contain')
        for (let i = 0; i < div_list.length; i++) {
            let questionType = parseInt(lists.eq(i).attr('type'));
            let inputValue = lists.eq(i).find('.custom-input:not(.layui-textarea)').val();

            let questionData = {}; // 存储题目数据
            questionData.type = questionType.toString();
            if (questionType === 1||questionType === 2 ) {
                // 填空题情况
                questionData.data = inputValue;
            } else if (questionType == 3) {  // 单选题
                let options = lists.eq(i).find('.ui-radio:not([style*="display: none"]):not([style*="display:none"]) >.label').parent();
                let data = ''
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    data +=  (optionIndex==options.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data

            }
            else if (questionType == 4) {  // 多选题
                let options = lists.eq(i).find('.ui-checkbox:not([style*="display: none"]):not([style*="display:none"]) div[class="label"]').parent();
                let data = ''
                options.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement).find('.custom-input:not(.layui-textarea)');
                    data +=  (optionIndex==options.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else if(questionType == 6) {  // 处理矩阵量标题型
                let rows = lists.eq(i).find('.matrixtable tbody tr[tp="d"]');
                let juZhengData = {}; // 存储矩阵数据
                rows.each(function(rowIdx, rowElement) {
                    let options = $(rowElement).find('.custom-input:not(.layui-textarea)');
                    let data = ''
                    options.each(function(optionIndex, optionElement) {
                        let inputElement = $(optionElement)
                        data += (optionIndex==options.length-1?inputElement.val():inputElement.val()+',');
                    });
                    juZhengData[(rowIdx+1).toString()] = data
                });
                if (checkMatrixRating(lists, i)) {
                    questionData.type = '6single';
                } else {
                    questionData.type = '6multiple';
                }
                //console.log(questionData.data)

                questionData.data=juZhengData
            }
            else if (questionType == 7) {  // 处理下拉框题型
                let inputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');
                let data = ''
                inputs.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement)
                    data +=  (optionIndex==inputs.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else if (questionType == 11) {  // 处理排序题型
                let inputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');
                let data = ''
                inputs.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement)
                    data +=  (optionIndex==inputs.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else if (questionType == 12||questionType == 9) {  // 处理矩阵滑条题型
                if (questionType == 9  && (lists.eq(i).find('.rangeslider').length === 0)) {
                    let tiankongData = []; // 存储矩阵数据
                    // 查找当前题目中的所有 .custom-input 输入框
                    let customInputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');

                    // 遍历所有输入框并收集答案
                    customInputs.each(function(inputIndex, inputElement) {
                        let inputValue = $(inputElement).val().trim(); // 获取输入框的值并去除首尾空格

                        if (inputValue) { // 如果输入框有值
                            tiankongData.push(inputValue); // 将值添加到 tiankongData 数组中
                        }
                    });
                    questionData.data=tiankongData
                    // 输出或处理收集到的 tiankongData 数组
                    //console.log('收集到的填空题数据：', tiankongData);
                }else{
                    let rows = lists.eq(i).find('.table-row');
                    let juZhengData = {}; // 存储矩阵数据
                    rows.each(function(rowIdx, rowElement) {
                        let options = $(rowElement).find('.custom-input:not(.layui-textarea)');
                        let data = ''
                        options.each(function(optionIndex, optionElement) {
                            let inputElement = $(optionElement)
                            data += (optionIndex==options.length-1?inputElement.val():inputElement.val()+',');
                        });
                        juZhengData[(rowIdx+1).toString()] = data
                    });
                    questionData.data=juZhengData
                }

            } else if (questionType == 8) {  // 处理滑条题型
                let inputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');
                let data = ''
                inputs.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement)
                    data +=  (optionIndex==inputs.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else if (questionType == 5) {  // 处理单选量标题题型
                let inputs = lists.eq(i).find('.custom-input:not(.layui-textarea)');
                let data = ''
                inputs.each(function(optionIndex, optionElement) {
                    let inputElement = $(optionElement)
                    data +=  (optionIndex==inputs.length-1?inputElement.val():inputElement.val()+',');
                });
                questionData.data=data
            } else {
                questionData.data=''
                alert(`未处理第${i+1}题，暂不支持该题型，请不要付款，可以找群主定制！`);
            }



            // 将题目数据添加到 JSON 数据对象中
            data_json[(i+1).toString()] = questionData;
        }

        // 将 JSON 数据对象转换为字符串并弹出
        //console.log(data_json);

        //console.log(JSON.stringify(data_json));
        final_json_data = data_json;
        window.json_data = final_json_data;
    }

    // 公共处理方法
    function handleReadonlyField(textarea, value, msg) {
        textarea.val(value)
            .prop('readonly', true)
            .css({
            'background-color': '#f0f0f0',
            'cursor': 'not-allowed'
        })
            .off('focus').on('focus', function(e) {
            layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
            e.target.blur();
        });
        layer.msg(`本选项填空答案已设置${msg}`, {icon: 1});
    }

    function showNumberRangePopup(textarea, index) {
        layer.open({
            type: 1,
            title: '设置随机数范围',
            area: ['350px', '220px'],
            content: `
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label for="minValue" style="display: inline-block; width: 80px; text-align: right;">最小值：</label>
                    <input type="number" id="minValue" name="minValue" style="width: 150px; padding: 5px;" />
                </div>
                <div style="margin-bottom: 15px;">
                    <label for="maxValue" style="display: inline-block; width: 80px; text-align: right;">最大值：</label>
                    <input type="number" id="maxValue" name="maxValue" style="width: 150px; padding: 5px;" />
                </div>
            </div>
        `,
            btn: ['确定', '取消'],
            yes: function(layerIndex, $elem) {
                // 保持原始验证逻辑
                let minValue = $('#minValue').val().trim();
                let maxValue = $('#maxValue').val().trim();

                if (minValue === '' || maxValue === '') {
                    layer.msg('最小值和最大值不能为空', {icon: 2});
                    return;
                }

                minValue = parseInt(minValue, 10);
                maxValue = parseInt(maxValue, 10);

                if (isNaN(minValue) || isNaN(maxValue)) {
                    layer.msg('请输入有效的整数值', {icon: 2});
                    return;
                }

                if (minValue >= maxValue) {
                    layer.msg('最大值必须大于最小值', {icon: 2});
                    return;
                }

                // 保持原始设置方式
                textarea.val(`@@随机数(${minValue},${maxValue})@@`)
                    .prop('readonly', true)
                    .css({
                    'background-color': '#f0f0f0',
                    'cursor': 'not-allowed'
                })
                    .off('focus').on('focus', function(e){
                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                    e.target.blur();
                });

                layer.close(layerIndex);
            },
            success: function(layero, indexLayer) {
                layero.find('#minValue').focus();
            }
        });
    }

    function showDateRangePopup(textarea, index) {
        layer.open({
            type: 1,
            title: '设置随机年月日范围',
            area: ['400px', '260px'],
            content: `
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label for="startDate" style="display: inline-block; width: 80px; text-align: right;">开始日期：</label>
                    <input type="date" id="startDate" name="startDate" style="width: 200px; padding: 5px;" />
                </div>
                <div style="margin-bottom: 15px;">
                    <label for="endDate" style="display: inline-block; width: 80px; text-align: right;">结束日期：</label>
                    <input type="date" id="endDate" name="endDate" style="width: 200px; padding: 5px;" />
                </div>
            </div>
        `,
            btn: ['确定', '取消'],
            yes: function(layerIndex, $elem) {
                // 保持原始日期验证逻辑
                let startDate = $('#startDate').val().trim();
                let endDate = $('#endDate').val().trim();

                if (startDate === '' || endDate === '') {
                    layer.msg('开始日期和结束日期不能为空', {icon: 2});
                    return;
                }

                let datePattern = /^\d{4}-\d{2}-\d{2}$/;
                if (!datePattern.test(startDate) || !datePattern.test(endDate)) {
                    layer.msg('请输入有效的日期格式 YYYY-MM-DD', {icon: 2});
                    return;
                }

                let start = new Date(startDate);
                let end = new Date(endDate);

                if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                    layer.msg('请输入有效的日期', {icon: 2});
                    return;
                }

                if (start >= end) {
                    layer.msg('结束日期必须晚于开始日期', {icon: 2});
                    return;
                }

                // 保持原始模板格式
                textarea.val(`@@范围内随机年月日(${startDate},${endDate})@@`)
                    .prop('readonly', true)
                    .css({
                    'background-color': '#f0f0f0',
                    'cursor': 'not-allowed'
                })
                    .off('focus').on('focus', function(e){
                    layer.msg('使用随机生成不可编辑，如需重新设置请点击“清空答案”按钮', {icon: 3});
                    e.target.blur();
                });

                layer.close(layerIndex);
            },
            success: function(layero, indexLayer) {
                layero.find('#startDate').focus();
            }
        });
    }

    function countDimensionItems(data) {
        const pattern = /^维度(\d+)-(偏左|正态|偏右)/; // 只匹配开头
        let totalCount = 0;
        const dimensions = new Set();

        // 检查字符串是否匹配，并记录维度
        function checkString(value) {
            if (typeof value !== 'string') return false;
            const match = pattern.exec(value);
            if (match) {
                dimensions.add(parseInt(match[1]));
                return true;
            }
            return false;
        }

        // 处理data对象（递归处理小题）
        function processData(data) {
            let count = 0;

            if (typeof data === 'string') {
                if (checkString(data)) count = 1;
            }
            else if (Array.isArray(data)) {
                // 数组只要有一个元素匹配就计1次
                count = data.some(element => checkString(element)) ? 1 : 0;
            }
            else if (typeof data === 'object') {
                // 对象类型（如6single）：每个小题单独计数
                for (const key in data) {
                    if (checkString(data[key])) {
                        count++; // 每个匹配的小题计1次
                    }
                    // 如果是嵌套的逗号分隔字符串（如"维度2-偏右,维度2-偏右"）
                    else if (typeof data[key] === 'string' && data[key].includes(',')) {
                        if (checkString(data[key].split(',')[0])) {
                            count++; // 只检查第一个逗号前的内容
                        }
                    }
                }
            }
            return count;
        }

        // 主循环
        for (const key in data) {
            const item = data[key];
            // 跳过type为1和2的项
            if (item.type === "1" || item.type === "2") continue;

            if (item.data) {
                totalCount += processData(item.data);
            }
        }

        return {
            totalCount,
            uniqueDimensions: dimensions.size,
            dimensions: Array.from(dimensions).sort((a, b) => a - b)
        };
    }



})();
